<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('menu_item_variants', function (Blueprint $table) {
            $table->string('name_en', 100)->nullable()->after('name');
            $table->string('type', 50)->nullable()->after('code');
            $table->boolean('is_active')->nullable()->default(true)->after('is_default');
            $table->string('color_code', 7)->nullable()->after('sort_order');
            $table->string('image')->nullable()->after('color_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_item_variants', function (Blueprint $table) {
            $table->dropColumn(['name_en', 'type', 'is_active', 'color_code', 'image']);
        });
    }
};