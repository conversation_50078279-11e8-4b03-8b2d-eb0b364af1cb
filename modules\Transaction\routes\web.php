<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Transaction Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Transaction module.
| These routes are loaded by the TransactionServiceProvider within a group
| which is assigned the "web" middleware group.
|
*/

Route::prefix('transactions')->name('transactions.')->group(function () {
    // Transaction management routes
    Route::get('/', function () {
        return view('transaction::transactions.index');
    })->name('index');
    
    Route::get('/create', function () {
        return view('transaction::transactions.create');
    })->name('create');
    
    Route::get('/{id}', function ($id) {
        return view('transaction::transactions.show', compact('id'));
    })->name('show');
    
    Route::get('/{id}/edit', function ($id) {
        return view('transaction::transactions.edit', compact('id'));
    })->name('edit');
});

Route::prefix('payments')->name('payments.')->group(function () {
    // Payment management routes
    Route::get('/', function () {
        return view('transaction::payments.index');
    })->name('index');
    
    Route::get('/create', function () {
        return view('transaction::payments.create');
    })->name('create');
    
    Route::get('/{id}', function ($id) {
        return view('transaction::payments.show', compact('id'));
    })->name('show');
});
