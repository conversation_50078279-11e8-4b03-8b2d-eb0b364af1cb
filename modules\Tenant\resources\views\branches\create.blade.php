@extends('layouts.app')

@section('title', 'إضافة فرع جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة فرع جديد</h3>
                    <div class="card-tools">
                        <a href="{{ route('branches.index') }}" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                
                <form action="{{ route('branches.store') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>
                                
                                <div class="form-group">
                                    <label for="tenant_id">المستأجر <span class="text-danger">*</span></label>
                                    <select name="tenant_id" id="tenant_id" class="form-control @error('tenant_id') is-invalid @enderror" required>
                                        <option value="">اختر المستأجر</option>
                                        @foreach($tenants as $tenant)
                                            <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                                {{ $tenant->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('tenant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="name">اسم الفرع <span class="text-danger">*</span></label>
                                    <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="code">كود الفرع</label>
                                    <input type="text" name="code" id="code" class="form-control @error('code') is-invalid @enderror" 
                                           value="{{ old('code') }}" placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="address">العنوان <span class="text-danger">*</span></label>
                                    <textarea name="address" id="address" class="form-control @error('address') is-invalid @enderror" 
                                              rows="3" required>{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات الاتصال</h5>
                                
                                <div class="form-group">
                                    <label for="phone">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror" 
                                           value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="email">البريد الإلكتروني</label>
                                    <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" 
                                           value="{{ old('email') }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="manager_name">اسم مدير الفرع <span class="text-danger">*</span></label>
                                    <input type="text" name="manager_name" id="manager_name" class="form-control @error('manager_name') is-invalid @enderror" 
                                           value="{{ old('manager_name') }}" required>
                                    @error('manager_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="timezone">المنطقة الزمنية <span class="text-danger">*</span></label>
                                    <select name="timezone" id="timezone" class="form-control @error('timezone') is-invalid @enderror" required>
                                        <option value="">اختر المنطقة الزمنية</option>
                                        <option value="Asia/Riyadh" {{ old('timezone') == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai" {{ old('timezone') == 'Asia/Dubai' ? 'selected' : '' }}>دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait" {{ old('timezone') == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (GMT+3)</option>
                                        <option value="Asia/Qatar" {{ old('timezone') == 'Asia/Qatar' ? 'selected' : '' }}>قطر (GMT+3)</option>
                                        <option value="Asia/Bahrain" {{ old('timezone') == 'Asia/Bahrain' ? 'selected' : '' }}>البحرين (GMT+3)</option>
                                    </select>
                                    @error('timezone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <!-- Branch Settings -->
                            <div class="col-md-6">
                                <h5 class="mb-3">إعدادات الفرع</h5>
                                
                                <div class="form-group">
                                    <label for="seating_capacity">سعة الجلوس</label>
                                    <input type="number" name="seating_capacity" id="seating_capacity" 
                                           class="form-control @error('seating_capacity') is-invalid @enderror" 
                                           value="{{ old('seating_capacity') }}" min="1">
                                    @error('seating_capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="delivery_radius">نطاق التوصيل (كم)</label>
                                    <input type="number" name="delivery_radius" id="delivery_radius" 
                                           class="form-control @error('delivery_radius') is-invalid @enderror" 
                                           value="{{ old('delivery_radius') }}" min="0" step="0.1">
                                    @error('delivery_radius')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Services -->
                            <div class="col-md-6">
                                <h5 class="mb-3">الخدمات المتاحة</h5>
                                
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="is_dine_in_enabled" id="is_dine_in_enabled" 
                                               class="custom-control-input" value="1" 
                                               {{ old('is_dine_in_enabled') ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_dine_in_enabled">
                                            تناول في المكان
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="is_takeaway_enabled" id="is_takeaway_enabled" 
                                               class="custom-control-input" value="1" 
                                               {{ old('is_takeaway_enabled') ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_takeaway_enabled">
                                            استلام
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="is_delivery_enabled" id="is_delivery_enabled" 
                                               class="custom-control-input" value="1" 
                                               {{ old('is_delivery_enabled') ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_delivery_enabled">
                                            توصيل
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="is_online_ordering_enabled" id="is_online_ordering_enabled" 
                                               class="custom-control-input" value="1" 
                                               {{ old('is_online_ordering_enabled') ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_online_ordering_enabled">
                                            طلب أونلاين
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> حفظ
                        </button>
                        <a href="{{ route('branches.index') }}" class="btn btn-secondary">
                            <i class="fa fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection