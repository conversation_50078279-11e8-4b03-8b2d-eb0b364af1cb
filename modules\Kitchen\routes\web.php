<?php

use Illuminate\Support\Facades\Route;
use Modules\Kitchen\Http\Controllers\KitchenController;
use Modules\Kitchen\Http\Controllers\KotOrderController;

/*
|--------------------------------------------------------------------------
| Kitchen Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Kitchen module.
| These routes are loaded by the KitchenServiceProvider within a group
| which is assigned the "web" middleware group.
|
*/

Route::middleware(['auth', 'web'])->group(function () {
    
    // Kitchen Management Routes
    Route::prefix('kitchens')->name('kitchens.')->group(function () {
        Route::get('/', [KitchenController::class, 'index'])->name('index');
        Route::get('/create', [KitchenController::class, 'create'])->name('create');
        Route::post('/', [KitchenController::class, 'store'])->name('store');
        Route::get('/{kitchen}', [KitchenController::class, 'show'])->name('show');
        Route::get('/{kitchen}/edit', [KitchenController::class, 'edit'])->name('edit');
        Route::put('/{kitchen}', [KitchenController::class, 'update'])->name('update');
        Route::delete('/{kitchen}', [KitchenController::class, 'destroy'])->name('destroy');
        
        // Kitchen Menu Item Management
        Route::get('/{kitchen}/menu-items', [KitchenController::class, 'menuItems'])->name('menu-items');
        Route::post('/{kitchen}/menu-items', [KitchenController::class, 'assignMenuItem'])->name('assign-menu-item');
        Route::delete('/{kitchen}/menu-items/{menuItem}', [KitchenController::class, 'removeMenuItem'])->name('remove-menu-item');
        Route::get('/{kitchen}/available-menu-items', [KitchenController::class, 'availableMenuItems'])->name('available-menu-items');
    });

    // KOT Order Management Routes
    Route::prefix('kot-orders')->name('kot-orders.')->group(function () {
        Route::get('/', [KotOrderController::class, 'index'])->name('index');
        Route::get('/{kotOrder}', [KotOrderController::class, 'show'])->name('show');
        Route::patch('/{kotOrder}/status', [KotOrderController::class, 'updateStatus'])->name('update-status');
        Route::patch('/{kotOrder}/priority', [KotOrderController::class, 'updatePriority'])->name('update-priority');
        Route::patch('/{kotOrder}/assign', [KotOrderController::class, 'assign'])->name('assign');
        Route::get('/{kotOrder}/print', [KotOrderController::class, 'print'])->name('print');
        Route::get('/statistics', [KotOrderController::class, 'statistics'])->name('statistics');
        Route::get('/active', [KotOrderController::class, 'activeKots'])->name('active');
    });

    // Kitchen Display Routes
    Route::prefix('kitchen-display')->name('kitchen-display.')->group(function () {
        Route::get('/', [KotOrderController::class, 'kitchenDisplay'])->name('index');
        Route::get('/{kitchen}', [KotOrderController::class, 'kitchenDisplay'])->name('kitchen');
    });
});
