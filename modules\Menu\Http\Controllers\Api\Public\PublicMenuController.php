<?php

namespace Modules\Menu\Http\Controllers\Api\Public;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Menu\Http\Resources\MenuResource;
use Modules\Menu\Services\MenuService;
use App\Models\Tenant;

class PublicMenuController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Get restaurant information by tenant name
     */
    public function getRestaurantInfo($tenantName)
    {
        $tenant = Tenant::where('name', $tenantName)->first();
        
        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }
        
        return response()->json([
            'id' => $tenant->id,
            'name' => $tenant->name,
            'description' => $tenant->description,
            'logo' => $tenant->logo,
            'contact_info' => $tenant->contact_info,
        ]);
    }

    /**
     * Get menus for a specific restaurant (tenant)
     */
    public function getMenus($tenantName, Request $request)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        // Get the default branch for this tenant or first available branch
        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        $menus = $this->menuService->getActiveMenusForBranch($branch->id);

        return MenuResource::collection($menus);
    }

    /**
     * Get specific menu for a restaurant
     */
    public function getMenu($tenantName, $menuId)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        $menu = $this->menuService->getMenuByIdForBranch($menuId, $branch->id);

        if (!$menu) {
            return response()->json(['error' => 'Menu not found'], 404);
        }

        return new MenuResource($menu);
    }

    /**
     * Get categories for a restaurant
     */
    public function getCategories($tenantName, Request $request)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        // This will be implemented when we refactor CategoryService
        return response()->json(['message' => 'Categories endpoint will be implemented']);
    }

    /**
     * Get menu items for a restaurant
     */
    public function getMenuItems($tenantName, Request $request)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        // This will be implemented when we refactor MenuItemService
        return response()->json(['message' => 'Menu items endpoint will be implemented']);
    }

    /**
     * Get addons for a restaurant
     */
    public function getAddons($tenantName, Request $request)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        // This will be implemented when we refactor AddonService
        return response()->json(['message' => 'Addons endpoint will be implemented']);
    }

    /**
     * Get variants for a restaurant
     */
    public function getVariants($tenantName, Request $request)
    {
        $tenant = Tenant::with('branches')->where('name', $tenantName)->first();

        if (!$tenant) {
            return response()->json(['error' => 'Restaurant not found'], 404);
        }

        $branch = $tenant->branches->where('is_default', true)->first()
                 ?? $tenant->branches->first();

        if (!$branch) {
            return response()->json(['error' => 'No branches found for this restaurant'], 404);
        }

        // This will be implemented when we refactor VariantService
        return response()->json(['message' => 'Variants endpoint will be implemented']);
    }
}