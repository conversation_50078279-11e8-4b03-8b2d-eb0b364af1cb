<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use App\Models\LoyaltyProgram;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class LoyaltyService
{
    /**
     * Add loyalty points to customer
     */
    public function addPoints(int $customerId, float $points, string $description = null, int $processedBy = null): Customer
    {
        DB::beginTransaction();
        
        try {
            $customer = Customer::findOrFail($customerId);
            
            // Create loyalty transaction
            LoyaltyTransaction::create([
                'customer_id' => $customerId,
                'transaction_type' => 'earned',
                'points' => $points,
                'description' => $description ?? 'Points earned',
                'processed_by' => $processedBy,
            ]);

            // Update customer's total loyalty points
            $customer->increment('loyalty_points', $points);
            
            DB::commit();
            return $customer->fresh();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemPoints(int $customerId, float $points, string $description = null, int $processedBy = null): Customer
    {
        DB::beginTransaction();
        
        try {
            $customer = Customer::findOrFail($customerId);
            
            if ($customer->loyalty_points < $points) {
                throw new \Exception('Insufficient loyalty points. Available: ' . $customer->loyalty_points . ', Requested: ' . $points);
            }

            // Create loyalty transaction
            LoyaltyTransaction::create([
                'customer_id' => $customerId,
                'transaction_type' => 'redeemed',
                'points' => $points,
                'description' => $description ?? 'Points redeemed',
                'processed_by' => $processedBy,
            ]);

            // Update customer's total loyalty points
            $customer->decrement('loyalty_points', $points);
            
            DB::commit();
            return $customer->fresh();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Adjust loyalty points (for corrections)
     */
    public function adjustPoints(int $customerId, float $points, string $description, int $processedBy = null): Customer
    {
        DB::beginTransaction();
        
        try {
            $customer = Customer::findOrFail($customerId);
            
            // Create loyalty transaction
            LoyaltyTransaction::create([
                'customer_id' => $customerId,
                'transaction_type' => 'adjusted',
                'points' => $points,
                'description' => $description,
                'processed_by' => $processedBy,
            ]);

            // Update customer's total loyalty points
            if ($points > 0) {
                $customer->increment('loyalty_points', $points);
            } else {
                $customer->decrement('loyalty_points', abs($points));
            }
            
            DB::commit();
            return $customer->fresh();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Expire loyalty points
     */
    public function expirePoints(int $customerId, float $points, string $description = null): Customer
    {
        DB::beginTransaction();
        
        try {
            $customer = Customer::findOrFail($customerId);
            
            // Create loyalty transaction
            LoyaltyTransaction::create([
                'customer_id' => $customerId,
                'transaction_type' => 'expired',
                'points' => $points,
                'description' => $description ?? 'Points expired',
            ]);

            // Update customer's total loyalty points
            $customer->decrement('loyalty_points', $points);
            
            DB::commit();
            return $customer->fresh();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get customer's loyalty transaction history
     */
    public function getLoyaltyHistory(int $customerId, int $limit = null): Collection
    {
        $query = LoyaltyTransaction::where('customer_id', $customerId)
            ->with(['processedBy'])
            ->orderBy('created_at', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    /**
     * Calculate points earned from order amount
     */
    public function calculatePointsFromOrder(float $orderAmount, int $tenantId = null): float
    {
        $program = LoyaltyProgram::where('tenant_id', $tenantId)
            ->active()
            ->first();
            
        if (!$program) {
            return 0;
        }
        
        return $program->calculatePointsEarned($orderAmount);
    }

    /**
     * Calculate redemption value of points
     */
    public function calculateRedemptionValue(float $points, int $tenantId = null): float
    {
        $program = LoyaltyProgram::where('tenant_id', $tenantId)
            ->active()
            ->first();
            
        if (!$program) {
            return 0;
        }
        
        return $program->calculateRedemptionValue($points);
    }

    /**
     * Get customer loyalty statistics
     */
    public function getCustomerLoyaltyStats(int $customerId): array
    {
        $customer = Customer::findOrFail($customerId);
        
        $totalEarned = LoyaltyTransaction::where('customer_id', $customerId)
            ->where('transaction_type', 'earned')
            ->sum('points');
            
        $totalRedeemed = LoyaltyTransaction::where('customer_id', $customerId)
            ->where('transaction_type', 'redeemed')
            ->sum('points');
            
        $totalExpired = LoyaltyTransaction::where('customer_id', $customerId)
            ->where('transaction_type', 'expired')
            ->sum('points');
            
        $totalAdjusted = LoyaltyTransaction::where('customer_id', $customerId)
            ->where('transaction_type', 'adjusted')
            ->sum('points');
        
        return [
            'current_balance' => $customer->loyalty_points,
            'total_earned' => $totalEarned,
            'total_redeemed' => $totalRedeemed,
            'total_expired' => $totalExpired,
            'total_adjusted' => $totalAdjusted,
            'lifetime_points' => $totalEarned + $totalAdjusted,
            'transaction_count' => LoyaltyTransaction::where('customer_id', $customerId)->count(),
        ];
    }

    /**
     * Get loyalty program for tenant
     */
    public function getLoyaltyProgram(int $tenantId): ?LoyaltyProgram
    {
        return LoyaltyProgram::where('tenant_id', $tenantId)
            ->active()
            ->first();
    }

    /**
     * Check if customer can redeem points
     */
    public function canRedeemPoints(int $customerId, float $points): bool
    {
        $customer = Customer::findOrFail($customerId);
        return $customer->loyalty_points >= $points && $points > 0;
    }

    /**
     * Get customers with highest loyalty points
     */
    public function getTopLoyaltyCustomers(int $tenantId, int $limit = 10): Collection
    {
        return Customer::where('tenant_id', $tenantId)
            ->where('loyalty_points', '>', 0)
            ->orderBy('loyalty_points', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Process loyalty points from order
     */
    public function processOrderLoyalty(int $customerId, float $orderAmount, int $orderId = null, int $tenantId = null): ?LoyaltyTransaction
    {
        $points = $this->calculatePointsFromOrder($orderAmount, $tenantId);
        
        if ($points <= 0) {
            return null;
        }
        
        return LoyaltyTransaction::create([
            'customer_id' => $customerId,
            'order_id' => $orderId,
            'transaction_type' => 'earned',
            'points' => $points,
            'description' => 'Points earned from order #' . $orderId,
        ]);
    }
}
