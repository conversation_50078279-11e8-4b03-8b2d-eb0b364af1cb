// ... existing code ...
        <!-- Public Page -->
        <li class="menu-item">
            <a href="/menu/restaurant/<?php echo e($tenant_name ?? 'restaurant'); ?>" class="menu-link" target="_blank">
                <i class="fas fa-globe menu-icon"></i>
                <span class="menu-text">🌐 صفحة المطعم العامة</span>
            </a>
        </li>
// ... existing code ...
<!-- Modern Sidebar -->
<aside class="sidebar" id="sidebar">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: #1e293b;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            transform: translateX(0);
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: #0f172a;
        }

        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .logo:hover {
            color: #60a5fa;
            text-decoration: none;
        }

        .logo i {
            font-size: 28px;
        }

        .user-profile-sidebar {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: #0f172a;
        }

        .user-avatar-sidebar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .user-name {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 16px;
        }

        .user-role {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin: 5px 0 0 0;
        }

        .sidebar-menu {
            padding: 20px 0;
            list-style: none;
            margin: 0;
        }

        .menu-category {
            color: rgba(255, 255, 255, 0.5);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 15px 20px 10px;
            margin-top: 20px;
        }

        .menu-category:first-child {
            margin-top: 0;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .menu-link:hover {
            color: white;
            background: #334155;
            text-decoration: none;
        }

        .menu-link.active {
            color: white;
            background: #3b82f6;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #60a5fa;
        }

        .menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .menu-text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .menu-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .submenu {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #0f172a;
        }

        .submenu.active {
            max-height: 500px;
        }

        .submenu .menu-link {
            padding: 10px 20px 10px 52px;
            font-size: 13px;
        }

        .menu-item.has-submenu.active .menu-arrow {
            transform: rotate(180deg);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }
        }

        /* Mobile overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar-overlay.active {
            display: block;
            opacity: 1;
        }
    </style>

    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <a href="<?php echo e(route('dashboard')); ?>" class="logo">
            <i class="fas fa-utensils"></i>
            <span>نظام إدارة المطعم</span>
        </a>
    </div>

    <!-- User Profile -->
    <div class="user-profile-sidebar">
        <img src="https://ui-avatars.com/api/?name=<?php echo e(Auth::user()->name ?? 'Admin'); ?>&background=ffffff&color=1e293b"
             alt="User Avatar" class="user-avatar-sidebar">
        <h6 class="user-name"><?php echo e(Auth::user()->name ?? 'مدير النظام'); ?></h6>
        <p class="user-role">مدير المطعم</p>
    </div>

    <!-- Navigation Menu -->
    <ul class="sidebar-menu">
        <!-- Dashboard -->
        <li class="menu-item">
            <a href="<?php echo e(route('dashboard')); ?>" class="menu-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                <i class="fas fa-tachometer-alt menu-icon"></i>
                <span class="menu-text">لوحة التحكم</span>
            </a>
        </li>

        <!-- POS System -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-cash-register menu-icon"></i>
                <span class="menu-text">نظام نقاط البيع</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('pos.index')); ?>" class="menu-link">لوحة نقاط البيع</a></li>
                <li><a href="<?php echo e(route('pos.create')); ?>" class="menu-link">طلب جديد</a></li>
            </ul>
        </li>

        <!-- Order Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-shopping-bag menu-icon"></i>
                <span class="menu-text">إدارة الطلبات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('orders.index')); ?>" class="menu-link">جميع الطلبات</a></li>
            </ul>
        </li>

        <!-- Kitchen Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-fire menu-icon"></i>
                <span class="menu-text">المطبخ</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="/kitchens" class="menu-link">إدارة المطابخ</a></li>
                <li><a href="/kot-orders" class="menu-link">طلبات المطبخ (KOT)</a></li>
                <li><a href="/kitchen-display" class="menu-link">شاشة المطبخ</a></li>
            </ul>
        </li>

        <!-- Menu Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-book-open menu-icon"></i>
                <span class="menu-text">إدارة القائمة</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('menus.index')); ?>" class="menu-link">إدارة القوائم</a></li>
                <li><a href="<?php echo e(route('categories.index')); ?>" class="menu-link">الفئات</a></li>
                <li><a href="<?php echo e(route('menu-items.index')); ?>" class="menu-link">عناصر القائمة</a></li>
                <li><a href="<?php echo e(route('addons.index')); ?>" class="menu-link">الإضافات</a></li>
                <li><a href="<?php echo e(route('variations.index')); ?>" class="menu-link">المتغيرات</a></li>
            </ul>
        </li>

        <!-- Inventory Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-boxes menu-icon"></i>
                <span class="menu-text">المخزون</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="/inventory/products" class="menu-link">المنتجات</a></li>
                <li><a href="/inventory/suppliers" class="menu-link">الموردين</a></li>
                <li><a href="/inventory/purchase-orders" class="menu-link">أوامر الشراء</a></li>
                <li><a href="/inventory/logs" class="menu-link">سجلات المخزون</a></li>
            </ul>
        </li>

        <!-- Customer Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-users menu-icon"></i>
                <span class="menu-text">👥 إدارة العملاء</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('customers.index')); ?>" class="menu-link">قائمة العملاء</a></li>
                <li><a href="<?php echo e(route('customers.create')); ?>" class="menu-link">إضافة عميل جديد</a></li>
                <li><a href="<?php echo e(route('loyalty-programs.index')); ?>" class="menu-link">برنامج الولاء</a></li>
            </ul>
        </li>

        <!-- Delivery Management -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-truck menu-icon"></i>
                <span class="menu-text">التوصيل</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('delivery.personnel.index')); ?>" class="menu-link">موظفي التوصيل</a></li>
                <li><a href="<?php echo e(route('delivery.personnel.create')); ?>" class="menu-link">إضافة موظف توصيل</a></li>
                <li><a href="<?php echo e(route('delivery.zones.index')); ?>" class="menu-link">مناطق التوصيل</a></li>
                <li><a href="<?php echo e(route('delivery.tracking.index')); ?>" class="menu-link">تتبع التوصيل</a></li>
                <li><a href="<?php echo e(route('delivery.reviews.index')); ?>" class="menu-link">تقييمات التوصيل</a></li>
            </ul>
        </li>

        <!-- System Management -->
        <li class="menu-category">إدارة النظام</li>

        <!-- Subscriptions -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-envelope menu-icon"></i>
                <span class="menu-text">الاشتراكات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('subscriptions-web.index')); ?>" class="menu-link">قائمة الاشتراكات</a></li>
                <li><a href="<?php echo e(route('subscriptions-web.create')); ?>" class="menu-link">إضافة اشتراك جديد</a></li>
            </ul>
        </li>

        <!-- Packages -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-box menu-icon"></i>
                <span class="menu-text">الباقات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('packages.index')); ?>" class="menu-link">قائمة الباقات</a></li>
                <li><a href="<?php echo e(route('packages.create')); ?>" class="menu-link">إضافة باقة جديدة</a></li>
            </ul>
        </li>

        <!-- Tenants -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-building menu-icon"></i>
                <span class="menu-text">المستأجرين</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('tenants.index')); ?>" class="menu-link">قائمة المستأجرين</a></li>
                <li><a href="<?php echo e(route('tenants.create')); ?>" class="menu-link">إضافة مستأجر جديد</a></li>
            </ul>
        </li>

        <!-- Branches -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-code-branch menu-icon"></i>
                <span class="menu-text">الفروع</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('branches.index')); ?>" class="menu-link">قائمة الفروع</a></li>
                <li><a href="<?php echo e(route('branches.create')); ?>" class="menu-link">إضافة فرع جديد</a></li>
            </ul>
        </li>

        <!-- Users -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-user menu-icon"></i>
                <span class="menu-text">المستخدمين</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('users.index')); ?>" class="menu-link">قائمة المستخدمين</a></li>
                <li><a href="<?php echo e(route('users.create')); ?>" class="menu-link">إضافة مستخدم جديد</a></li>
            </ul>
        </li>

        <!-- Roles & Permissions -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-shield-alt menu-icon"></i>
                <span class="menu-text">الأدوار والصلاحيات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('roles.index')); ?>" class="menu-link">الأدوار</a></li>
                <li><a href="<?php echo e(route('permissions.index')); ?>" class="menu-link">الصلاحيات</a></li>
            </ul>
        </li>

        <!-- Reservations -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-chair menu-icon"></i>
                <span class="menu-text">الحجوزات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('reservation.reservations')); ?>" class="menu-link">جميع الحجوزات</a></li>
                <li><a href="<?php echo e(route('areas.index')); ?>" class="menu-link">إدارة المناطق</a></li>
                <li><a href="<?php echo e(route('tables.index')); ?>" class="menu-link">إدارة الطاولات</a></li>
                <li><a href="<?php echo e(route('reservation.waiter-requests')); ?>" class="menu-link">طلبات النادل</a></li>
                <li><a href="<?php echo e(route('reservation.qr.test')); ?>" class="menu-link">اختبار رموز QR</a></li>
            </ul>
        </li>

        <!-- Public Page -->
     
        <!-- Public Page -->
        <li class="menu-item">
            <a href="/restaurant/<?php echo e(Auth::user()->tenant->name); ?>" class="menu-link" target="_blank">
                <i class="fas fa-globe menu-icon"></i>
                <span class="menu-text">صفحة المطعم العامة</span>
            </a>
        </li>


        <!-- Finance & Payments -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-credit-card menu-icon"></i>
                <span class="menu-text">المالية والمدفوعات</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('transactions.index')); ?>" class="menu-link">إدارة المعاملات</a></li>
                <li><a href="<?php echo e(route('payments.index')); ?>" class="menu-link">المدفوعات</a></li>
                <li><a href="/payments/methods" class="menu-link">طرق الدفع</a></li>
                <li><a href="/payments/refunds" class="menu-link">المرتجعات</a></li>
            </ul>
        </li>

        <!-- HR -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-user-tie menu-icon"></i>
                <span class="menu-text">الموارد البشرية</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="/hr/staff" class="menu-link">إدارة الموظفين</a></li>
                <li><a href="/hr/attendance" class="menu-link">الحضور والانصراف</a></li>
                <li><a href="/hr/shifts" class="menu-link">الورديات</a></li>
                <li><a href="/hr/leave-requests" class="menu-link">طلبات الإجازة</a></li>
                <li><a href="/hr/penalties" class="menu-link">الجزاءات</a></li>
            </ul>
        </li>

        <!-- Payroll -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-money-bill-wave menu-icon"></i>
                <span class="menu-text">الرواتب</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="/payroll/payslips" class="menu-link">قسائم الراتب</a></li>
                <li><a href="/payroll/periods" class="menu-link">فترات الدفع</a></li>
            </ul>
        </li>

        <!-- Stock Control -->
        <li class="menu-item has-submenu">
            <a href="#" class="menu-link" onclick="toggleSubmenu(this)">
                <i class="fas fa-warehouse menu-icon"></i>
                <span class="menu-text">إدارة المخزون</span>
                <i class="fas fa-chevron-down menu-arrow"></i>
            </a>
            <ul class="submenu">
                <li><a href="<?php echo e(route('inventory.index')); ?>" class="menu-link">لوحة تحكم المخزون</a></li>
                <li><a href="<?php echo e(route('inventory.units.index')); ?>" class="menu-link">الوحدات</a></li>
                <li><a href="<?php echo e(route('inventory.categories.index')); ?>" class="menu-link">فئات المواد</a></li>
                <li><a href="<?php echo e(route('inventory.items.index')); ?>" class="menu-link">إدارة المواد</a></li>
                <li><a href="<?php echo e(route('inventory.stock.index')); ?>" class="menu-link">إدارة المخزون</a></li>
                <li><a href="<?php echo e(route('inventory.stock.movements')); ?>" class="menu-link">حركات المخزون</a></li>
                <li><a href="<?php echo e(route('inventory.recipes.index')); ?>" class="menu-link">الوصفات</a></li>
                <li><a href="<?php echo e(route('inventory.purchase-orders.index')); ?>" class="menu-link">أوامر الشراء</a></li>
                <li><a href="<?php echo e(route('inventory.suppliers.index')); ?>" class="menu-link">الموردين</a></li>
            </ul>
        </li>
    </ul>

    <script>
        // Toggle submenu
        function toggleSubmenu(element) {
            event.preventDefault();

            const menuItem = element.parentElement;
            const submenu = menuItem.querySelector('.submenu');
            const isActive = menuItem.classList.contains('active');

            // Close all other submenus
            document.querySelectorAll('.menu-item.has-submenu.active').forEach(item => {
                if (item !== menuItem) {
                    item.classList.remove('active');
                    item.querySelector('.submenu').classList.remove('active');
                }
            });

            // Toggle current submenu
            if (isActive) {
                menuItem.classList.remove('active');
                submenu.classList.remove('active');
            } else {
                menuItem.classList.add('active');
                submenu.classList.add('active');
            }
        }

        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // Close sidebar when clicking on overlay
        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    toggleSidebar();
                });
            }
        });
    </script>
</aside>

<!-- Mobile Sidebar Overlay -->
<div class="sidebar-overlay" id="sidebar-overlay"></div><?php /**PATH D:\my dehive work\test\epis - Copy\resources\views/layouts/main-sidebar.blade.php ENDPATH**/ ?>