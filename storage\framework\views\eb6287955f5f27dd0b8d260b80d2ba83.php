<?php $__env->startSection('css'); ?>
<!-- DataTables CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة الإضافات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الإضافات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-addon-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الإضافات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع الإضافات المتاحة</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="addons-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الإضافة</th>
                                <th class="border-bottom-0">السعر</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Addon Modal -->
<div class="modal fade" id="addonModal" tabindex="-1" role="dialog" aria-labelledby="addonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addonModalLabel">إضافة إضافة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addonForm">
                <div class="modal-body">
                    <input type="hidden" id="addon_id" name="addon_id">
                    
                    <!-- Basic Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="menu_item_id">عنصر القائمة <span class="text-danger">*</span></label>
                                        <select class="form-control select2" id="menu_item_id" name="menu_item_id" required>
                                            <option value="">اختر عنصر القائمة</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="addon_group_name">اسم مجموعة الإضافة</label>
                                        <input type="text" class="form-control" id="addon_group_name" name="addon_group_name">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">اسم الإضافة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code">الكود <small class="text-muted">(يتم إنشاؤه تلقائياً)</small></label>
                                        <input type="text" class="form-control" id="code" name="code" placeholder="سيتم إنشاؤه تلقائياً من الاسم" readonly>
                                        <small class="form-text text-muted">سيتم إنشاء الكود تلقائياً بناءً على اسم الإضافة</small>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات التسعير</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price">السعر <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cost">التكلفة</label>
                                        <input type="number" step="0.01" class="form-control" id="cost" name="cost">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">الإعدادات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_required">مطلوب</label>
                                        <select class="form-control" id="is_required" name="is_required">
                                            <option value="0">اختياري</option>
                                            <option value="1">مطلوب</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="max_quantity">الحد الأقصى للكمية</label>
                                        <input type="number" class="form-control" id="max_quantity" name="max_quantity" min="1">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_active">الحالة</label>
                                        <select class="form-control" id="is_active" name="is_active">
                                            <option value="1">نشط</option>
                                            <option value="0">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sort_order">ترتيب العرض</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" min="0">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-addon-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Addon Modal -->
<div class="modal fade" id="showAddonModal" tabindex="-1" role="dialog" aria-labelledby="showAddonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showAddonModalLabel">تفاصيل الإضافة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Basic Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>عنصر القائمة:</strong></label>
                                    <p id="show_menu_item"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>اسم مجموعة الإضافة:</strong></label>
                                    <p id="show_addon_group_name"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>اسم الإضافة:</strong></label>
                                    <p id="show_name"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>كود الإضافة:</strong></label>
                                    <p id="show_code"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات التسعير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>السعر:</strong></label>
                                    <p id="show_price"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>التكلفة:</strong></label>
                                    <p id="show_cost"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Status -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">الإعدادات والحالة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>مطلوب:</strong></label>
                                    <p id="show_is_required"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الحد الأقصى للكمية:</strong></label>
                                    <p id="show_max_quantity"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الحالة:</strong></label>
                                    <p id="show_status"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>ترتيب العرض:</strong></label>
                                    <p id="show_sort_order"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ الإنشاء:</strong></label>
                                    <p id="show_created_at"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ التحديث:</strong></label>
                                    <p id="show_updated_at"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- DataTables JS -->
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
<!-- Select2 JS -->
<script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
<!-- Sweet Alert JS -->
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#addonModal')
    });

    // Load menu items
    function loadMenuItems() {
        $.ajax({
            url: '<?php echo e(route("menu-items.index")); ?>',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر عنصر القائمة</option>';
                if (response.data) {
                    $.each(response.data, function(index, item) {
                        options += '<option value="' + item.id + '">' + item.name + '</option>';
                    });
                }
                $('#menu_item_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading menu items');
            }
        });
    }

    // Load menu items on page load
    loadMenuItems();

    // Initialize DataTable with server-side processing
    var table = $('#addons-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("addons.index")); ?>',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'price', name: 'price' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Add Addon Button
    $('#add-addon-btn').click(function() {
        $('#addonForm')[0].reset();
        $('#addon_id').val('');
        $('#menu_item_id').val('').trigger('change');
        $('#addonModalLabel').text('إضافة إضافة جديدة');
        $('.form-control').removeClass('is-invalid');
        $('#code').val(''); // Clear code field for new addons
        $('#addonModal').modal('show');
    });

    // Generate code preview when name changes
    $('#name').on('input', function() {
        var name = $(this).val();
        if (name && !$('#addon_id').val()) { // Only for new addons
            var code = generateCodeFromName(name, 'ADD');
            $('#code').val(code);
        }
    });

    // Function to generate code from name
    function generateCodeFromName(name, prefix) {
        // Remove Arabic diacritics and special characters
        var cleanName = name.replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, '');
        
        // Map common Arabic words to English
        var arabicToEnglish = {
            'جبن': 'CHEESE',
            'خضار': 'VEGETABLES',
            'صوص': 'SAUCE',
            'توابل': 'SPICES',
            'إضافي': 'EXTRA',
            'كبير': 'LARGE',
            'صغير': 'SMALL',
            'متوسط': 'MEDIUM',
            'حار': 'SPICY',
            'بارد': 'COLD',
            'ساخن': 'HOT',
            'مقلي': 'FRIED',
            'مشوي': 'GRILLED',
            'مسلوق': 'BOILED'
        };
        
        var words = cleanName.split(' ');
        var codeWords = [];
        
        words.forEach(function(word) {
            word = word.trim();
            if (word) {
                if (arabicToEnglish[word]) {
                    codeWords.push(arabicToEnglish[word]);
                } else {
                    // Take first 3 characters and convert to uppercase
                    codeWords.push(word.substring(0, 3).toUpperCase());
                }
            }
        });
        
        var baseCode = codeWords.join('_');
        return prefix + '_' + baseCode;
    }

    // Edit Addon Button
    $(document).on('click', '.edit-addon', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("addons.edit", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#addon_id').val(response.id);
                $('#menu_item_id').val(response.menu_item_id).trigger('change');
                $('#addon_group_name').val(response.addon_group_name);
                $('#name').val(response.name);
                $('#code').val(response.code);
                $('#price').val(response.price);
                $('#cost').val(response.cost);
                $('#is_required').val(response.is_required);
                $('#max_quantity').val(response.max_quantity);
                $('#is_active').val(response.is_active);
                $('#sort_order').val(response.sort_order);
                $('#addonModalLabel').text('تعديل الإضافة');
                $('.form-control').removeClass('is-invalid');
                $('#addonModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الإضافة', 'error');
            }
        });
    });

    // Show Addon Button
    $(document).on('click', '.show-addon', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("addons.show", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var addon = response;
                $('#show_menu_item').text(addon.menu_item ? addon.menu_item.name : '-');
                $('#show_addon_group_name').text(addon.addon_group_name || '-');
                $('#show_name').text(addon.name || '-');
                $('#show_code').text(addon.code || '-');
                $('#show_price').text(addon.price ? addon.price + ' ريال' : '-');
                $('#show_cost').text(addon.cost ? addon.cost + ' ريال' : '-');
                $('#show_is_required').html(addon.is_required == 1 ? '<span class="badge badge-warning">مطلوب</span>' : '<span class="badge badge-secondary">اختياري</span>');
                $('#show_max_quantity').text(addon.max_quantity || '-');
                $('#show_status').html(addon.is_active == 1 ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                $('#show_sort_order').text(addon.sort_order || '-');
                $('#show_created_at').text(new Date(addon.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(addon.updated_at).toLocaleDateString('ar-SA'));
                $('#showAddonModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الإضافة', 'error');
            }
        });
    });

    // Delete Addon Button
    $(document).on('click', '.delete-addon', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(route("addons.destroy", ":id")); ?>'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف الإضافة بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف الإضافة", "error");
                }
            });
        });
    });

    // Save Addon Form
    $('#addonForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#addon_id').val();
        var url = id ? '<?php echo e(route("addons.update", ":id")); ?>'.replace(':id', id) : '<?php echo e(route("addons.store")); ?>';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#addonModal').modal('hide');
                swal("نجح!", id ? "تم تحديث الإضافة بنجاح" : "تم إضافة الإضافة بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ الإضافة", "error");
                }
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\test\epis - Copy\modules\Menu\Providers/../resources/views/addons.blade.php ENDPATH**/ ?>