@extends('layouts.master')

@push('inventory-styles')
<style>
.stock-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    margin-bottom: 20px;
}

.stock-card:hover {
    transform: translateY(-2px);
}

.stock-level-indicator {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 10px;
}

.stock-level-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.stock-level-fill.critical { background: #dc3545; }
.stock-level-fill.low { background: #ffc107; }
.stock-level-fill.normal { background: #28a745; }
.stock-level-fill.high { background: #17a2b8; }

.alert-item {
    border-left: 4px solid;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
}

.alert-item.critical {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.alert-item.low {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.quick-action-btn {
    width: 100%;
    margin-bottom: 10px;
    text-align: left;
    border-radius: 8px;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إدارة المخزون</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="bulkStockUpdate()">
                <i class="mdi mdi-cube-outline"></i> تحديث مجمع
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="generateStockReport()">
                <i class="mdi mdi-file-chart"></i> تقرير المخزون
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Stock Overview Cards -->
<div class="row row-sm">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-danger">
                        <i class="mdi mdi-alert-circle"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-danger">{{ $analytics['critical_stock'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون حرج</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-alert"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-warning">{{ $analytics['low_stock'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون منخفض</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-success">
                        <i class="mdi mdi-check-circle"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-success">{{ $analytics['normal_stock'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون طبيعي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-info">
                        <i class="mdi mdi-trending-up"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-info">{{ $analytics['overstock'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون زائد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Stock Alerts -->
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">تنبيهات المخزون</h4>
                <div class="card-options">
                    <a href="{{ route('inventory.stock.alerts') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                @if($criticalItems && $criticalItems->count() > 0)
                    @foreach($criticalItems->take(5) as $item)
                    <div class="alert-item critical">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 text-danger">{{ $item->product->name }}</h6>
                                <small class="text-muted">{{ $item->product->sku }}</small>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-danger">{{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}</span>
                                <small class="text-muted d-block">نفد المخزون</small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif
                
                @if($lowStockItems && $lowStockItems->count() > 0)
                    @foreach($lowStockItems->take(3) as $item)
                    <div class="alert-item low">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 text-warning">{{ $item->product->name }}</h6>
                                <small class="text-muted">{{ $item->product->sku }}</small>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-warning">{{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}</span>
                                <small class="text-muted d-block">الحد الأدنى: {{ $item->minimum_stock }}</small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif
                
                @if((!$criticalItems || $criticalItems->count() == 0) && (!$lowStockItems || $lowStockItems->count() == 0))
                <div class="text-center text-muted py-4">
                    <i class="mdi mdi-check-circle" style="font-size: 48px;"></i>
                    <p class="mt-2">لا توجد تنبيهات مخزون حالياً</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">الإجراءات السريعة</h4>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-outline-primary quick-action-btn" onclick="quickStockUpdate()">
                    <i class="mdi mdi-cube-outline mr-2"></i>تحديث سريع للمخزون
                </button>
                
                <button type="button" class="btn btn-outline-success quick-action-btn" onclick="stockAdjustment()">
                    <i class="mdi mdi-adjust mr-2"></i>تسوية المخزون
                </button>
                
                <button type="button" class="btn btn-outline-info quick-action-btn" onclick="stockTransfer()">
                    <i class="mdi mdi-swap-horizontal mr-2"></i>نقل المخزون
                </button>
                
                <button type="button" class="btn btn-outline-warning quick-action-btn" onclick="stockCount()">
                    <i class="mdi mdi-counter mr-2"></i>جرد المخزون
                </button>
                
                <button type="button" class="btn btn-outline-secondary quick-action-btn" onclick="viewMovements()">
                    <i class="mdi mdi-history mr-2"></i>حركات المخزون
                </button>
            </div>
        </div>
    </div>
    
    <!-- Recent Movements -->
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">آخر الحركات</h4>
                <div class="card-options">
                    <a href="{{ route('inventory.stock.movements') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                @if($recentMovements && $recentMovements->count() > 0)
                    @foreach($recentMovements->take(8) as $movement)
                    <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                        <div>
                            <h6 class="mb-1">{{ $movement->branchInventory->product->name }}</h6>
                            <small class="text-muted">
                                @switch($movement->type)
                                    @case('in')
                                        إدخال
                                        @break
                                    @case('out')
                                        إخراج
                                        @break
                                    @case('adjustment')
                                        تعديل
                                        @break
                                    @case('transfer')
                                        نقل
                                        @break
                                    @default
                                        {{ $movement->type }}
                                @endswitch
                            </small>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-{{ $movement->type === 'out' ? 'danger' : 'success' }}">
                                {{ $movement->type === 'out' ? '-' : '+' }}{{ $movement->quantity }}
                            </span>
                            <small class="text-muted d-block">{{ $movement->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                    @endforeach
                @else
                <div class="text-center text-muted py-4">
                    <i class="mdi mdi-history" style="font-size: 48px;"></i>
                    <p class="mt-2">لا توجد حركات مخزون حديثة</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Stock Levels Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">مستويات المخزون</h4>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshStockTable()">
                                <i class="mdi mdi-refresh"></i> تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportStock()">
                                <i class="mdi mdi-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="stock-table" class="table table-striped table-bordered text-md-nowrap">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المادة</th>
                                <th>الرمز</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>الحد الأقصى</th>
                                <th>مستوى المخزون</th>
                                <th>الحالة</th>
                                <th>آخر حركة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
@include('Inventory::stock.modals')

@endsection

@push('inventory-scripts')
<script>
let stockTable;

$(document).ready(function() {
    initializeStockTable();
});

function initializeStockTable() {
    stockTable = $('#stock-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.stock.datatable") }}',
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'product.name', name: 'product.name' },
            { data: 'product.sku', name: 'product.sku' },
            { 
                data: 'current_stock', 
                name: 'current_stock',
                render: function(data, type, row) {
                    return `${data} ${row.product.unit?.symbol || ''}`;
                }
            },
            { data: 'minimum_stock', name: 'minimum_stock' },
            { data: 'maximum_stock', name: 'maximum_stock' },
            { 
                data: 'stock_level', 
                name: 'stock_level',
                render: function(data, type, row) {
                    const percentage = getStockPercentage(row.current_stock, row.minimum_stock, row.maximum_stock);
                    const level = getStockLevel(row.current_stock, row.minimum_stock, row.maximum_stock);
                    
                    return `
                        <div class="stock-level-indicator">
                            <div class="stock-level-fill ${level}" style="width: ${percentage}%"></div>
                        </div>
                        <small class="text-muted">${Math.round(percentage)}%</small>
                    `;
                }
            },
            { 
                data: 'status', 
                name: 'status',
                render: function(data, type, row) {
                    return getStockStatusBadge(row.current_stock, row.minimum_stock);
                }
            },
            { 
                data: 'last_movement', 
                name: 'last_movement',
                render: function(data) {
                    return data ? InventoryModule.formatDate(data) : '-';
                }
            },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="table-actions">
                            <button type="button" class="btn btn-sm btn-primary btn-action" onclick="updateStock(${row.id})" title="تحديث المخزون">
                                <i class="mdi mdi-cube-outline"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-info btn-action" onclick="viewMovements(${row.id})" title="الحركات">
                                <i class="mdi mdi-history"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-success btn-action" onclick="adjustStock(${row.id})" title="تسوية">
                                <i class="mdi mdi-adjust"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[1, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true
    });
}

function getStockPercentage(current, minimum, maximum) {
    if (!maximum) return current > minimum ? 100 : (current / minimum) * 50;
    return Math.min((current / maximum) * 100, 100);
}

function getStockLevel(current, minimum, maximum) {
    if (current <= 0) return 'critical';
    if (current <= minimum) return 'low';
    if (maximum && current >= maximum * 0.8) return 'high';
    return 'normal';
}

function getStockStatusBadge(current, minimum) {
    if (current <= 0) {
        return '<span class="badge badge-danger">نفد المخزون</span>';
    } else if (current <= minimum) {
        return '<span class="badge badge-warning">مخزون منخفض</span>';
    } else {
        return '<span class="badge badge-success">متوفر</span>';
    }
}

function refreshStockTable() {
    stockTable.ajax.reload();
}

function updateStock(id) {
    $('#updateStockModal').modal('show');
    $('#updateStockModal').data('item-id', id);
}

function viewMovements(id) {
    window.location.href = `{{ route('inventory.stock.movements.item', ':id') }}`.replace(':id', id);
}

function adjustStock(id) {
    $('#adjustStockModal').modal('show');
    $('#adjustStockModal').data('item-id', id);
}

function quickStockUpdate() {
    $('#quickUpdateModal').modal('show');
}

function stockAdjustment() {
    $('#stockAdjustmentModal').modal('show');
}

function stockTransfer() {
    $('#stockTransferModal').modal('show');
}

function stockCount() {
    $('#stockCountModal').modal('show');
}

function bulkStockUpdate() {
    $('#bulkUpdateModal').modal('show');
}

function generateStockReport() {
    window.location.href = '{{ route("inventory.api.stock.report") }}';
}

function exportStock() {
    window.location.href = '{{ route("inventory.api.stock.export") }}';
}
</script>
@endpush
