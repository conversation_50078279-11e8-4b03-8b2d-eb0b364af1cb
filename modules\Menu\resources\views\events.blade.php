@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<!-- Date Picker CSS -->
<link href="{{URL::asset('assets/plugins/jquery-ui/ui/widgets/datepicker.js')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/amazeui-datetimepicker/css/amazeui.datetimepicker.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة الفعاليات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الفعاليات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-event-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الفعاليات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع فعاليات المطعم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="events-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الفعالية</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">تاريخ البداية</th>
                                <th class="border-bottom-0">تاريخ النهاية</th>
                                <th class="border-bottom-0">السعر</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" role="dialog" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">إضافة فعالية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="eventForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="event_id" name="event_id">
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title">عنوان الفعالية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title_en">عنوان الفعالية (إنجليزي)</label>
                                <input type="text" class="form-control" id="title_en" name="title_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="event_type">نوع الفعالية <span class="text-danger">*</span></label>
                                <select class="form-control" id="event_type" name="event_type" required>
                                    <option value="">اختر نوع الفعالية</option>
                                    <option value="workshop">ورشة عمل</option>
                                    <option value="tasting">تذوق</option>
                                    <option value="cooking_class">فصل طبخ</option>
                                    <option value="live_music">موسيقى حية</option>
                                    <option value="special_dinner">عشاء خاص</option>
                                    <option value="celebration">احتفال</option>
                                    <option value="other">أخرى</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location">الموقع</label>
                                <input type="text" class="form-control" id="location" name="location">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Date & Time -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">التاريخ والوقت</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_time">وقت البداية <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_time">وقت النهاية</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Capacity -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">التسعير والسعة</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="price">السعر</label>
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_participants">الحد الأقصى للمشاركين</label>
                                <input type="number" class="form-control" id="max_participants" name="max_participants" min="1">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="min_participants">الحد الأدنى للمشاركين</label>
                                <input type="number" class="form-control" id="min_participants" name="min_participants" min="1">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الإعدادات</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requires_reservation" name="requires_reservation" value="1">
                                    <label class="form-check-label" for="requires_reservation">
                                        يتطلب حجز
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" value="1">
                                    <label class="form-check-label" for="is_recurring">
                                        متكرر
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الصور</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="image_urls">صور الفعالية</label>
                                <input type="file" class="form-control" id="image_urls" name="image_urls[]" multiple accept="image/*">
                                <small class="form-text text-muted">يمكنك اختيار عدة صور</small>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-event-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Event Modal -->
<div class="modal fade" id="showEventModal" tabindex="-1" role="dialog" aria-labelledby="showEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showEventModalLabel">تفاصيل الفعالية</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>عنوان الفعالية:</strong></label>
                            <p id="show_title"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النوع:</strong></label>
                            <p id="show_event_type"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label><strong>الوصف:</strong></label>
                            <p id="show_description"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ البداية:</strong></label>
                            <p id="show_start_date"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ النهاية:</strong></label>
                            <p id="show_end_date"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>السعر:</strong></label>
                            <p id="show_price"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحد الأقصى للمشاركين:</strong></label>
                            <p id="show_max_participants"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مميز:</strong></label>
                            <p id="show_featured"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.flash.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var eventsTable = $('#events-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("api.menu.events.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            {data: 'id', name: 'id'},
            {data: 'title', name: 'title'},
            {data: 'event_type', name: 'event_type'},
            {data: 'start_date', name: 'start_date'},
            {data: 'end_date', name: 'end_date'},
            {data: 'price', name: 'price'},
            {data: 'is_active', name: 'is_active', render: function(data) {
                return data ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
            }},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Add Event Button
    $('#add-event-btn').click(function() {
        $('#eventForm')[0].reset();
        $('#event_id').val('');
        $('#eventModalLabel').text('إضافة فعالية جديدة');
        $('#eventModal').modal('show');
    });

    // Submit Event Form
    $('#eventForm').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var eventId = $('#event_id').val();
        var url = eventId ? '{{ route("api.menu.events.update", ":id") }}'.replace(':id', eventId) : '{{ route("api.menu.events.store") }}';
        var method = eventId ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#eventModal').modal('hide');
                    eventsTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $('#' + key).addClass('is-invalid');
                        $('#' + key).siblings('.invalid-feedback').text(value[0]);
                    });
                } else {
                    swal('خطأ!', 'حدث خطأ أثناء حفظ الفعالية', 'error');
                }
            }
        });
    });

    // Edit Event
    $(document).on('click', '.edit-event', function() {
        var eventId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.events.show", ":id") }}'.replace(':id', eventId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var event = response.data;
                    $('#event_id').val(event.id);
                    $('#title').val(event.title);
                    $('#title_en').val(event.title_en);
                    $('#description').val(event.description);
                    $('#event_type').val(event.event_type);
                    $('#location').val(event.location);
                    $('#start_date').val(event.start_date);
                    $('#start_time').val(event.start_time);
                    $('#end_date').val(event.end_date);
                    $('#end_time').val(event.end_time);
                    $('#price').val(event.price);
                    $('#max_participants').val(event.max_participants);
                    $('#min_participants').val(event.min_participants);
                    $('#is_active').prop('checked', event.is_active);
                    $('#is_featured').prop('checked', event.is_featured);
                    $('#requires_reservation').prop('checked', event.requires_reservation);
                    $('#is_recurring').prop('checked', event.is_recurring);
                    
                    $('#eventModalLabel').text('تعديل الفعالية');
                    $('#eventModal').modal('show');
                }
            }
        });
    });

    // Show Event
    $(document).on('click', '.show-event', function() {
        var eventId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.events.show", ":id") }}'.replace(':id', eventId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var event = response.data;
                    $('#show_title').text(event.title);
                    $('#show_event_type').text(event.event_type);
                    $('#show_description').text(event.description || 'غير محدد');
                    $('#show_start_date').text(event.start_date + ' ' + (event.start_time || ''));
                    $('#show_end_date').text(event.end_date + ' ' + (event.end_time || ''));
                    $('#show_price').text(event.price ? event.price + ' ريال' : 'مجاني');
                    $('#show_max_participants').text(event.max_participants || 'غير محدود');
                    $('#show_status').html(event.is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                    $('#show_featured').html(event.is_featured ? '<span class="badge badge-info">مميز</span>' : '<span class="badge badge-secondary">عادي</span>');
                    
                    $('#showEventModal').modal('show');
                }
            }
        });
    });

    // Delete Event
    $(document).on('click', '.delete-event', function() {
        var eventId = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("api.menu.events.destroy", ":id") }}'.replace(':id', eventId),
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        eventsTable.ajax.reload();
                        swal('تم الحذف!', response.message, 'success');
                    }
                },
                error: function() {
                    swal('خطأ!', 'حدث خطأ أثناء حذف الفعالية', 'error');
                }
            });
        });
    });

    // Toggle Event Status
    $(document).on('click', '.toggle-status', function() {
        var eventId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.events.toggle-status", ":id") }}'.replace(':id', eventId),
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    eventsTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function() {
                swal('خطأ!', 'حدث خطأ أثناء تغيير حالة الفعالية', 'error');
            }
        });
    });
});
</script>
@endsection