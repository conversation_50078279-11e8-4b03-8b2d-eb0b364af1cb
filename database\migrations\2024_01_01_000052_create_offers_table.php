<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->string('offer_type')->default('discount'); // discount, bogo, combo, free_item, cashback
            $table->string('discount_type')->nullable(); // percentage, fixed_amount, buy_x_get_y
            $table->decimal('discount_value', 10, 2)->nullable();
            $table->decimal('max_discount_amount', 10, 2)->nullable();
            $table->decimal('min_order_amount', 10, 2)->nullable();
            $table->integer('buy_quantity')->nullable(); // For BOGO offers
            $table->integer('get_quantity')->nullable(); // For BOGO offers
            $table->json('applicable_items')->nullable(); // Menu items this offer applies to
            $table->json('excluded_items')->nullable(); // Menu items excluded from offer
            $table->json('applicable_categories')->nullable(); // Categories this offer applies to
            $table->json('excluded_categories')->nullable(); // Categories excluded from offer
            $table->json('combo_items')->nullable(); // For combo offers
            $table->decimal('combo_price', 10, 2)->nullable();
            $table->string('promo_code')->nullable();
            $table->boolean('requires_promo_code')->default(false);
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->json('available_days')->nullable(); // [0,1,2,3,4,5,6] for days of week
            $table->json('customer_eligibility')->nullable(); // new, existing, vip, etc.
            $table->integer('usage_limit_per_customer')->nullable();
            $table->integer('total_usage_limit')->nullable();
            $table->integer('current_usage_count')->default(0);
            $table->boolean('is_stackable')->default(false); // Can be combined with other offers
            $table->json('stackable_with')->nullable(); // Specific offers this can be combined with
            $table->string('priority')->default('medium'); // low, medium, high
            $table->json('terms_conditions')->nullable();
            $table->string('image_url')->nullable();
            $table->string('banner_image')->nullable();
            $table->json('notification_settings')->nullable();
            $table->boolean('auto_apply')->default(false);
            $table->json('auto_apply_conditions')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_public')->default(true); // Visible to customers
            $table->integer('sort_order')->default(0);
            $table->json('analytics_data')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['start_date', 'end_date']);
            $table->index(['is_active', 'is_public']);
            $table->index('offer_type');
            $table->index('promo_code');
            $table->index('code');
            $table->index(['is_featured', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offers');
    }
};