<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerService
{
    /**
     * Get all customers for a specific branch with pagination
     */
    public function getCustomersByBranch(int $branchId, int $perPage = 15): LengthAwarePaginator
    {
        return Customer::where('branch_id', $branchId)
            ->with(['branch', 'loyaltyTransactions'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Create a new customer
     */
    public function createCustomer(array $data): Customer
    {
        return Customer::create($data);
    }

    /**
     * Update an existing customer
     */
    public function updateCustomer(int $customerId, array $data): Customer
    {
        $customer = Customer::findOrFail($customerId);
        $customer->update($data);
        return $customer->fresh();
    }

    /**
     * Get customer by ID
     */
    public function getCustomerById(int $customerId): Customer
    {
        return Customer::with(['branch', 'loyaltyTransactions', 'orders'])
            ->findOrFail($customerId);
    }

    /**
     * Delete a customer (soft delete)
     */
    public function deleteCustomer(int $customerId): bool
    {
        $customer = Customer::findOrFail($customerId);
        return $customer->delete();
    }


    /**
     * Activate a customer
     */
    public function activateCustomer(int $customerId): Customer
    {
        $customer = Customer::findOrFail($customerId);
        $customer->update(['is_active' => true]);
        return $customer;
    }

    /**
     * Deactivate a customer
     */
    public function deactivateCustomer(int $customerId): Customer
    {
        $customer = Customer::findOrFail($customerId);
        $customer->update(['is_active' => false]);
        return $customer;
    }

    /**
     * Update customer's last visit
     */
    public function updateLastVisit(int $customerId): Customer
    {
        $customer = Customer::findOrFail($customerId);
        $customer->update(['last_visit_at' => now()]);
        return $customer;
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(int $customerId, float $points, string $description = null, int $processedBy = null): Customer
    {
        $customer = Customer::findOrFail($customerId);
        
        // Create loyalty transaction
        LoyaltyTransaction::create([
            'customer_id' => $customerId,
            'transaction_type' => 'earned',
            'points' => $points,
            'description' => $description ?? 'Points earned',
            'processed_by' => $processedBy,
        ]);

        // Update customer's total loyalty points
        $customer->increment('loyalty_points', $points);
        
        return $customer->fresh();
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemLoyaltyPoints(int $customerId, float $points, string $description = null, int $processedBy = null): Customer
    {
        $customer = Customer::findOrFail($customerId);
        
        if ($customer->loyalty_points < $points) {
            throw new \Exception('Insufficient loyalty points');
        }

        // Create loyalty transaction
        LoyaltyTransaction::create([
            'customer_id' => $customerId,
            'transaction_type' => 'redeemed',
            'points' => $points,
            'description' => $description ?? 'Points redeemed',
            'processed_by' => $processedBy,
        ]);

        // Update customer's total loyalty points
        $customer->decrement('loyalty_points', $points);
        
        return $customer->fresh();
    }

    /**
     * Get customer's loyalty history
     */
    public function getLoyaltyHistory(int $customerId): Collection
    {
        return LoyaltyTransaction::where('customer_id', $customerId)
            ->with(['processedBy'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get active customers by branch
     */
    public function getActiveCustomersByBranch(int $branchId): Collection
    {
        return Customer::where('branch_id', $branchId)
            ->where('is_active', true)
            ->with(['branch', 'loyaltyTransactions'])
            ->get();
    }

    /**
     * Search customers by name or phone
     */
    public function searchCustomers(string $query, int $branchId = null): Collection
    {
        $queryBuilder = Customer::where(function ($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('phone', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%");
        });

        if ($branchId) {
            $queryBuilder->where('branch_id', $branchId);
        }

        return $queryBuilder->with(['branch', 'loyaltyTransactions'])
            ->limit(10)
            ->get();
    }

    /**
     * Get customer statistics
     */
    public function getCustomerStatistics(int $branchId = null): array
    {
        $query = Customer::query();

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return [
            'total_customers' => $query->count(),
            'active_customers' => $query->where('is_active', true)->count(),
            'inactive_customers' => $query->where('is_active', false)->count(),
            'customers_with_loyalty' => $query->whereHas('loyaltyTransactions')->count(),
        ];
    }
}