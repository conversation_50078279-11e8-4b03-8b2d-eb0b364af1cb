<?php

namespace Modules\Kitchen\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KitchenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'branch_id' => $this->branch_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'station_type' => $this->station_type,
            'station_type_label' => $this->getStationTypeLabel(),
            'max_concurrent_orders' => $this->max_concurrent_orders,
            'average_prep_time_minutes' => $this->average_prep_time_minutes,
            'is_active' => $this->is_active,
            'display_order' => $this->display_order,
            'manager_id' => $this->manager_id,
            'equipment_list' => $this->equipment_list,
            'operating_hours' => $this->operating_hours,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'deleted_at' => $this->deleted_at?->toISOString(),

            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                    'code' => $this->branch->code,
                ];
            }),

            'manager' => $this->whenLoaded('manager', function () {
                return [
                    'id' => $this->manager->id,
                    'name' => $this->manager->name,
                    'email' => $this->manager->email,
                ];
            }),

            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                ];
            }),

            'menu_items' => $this->whenLoaded('menuItems', function () {
                return $this->menuItems->map(function ($menuItem) {
                    return [
                        'id' => $menuItem->id,
                        'name' => $menuItem->name,
                        'price' => $menuItem->price,
                        'category' => $menuItem->category?->name,
                        'pivot' => [
                            'prep_time_minutes' => $menuItem->pivot->prep_time_minutes,
                            'priority_level' => $menuItem->pivot->priority_level,
                            'is_active' => $menuItem->pivot->is_active,
                            'special_instructions' => $menuItem->pivot->special_instructions,
                            'assigned_at' => $menuItem->pivot->assigned_at?->toISOString(),
                        ],
                    ];
                });
            }),

            'kitchen_menu_items' => $this->whenLoaded('kitchenMenuItems', function () {
                return KitchenMenuItemResource::collection($this->kitchenMenuItems);
            }),

            'kot_orders' => $this->whenLoaded('kotOrders', function () {
                return KotOrderResource::collection($this->kotOrders);
            }),

            // Computed attributes
            'is_operating' => $this->isOperating(),
            'current_workload' => $this->getCurrentWorkload(),
            'can_accept_orders' => $this->canAcceptOrders(),

            // Statistics (when requested)
            'statistics' => $this->when($request->include_statistics, function () {
                return [
                    'total_kot_orders' => $this->kotOrders()->count(),
                    'pending_kot_orders' => $this->kotOrders()->pending()->count(),
                    'preparing_kot_orders' => $this->kotOrders()->preparing()->count(),
                    'ready_kot_orders' => $this->kotOrders()->ready()->count(),
                    'completed_today' => $this->kotOrders()
                        ->completed()
                        ->whereDate('completed_at', today())
                        ->count(),
                    'average_prep_time_today' => $this->kotOrders()
                        ->completed()
                        ->whereDate('completed_at', today())
                        ->whereNotNull('actual_prep_time_minutes')
                        ->avg('actual_prep_time_minutes'),
                ];
            }),
        ];
    }

    /**
     * Get station type label.
     */
    private function getStationTypeLabel(): string
    {
        $labels = [
            'hot' => 'Hot Station',
            'cold' => 'Cold Station',
            'grill' => 'Grill Station',
            'fryer' => 'Fryer Station',
            'salad' => 'Salad Station',
            'dessert' => 'Dessert Station',
            'beverage' => 'Beverage Station',
            'prep' => 'Prep Station',
            'main' => 'Main Kitchen',
            'other' => 'Other',
        ];

        return $labels[$this->station_type] ?? ucfirst($this->station_type);
    }
}
