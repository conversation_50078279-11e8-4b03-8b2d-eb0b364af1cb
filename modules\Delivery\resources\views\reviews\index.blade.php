@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تقييمات التوصيل</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-info btn-sm" id="stats-btn">
                            <i class="fa fa-chart-bar"></i> الإحصائيات
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="personnel-filter" class="form-control">
                                <option value="">جميع موظفي التوصيل</option>
                                @foreach($deliveryPersonnel as $personnel)
                                    <option value="{{ $personnel->id }}">{{ $personnel->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select id="rating-filter" class="form-control">
                                <option value="">جميع التقييمات</option>
                                <option value="5">5 نجوم</option>
                                <option value="4">4 نجوم</option>
                                <option value="3">3 نجوم</option>
                                <option value="2">2 نجوم</option>
                                <option value="1">1 نجمة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" id="date-from" class="form-control" placeholder="من تاريخ">
                        </div>
                        <div class="col-md-2">
                            <input type="date" id="date-to" class="form-control" placeholder="إلى تاريخ">
                        </div>
                        <div class="col-md-3">
                            <button type="button" id="filter-btn" class="btn btn-info">
                                <i class="fa fa-filter"></i> فلتر
                            </button>
                            <button type="button" id="reset-btn" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="reviews-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>موظف التوصيل</th>
                                    <th>التقييم</th>
                                    <th>التعليق</th>
                                    <th>تاريخ التوصيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إحصائيات تقييمات التوصيل</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="date" id="stats-date-from" class="form-control" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-3">
                        <input type="date" id="stats-date-to" class="form-control" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-3">
                        <button type="button" id="load-stats" class="btn btn-primary">
                            <i class="fa fa-chart-bar"></i> تحميل الإحصائيات
                        </button>
                    </div>
                </div>
                
                <div id="stats-content">
                    <!-- Statistics content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Review Details Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل التقييم</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="review-details">
                    <!-- Review details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#reviews-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.reviews.data") }}',
            data: function(d) {
                d.delivery_personnel_id = $('#personnel-filter').val();
                d.rating = $('#rating-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'order_number', name: 'order_number' },
            { data: 'customer_name', name: 'customer.name' },
            { data: 'delivery_personnel_name', name: 'deliveryPersonnel.name' },
            { data: 'rating_stars', name: 'delivery_rating' },
            { data: 'review_text', name: 'delivery_review' },
            { data: 'delivery_date_formatted', name: 'delivery_date' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[6, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        autoWidth: false,
        responsive: true
    });

    // Filter functionality
    $('#filter-btn').click(function() {
        table.draw();
    });

    $('#reset-btn').click(function() {
        $('#personnel-filter').val('');
        $('#rating-filter').val('');
        $('#date-from').val('');
        $('#date-to').val('');
        table.draw();
    });

    // Statistics functionality
    $('#stats-btn').click(function() {
        $('#statsModal').modal('show');
        loadStatistics();
    });

    $('#load-stats').click(function() {
        loadStatistics();
    });

    function loadStatistics() {
        var dateFrom = $('#stats-date-from').val();
        var dateTo = $('#stats-date-to').val();
        
        $.ajax({
            url: '{{ route("delivery.reviews.statistics") }}',
            method: 'GET',
            data: {
                date_from: dateFrom,
                date_to: dateTo
            },
            success: function(response) {
                if (response.success) {
                    displayStatistics(response.data);
                } else {
                    $('#stats-content').html('<div class="alert alert-danger">فشل في تحميل الإحصائيات</div>');
                }
            },
            error: function() {
                $('#stats-content').html('<div class="alert alert-danger">حدث خطأ أثناء تحميل الإحصائيات</div>');
            }
        });
    }

    function displayStatistics(data) {
        var statsHtml = `
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fa fa-star"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">إجمالي التقييمات</span>
                            <span class="info-box-number">${data.total_reviews}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fa fa-star-half-o"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">متوسط التقييم</span>
                            <span class="info-box-number">${parseFloat(data.average_rating).toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>توزيع التقييمات</h6>
                    <div class="rating-distribution">
        `;
        
        data.rating_distribution.forEach(function(rating) {
            var percentage = (rating.count / data.total_reviews * 100).toFixed(1);
            statsHtml += `
                <div class="rating-row mb-2">
                    <span>${rating.delivery_rating} نجوم</span>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${percentage}%">${rating.count} (${percentage}%)</div>
                    </div>
                </div>
            `;
        });
        
        statsHtml += `
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>أفضل موظفي التوصيل</h6>
                    <div class="top-personnel">
        `;
        
        data.top_rated_personnel.forEach(function(personnel, index) {
            statsHtml += `
                <div class="personnel-row mb-2">
                    <span class="badge badge-primary">${index + 1}</span>
                    <span>${personnel.delivery_personnel.name}</span>
                    <span class="float-right">
                        ${parseFloat(personnel.avg_rating).toFixed(2)} نجمة (${personnel.total_deliveries} توصيل)
                    </span>
                </div>
            `;
        });
        
        statsHtml += `
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <h6>أحدث التقييمات</h6>
                    <div class="recent-reviews">
        `;
        
        data.recent_reviews.forEach(function(review) {
            var stars = '';
            for (var i = 1; i <= 5; i++) {
                stars += i <= review.delivery_rating ? '<i class="fa fa-star text-warning"></i>' : '<i class="fa fa-star-o text-muted"></i>';
            }
            
            statsHtml += `
                <div class="review-item border-bottom pb-2 mb-2">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>#${review.order_number}</strong><br>
                            <small>${review.customer.name}</small>
                        </div>
                        <div class="col-md-3">
                            ${review.delivery_personnel.name}
                        </div>
                        <div class="col-md-3">
                            ${stars}
                        </div>
                        <div class="col-md-3">
                            <small>${review.delivery_review || 'لا يوجد تعليق'}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        statsHtml += `
                    </div>
                </div>
            </div>
        `;
        
        $('#stats-content').html(statsHtml);
    }

    // Review details functionality
    window.showReviewDetails = function(orderId) {
        $.ajax({
            url: '{{ route("delivery.reviews.show", ":id") }}'.replace(':id', orderId),
            method: 'GET',
            success: function(response) {
                $('#review-details').html(response);
                $('#reviewModal').modal('show');
            },
            error: function() {
                toastr.error('حدث خطأ أثناء تحميل تفاصيل التقييم');
            }
        });
    };

    // Set default dates (last 30 days)
    var today = new Date();
    var thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#stats-date-from').val(thirtyDaysAgo.toISOString().split('T')[0]);
    $('#stats-date-to').val(today.toISOString().split('T')[0]);
});
</script>
@endpush