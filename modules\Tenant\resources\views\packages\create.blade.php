@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/amazeui-datetimepicker/css/amazeui.datetimepicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/pickerjs/picker.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الباقات / إضافة باقة جديدة</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('packages.index') }}" class="btn btn-secondary">
                <i class="mdi mdi-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-lg-12 col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="main-content-label mg-b-5">
                    إضافة باقة جديدة
                </div>
                <p class="mg-b-20">قم بملء البيانات التالية لإضافة باقة اشتراك جديدة</p>
                
                <form action="{{ route('packages.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم الباقة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type">نوع الباقة <span class="text-danger">*</span></label>
                                <select class="form-control select2 @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">اختر نوع الباقة</option>
                                    <option value="basic" {{ old('type') == 'basic' ? 'selected' : '' }}>أساسي</option>
                                    <option value="premium" {{ old('type') == 'premium' ? 'selected' : '' }}>مميز</option>
                                    <option value="enterprise" {{ old('type') == 'enterprise' ? 'selected' : '' }}>مؤسسي</option>
                                    <option value="custom" {{ old('type') == 'custom' ? 'selected' : '' }}>مخصص</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price">السعر <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('price') is-invalid @enderror" 
                                           id="price" name="price" value="{{ old('price') }}" required>
                                    <div class="input-group-append">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="billing_cycle">دورة الفوترة <span class="text-danger">*</span></label>
                                <select class="form-control select2 @error('billing_cycle') is-invalid @enderror" 
                                        id="billing_cycle" name="billing_cycle" required>
                                    <option value="">اختر دورة الفوترة</option>
                                    <option value="monthly" {{ old('billing_cycle') == 'monthly' ? 'selected' : '' }}>شهري</option>
                                    <option value="yearly" {{ old('billing_cycle') == 'yearly' ? 'selected' : '' }}>سنوي</option>
                                    <option value="lifetime" {{ old('billing_cycle') == 'lifetime' ? 'selected' : '' }}>مدى الحياة</option>
                                </select>
                                @error('billing_cycle')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4" 
                                          placeholder="وصف مختصر للباقة">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Features Section -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mg-b-10">الميزات والحدود</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="max_branches">الحد الأقصى للفروع</label>
                                        <input type="number" min="1" 
                                               class="form-control @error('max_branches') is-invalid @enderror" 
                                               id="max_branches" name="max_branches" value="{{ old('max_branches', 1) }}">
                                        @error('max_branches')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="max_users">الحد الأقصى للمستخدمين</label>
                                        <input type="number" min="1" 
                                               class="form-control @error('max_users') is-invalid @enderror" 
                                               id="max_users" name="max_users" value="{{ old('max_users', 5) }}">
                                        @error('max_users')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="storage_limit">حد التخزين (GB)</label>
                                        <input type="number" min="1" 
                                               class="form-control @error('storage_limit') is-invalid @enderror" 
                                               id="storage_limit" name="storage_limit" value="{{ old('storage_limit', 10) }}">
                                        @error('storage_limit')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Features Checkboxes -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mg-b-10">الميزات المتاحة</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_pos" name="features[]" value="pos" 
                                               {{ in_array('pos', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_pos">
                                            نظام نقاط البيع (POS)
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_inventory" name="features[]" value="inventory" 
                                               {{ in_array('inventory', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_inventory">
                                            إدارة المخزون
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_reports" name="features[]" value="reports" 
                                               {{ in_array('reports', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_reports">
                                            التقارير المتقدمة
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_delivery" name="features[]" value="delivery" 
                                               {{ in_array('delivery', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_delivery">
                                            إدارة التوصيل
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_hr" name="features[]" value="hr" 
                                               {{ in_array('hr', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_hr">
                                            إدارة الموارد البشرية
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="has_api" name="features[]" value="api" 
                                               {{ in_array('api', old('features', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="has_api">
                                            واجهة برمجة التطبيقات (API)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control select2 @error('status') is-invalid @enderror" 
                                        id="status" name="status">
                                    <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sort_order">ترتيب العرض</label>
                                <input type="number" min="0" 
                                       class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mg-t-30">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-content-save"></i> حفظ الباقة
                        </button>
                        <a href="{{ route('packages.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-cancel"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/amazeui-datetimepicker/js/amazeui.datetimepicker.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.js')}}"></script>
<script src="{{URL::asset('assets/plugins/pickerjs/picker.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.js')}}"></script>
<script src="{{URL::asset('assets/js/form-elements.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var requiredFields = ['name', 'type', 'price', 'billing_cycle'];
        
        requiredFields.forEach(function(field) {
            var value = $('#' + field).val();
            if (!value || value.trim() === '') {
                isValid = false;
                $('#' + field).addClass('is-invalid');
            } else {
                $('#' + field).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // Price validation
    $('#price').on('input', function() {
        var value = parseFloat($(this).val());
        if (value < 0) {
            $(this).val(0);
        }
    });
    
    // Numeric fields validation
    $('input[type="number"]').on('input', function() {
        var value = parseInt($(this).val());
        var min = parseInt($(this).attr('min')) || 0;
        if (value < min) {
            $(this).val(min);
        }
    });
});
</script>
@endsection