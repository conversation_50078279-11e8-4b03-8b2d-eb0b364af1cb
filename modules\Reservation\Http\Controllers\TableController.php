<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Reservation\Services\TableService;
use Modules\Reservation\Http\Requests\CreateTableRequest;
use Modules\Reservation\Http\Requests\UpdateTableRequest;
use Modules\Reservation\Http\Resources\TableResource;
use Modules\Reservation\Http\Resources\TableCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class TableController extends Controller
{
    protected TableService $tableService;

    public function __construct(TableService $tableService)
    {
        $this->tableService = $tableService;
    }

    /**
     * Display a listing of tables.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $filters = $request->only([
                'area_id',
                'status',
                'section',
                'min_capacity',
                'max_capacity',
                'is_active'
            ]);
            
            // Add branch_id from authenticated user
            $filters['branch_id'] = $user->branch_id;

            $tables = $this->tableService->getAllTables($filters);

            return response()->json([
                'success' => true,
                'data' => new TableCollection($tables),
                'message' => 'Tables retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tables',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created table.
     */
    public function store(CreateTableRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id || !$user->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch or tenant'
                ], 400);
            }

            $data = $request->validated();
            $data['tenant_id'] = $user->tenant_id;
            $data['branch_id'] = $user->branch_id;
            
            $table = $this->tableService->createTable($data);

            return response()->json([
                'success' => true,
                'data' => new TableResource($table),
                'message' => 'Table created successfully'
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display the specified table.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $table = $this->tableService->getTableByIdForBranch($id, $user->branch_id);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new TableResource($table),
                'message' => 'Table retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified table.
     */
    public function update(UpdateTableRequest $request, int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $table = $this->tableService->updateTableForBranch($id, $request->validated(), $user->branch_id);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new TableResource($table),
                'message' => 'Table updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove the specified table.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $deleted = $this->tableService->deleteTableForBranch($id, $user->branch_id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Table deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete table',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get available tables for a specific time slot.
     */
    public function available(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $request->validate([
                'date' => 'required|date',
                'time' => 'required|date_format:H:i',
                'duration' => 'required|integer|min:30',
                'party_size' => 'required|integer|min:1',
                'area_id' => 'nullable|integer|exists:areas,id'
            ]);

            $availableTables = $this->tableService->getAvailableTables(
                $user->branch_id,
                $request->date,
                $request->time,
                $request->duration,
                $request->party_size,
                $request->area_id
            );

            return response()->json([
                'success' => true,
                'data' => new TableCollection($availableTables),
                'message' => 'Available tables retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available tables',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update table status.
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|string|in:available,occupied,reserved,cleaning,out_of_order'
            ]);

            $table = $this->tableService->updateTableStatus($id, $request->get('status'));

            return response()->json([
                'success' => true,
                'data' => new TableResource($table),
                'message' => 'Table status updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update table status',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Generate QR code for table.
     */
    public function generateQR(int $id): JsonResponse
    {
        try {
            $qrData = $this->tableService->generateTableQR($id);

            return response()->json([
                'success' => true,
                'data' => $qrData,
                'message' => 'QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get table by QR code.
     */
    public function getByQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'qr_code' => 'required|string'
            ]);

            $table = $this->tableService->getTableByQR($request->get('qr_code'));

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found for this QR code'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new TableResource($table),
                'message' => 'Table retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table by QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get table occupancy statistics.
     */
    public function occupancy(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $request->validate([
                'date' => 'nullable|date',
                'area_id' => 'nullable|integer|exists:areas,id'
            ]);

            $occupancyData = $this->tableService->getTableOccupancy(
                $user->branch_id,
                $request->date ?? now()->toDateString(),
                $request->area_id
            );

            return response()->json([
                'success' => true,
                'data' => $occupancyData,
                'message' => 'Table occupancy data retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table occupancy data',
                'error' => $e->getMessage()
            ], 400);
        }
    }
}