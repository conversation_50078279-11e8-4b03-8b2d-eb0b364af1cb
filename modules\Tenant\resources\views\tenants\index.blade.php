@extends('layouts.master')

@section('title', 'إدارة المستأجرين')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">إدارة المستأجرين</h3>
                    <a href="{{ route('tenants.create') }}" class="btn btn-primary">
                        <i class="fa fa-plus"></i> إضافة مستأجر جديد
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="statusFilter">الحالة</label>
                            <select id="statusFilter" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                                <option value="trial">تجريبي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="businessTypeFilter">نوع النشاط</label>
                            <select id="businessTypeFilter" class="form-control">
                                <option value="">جميع الأنواع</option>
                                <option value="restaurant">مطعم</option>
                                <option value="cafe">مقهى</option>
                                <option value="fast_food">وجبات سريعة</option>
                                <option value="bakery">مخبز</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="countryFilter">الدولة</label>
                            <select id="countryFilter" class="form-control">
                                <option value="">جميع الدول</option>
                                @foreach($countries as $country)
                                    <option value="{{ $country->id }}">{{ $country->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" id="resetFilters" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="tenants-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الكود</th>
                                    <th>نوع النشاط</th>
                                    <th>الدولة</th>
                                    <th>معلومات الاتصال</th>
                                    <th>الحالة</th>
                                    <th>الفروع</th>
                                    <th>المستخدمين</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تغيير الحالة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" id="confirmStatusChange" class="btn btn-primary">تأكيد</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المستأجر؟ هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" id="confirmDelete" class="btn btn-danger">حذف</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .contact-info div {
        margin-bottom: 2px;
        font-size: 0.9em;
    }
    .contact-info i {
        width: 15px;
        margin-right: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    let table = $('#tenants-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("tenants.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.business_type = $('#businessTypeFilter').val();
                d.country_id = $('#countryFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'business_type', name: 'business_type' },
            { data: 'country_name', name: 'country.name' },
            { data: 'contact_info', name: 'contact_info', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'branches_count', name: 'branches_count', orderable: false, searchable: false },
            { data: 'users_count', name: 'users_count', orderable: false, searchable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[9, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
    });

    // Filter change handlers
    $('#statusFilter, #businessTypeFilter, #countryFilter').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#statusFilter, #businessTypeFilter, #countryFilter').val('');
        table.draw();
    });

    // Status change functionality
    let currentTenantId = null;
    let currentAction = null;

    window.changeStatus = function(tenantId, action) {
        currentTenantId = tenantId;
        currentAction = action;
        
        let messages = {
            'activate': 'هل أنت متأكد من تفعيل هذا المستأجر؟',
            'deactivate': 'هل أنت متأكد من إلغاء تفعيل هذا المستأجر؟',
            'suspend': 'هل أنت متأكد من تعليق هذا المستأجر؟'
        };
        
        $('#statusMessage').text(messages[action]);
        $('#statusModal').modal('show');
    };

    $('#confirmStatusChange').click(function() {
        if (currentTenantId && currentAction) {
            $.ajax({
                url: `/admin/tenants/${currentTenantId}/${currentAction}`,
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#statusModal').modal('hide');
                    table.draw();
                    showToast('تم تغيير حالة المستأجر بنجاح', 'success');
                },
                error: function(xhr) {
                    $('#statusModal').modal('hide');
                    showToast('حدث خطأ أثناء تغيير الحالة', 'error');
                }
            });
        }
    });

    // Delete functionality
    let deleteId = null;

    window.deleteTenant = function(tenantId) {
        deleteId = tenantId;
        $('#deleteModal').modal('show');
    };

    $('#confirmDelete').click(function() {
        if (deleteId) {
            $.ajax({
                url: `/admin/tenants/${deleteId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#deleteModal').modal('hide');
                    table.draw();
                    showToast('تم حذف المستأجر بنجاح', 'success');
                },
                error: function(xhr) {
                    $('#deleteModal').modal('hide');
                    let message = xhr.responseJSON?.message || 'حدث خطأ أثناء الحذف';
                    showToast(message, 'error');
                }
            });
        }
    });

    // Toast notification function
    function showToast(message, type) {
        // You can implement your preferred toast notification here
        // For now, using simple alert
        if (type === 'success') {
            alert('✓ ' + message);
        } else {
            alert('✗ ' + message);
        }
    }
});
</script>
@endpush
