<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('menu_id')->nullable()->constrained('menus')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('menu_categories');
            $table->string('name')->nullable();
            $table->string('code', 50)->nullable();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable()->comment('For receipts/KOT');
            $table->decimal('base_price', 10, 2)->nullable();
            $table->decimal('cost_price', 10, 2)->nullable()->comment('For profit analysis');
            $table->json('image_urls')->nullable()->comment('Multiple images');
            $table->integer('prep_time_minutes')->nullable()->default(0);
            $table->integer('calories')->nullable();
            $table->json('nutritional_info')->nullable()->comment('Detailed nutrition data');
            $table->json('allergens')->nullable()->comment('Array of allergen codes');
            $table->json('dietary_info')->nullable()->comment('vegan, vegetarian, gluten-free, etc.');
            $table->foreignId('recipe_id')->nullable()->constrained('recipes')->onDelete('set null');
            $table->string('barcode', 100)->nullable();
            $table->string('sku', 100)->nullable();
            $table->boolean('is_active')->nullable()->default(true);
            $table->boolean('is_featured')->nullable()->default(false);
            $table->boolean('is_spicy')->nullable()->default(false);
            $table->integer('spice_level')->nullable()->comment('1-5 scale');
            $table->integer('sort_order')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique(['menu_id', 'code']);
            
            // Performance indexes
            $table->index(['menu_id', 'is_active', 'sort_order']);
            $table->index(['category_id', 'is_active']);
            $table->index(['is_featured', 'is_active']);
            $table->index(['barcode']);
            $table->index(['sku']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_items');
    }
};