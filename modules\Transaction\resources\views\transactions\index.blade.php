@extends('layouts.master')

@section('title', 'Transaction Management')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.transaction-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}
.transaction-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}
.transaction-status {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}
.status-due { background-color: #ffeaa7; color: #2d3436; }
.status-partially_paid { background-color: #fd79a8; color: #ffffff; }
.status-paid { background-color: #00b894; color: #ffffff; }
.status-overpaid { background-color: #74b9ff; color: #ffffff; }
.status-refunded { background-color: #6c5ce7; color: #ffffff; }
.status-cancelled { background-color: #e17055; color: #ffffff; }

.payment-sidebar {
    position: sticky;
    top: 20px;
    height: calc(100vh - 40px);
    overflow-y: auto;
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
}

.payment-form {
    background: #f8f9fc;
    padding: 1.5rem;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
}

.transaction-summary {
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.amount-display {
    font-size: 1.25rem;
    font-weight: 600;
}

.amount-due { color: #e74c3c; }
.amount-paid { color: #27ae60; }
.amount-total { color: #2c3e50; }

.transaction-grid {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 1.5rem;
}

@media (max-width: 768px) {
    .transaction-grid {
        grid-template-columns: 1fr;
    }
    .payment-sidebar {
        position: relative;
        height: auto;
    }
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Transaction Management</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ Transactions</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button class="btn btn-info btn-icon ml-2" id="refresh-transactions">
                <i class="mdi mdi-refresh"></i>
            </button>
              <!-- Add Transaction Button -->
         
        </div>
          <!-- Add Transaction Button -->
            <div class="mb-3">
                <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#addTransactionModal">
                    <i class="fas fa-plus"></i> Add New Transaction
                </button>
            </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-primary-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Total Transactions</h6>
                            <h2 class="text-white mb-0 number-font" id="total-transactions">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-receipt text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-danger-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Due Amount</h6>
                            <h2 class="text-white mb-0 number-font" id="total-due">$0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-exclamation-triangle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-success-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Paid Amount</h6>
                            <h2 class="text-white mb-0 number-font" id="total-paid">$0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-check-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-warning-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Total Amount</h6>
                            <h2 class="text-white mb-0 number-font" id="total-amount">$0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-dollar-sign text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


        <!-- Transactions List -->
        <div class="transactions-main">
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select class="form-control select2" id="status-filter">
                                <option value="">All Statuses</option>
                                <option value="due">Due</option>
                                <option value="partially_paid">Partially Paid</option>
                                <option value="paid">Paid</option>
                                <option value="overpaid">Overpaid</option>
                                <option value="refunded">Refunded</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date-from-filter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date-to-filter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-secondary" id="clear-filters">Clear</button>
                                <button class="btn btn-primary" id="apply-filters">Apply</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table key-buttons text-md-nowrap" id="transactions-table" data-page-length='25'>
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">Transaction #</th>
                                    <th class="border-bottom-0">Order #</th>
                                    <th class="border-bottom-0">Status</th>
                                    <th class="border-bottom-0">Total</th>
                                    <th class="border-bottom-0">Paid</th>
                                    <th class="border-bottom-0">Due</th>
                                    <th class="border-bottom-0">Date</th>
                                    <th class="border-bottom-0">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

 
</div>

<!-- Add Transaction Modal -->
<div class="modal fade" id="addTransactionModal" tabindex="-1" role="dialog" aria-labelledby="addTransactionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTransactionModalLabel">إضافة معاملة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addTransactionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="order_id">رقم الطلب</label>
                                <select class="form-control select2" id="order_id" name="order_id" required>
                                    <option value="">اختر الطلب</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transaction_type">نوع المعاملة</label>
                                <select class="form-control" id="transaction_type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="sale">بيع</option>
                                    <option value="refund">استرداد</option>
                                    <option value="adjustment">تعديل</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="total_amount">المبلغ الإجمالي</label>
                                <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="paid_amount">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paid_amount" name="paid_amount" step="0.01" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transaction_status">الحالة</label>
                                <select class="form-control" id="transaction_status" name="status" required>
                                    <option value="pending">معلق</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transaction_date">تاريخ المعاملة</label>
                                <input type="datetime-local" class="form-control" id="transaction_date" name="transaction_date" value="{{ date('Y-m-d\TH:i') }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ المعاملة</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    let table;
    let selectedTransaction = null;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#transactions-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("api.transactions.index") }}',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.date_from = $('#date-from-filter').val();
                    d.date_to = $('#date-to-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'transaction_number', name: 'transaction_number' },
                { data: 'order_number', name: 'order.order_number' },
                { data: 'status_badge', name: 'status' },
                { data: 'formatted_total', name: 'total_amount' },
                { data: 'formatted_paid', name: 'paid_amount' },
                { data: 'formatted_due', name: 'due_amount' },
                { data: 'formatted_date', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[7, 'desc']] // Order by date descending
        });
    }

    // Load payment methods
    function loadPaymentMethods() {
        $.ajax({
            url: '{{ route("api.payments.payment-methods") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    let options = '<option value="">Select Payment Method</option>';
                    let quickButtons = '';

                    response.data.forEach(function(method) {
                        if (method.is_active) {
                            options += `<option value="${method.id}" data-type="${method.type}">${method.name}</option>`;
                            quickButtons += `<button type="button" class="btn btn-outline-primary btn-sm mb-2 quick-payment-btn" data-method-id="${method.id}" data-method-name="${method.name}" data-method-type="${method.type}">${method.name}</button>`;
                        }
                    });

                    $('#payment-method').html(options);
                    $('#quick-payment-methods').html(quickButtons);
                }
            },
            error: function(xhr) {
                console.error('Error loading payment methods:', xhr);
            }
        });
    }

    // Handle transaction selection
    $(document).on('click', '.select-transaction', function() {
        const transactionId = $(this).data('transaction-id');

        $.ajax({
            url: `{{ url('api/transactions') }}/${transactionId}`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    selectedTransaction = response.data;
                    displayTransactionDetails(selectedTransaction);
                    showPaymentForm();
                }
            },
            error: function(xhr) {
                console.error('Error loading transaction details:', xhr);
                swal("Error!", "Failed to load transaction details", "error");
            }
        });
    });

    // Display transaction details in sidebar
    function displayTransactionDetails(transaction) {
        $('#selected-transaction-number').text(transaction.transaction_number);
        $('#selected-order-number').text(transaction.order ? transaction.order.order_number : 'N/A');
        $('#selected-total-amount').text('$' + parseFloat(transaction.total_amount).toFixed(2));
        $('#selected-paid-amount').text('$' + parseFloat(transaction.paid_amount).toFixed(2));
        $('#selected-due-amount').text('$' + parseFloat(transaction.due_amount).toFixed(2));
        $('#selected-transaction-id').val(transaction.id);

        // Set max payment amount to due amount
        $('#payment-amount').attr('max', transaction.due_amount);
        $('#payment-amount').val(transaction.due_amount);

        $('#transaction-summary').show();
    }

    // Show payment form
    function showPaymentForm() {
        if (selectedTransaction && selectedTransaction.due_amount > 0) {
            $('#payment-form').show();
        } else {
            $('#payment-form').hide();
            if (selectedTransaction && selectedTransaction.due_amount <= 0) {
                swal("Info", "This transaction is already fully paid", "info");
            }
        }
    }

    // Handle payment method change
    $('#payment-method').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const methodType = selectedOption.data('type');

        if (methodType === 'cash') {
            $('#cash-received-group').show();
            $('#cash-received').attr('required', true);
        } else {
            $('#cash-received-group').hide();
            $('#cash-received').attr('required', false);
        }
    });

    // Quick payment method buttons
    $(document).on('click', '.quick-payment-btn', function() {
        if (!selectedTransaction) {
            swal("Warning", "Please select a transaction first", "warning");
            return;
        }

        const methodId = $(this).data('method-id');
        const methodName = $(this).data('method-name');
        const methodType = $(this).data('method-type');

        $('#payment-method').val(methodId).trigger('change');
        $('#payment-amount').val(selectedTransaction.due_amount);

        if (methodType === 'cash') {
            $('#cash-received').val(selectedTransaction.due_amount);
        }
    });

    // Process payment form submission
    $('#process-payment-form').on('submit', function(e) {
        e.preventDefault();

        if (!selectedTransaction) {
            swal("Warning", "Please select a transaction first", "warning");
            return;
        }

        const formData = new FormData(this);
        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();

        // Show loading state
        submitButton.prop('disabled', true).text('Processing...');

        $.ajax({
            url: '{{ route("api.payments.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal("Success!", "Payment processed successfully", "success");

                    // Reset form and hide payment form
                    $('#process-payment-form')[0].reset();
                    $('#payment-form').hide();
                    $('#transaction-summary').hide();
                    selectedTransaction = null;

                    // Refresh table and statistics
                    table.draw();
                    loadStatistics();
                } else {
                    swal("Error!", response.message || "Failed to process payment", "error");
                }
            },
            error: function(xhr) {
                console.error('Error processing payment:', xhr);
                let errorMessage = "Failed to process payment";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                swal("Error!", errorMessage, "error");
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Cancel payment
    $('#cancel-payment').on('click', function() {
        $('#process-payment-form')[0].reset();
        $('#payment-form').hide();
        $('#transaction-summary').hide();
        selectedTransaction = null;
    });

    // Initialize table
    initializeDataTable();

    // Load payment methods
    loadPaymentMethods();

    // Filter functionality
    $('#apply-filters').on('click', function() {
        table.draw();
    });

    $('#clear-filters').on('click', function() {
        $('#status-filter, #date-from-filter, #date-to-filter').val('');
        $('.select2').trigger('change');
        table.draw();
    });

    $('#refresh-transactions').on('click', function() {
        table.draw();
        loadStatistics();
    });

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("api.transactions.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    $('#total-transactions').text(response.data.total_transactions || 0);
                    $('#total-due').text('$' + (response.data.total_due || 0).toFixed(2));
                    $('#total-paid').text('$' + (response.data.total_paid || 0).toFixed(2));
                    $('#total-amount').text('$' + (response.data.total_amount || 0).toFixed(2));
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Initial statistics load
    loadStatistics();

    // Add Transaction Modal functionality
    $('#add-transaction-btn').on('click', function() {
        $('#addTransactionModal').modal('show');
        loadOrders();
    });

    // Load orders for the dropdown
    function loadOrders() {
        $.ajax({
            url: '{{ route("api.orders.index") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    let options = '<option value="">اختر الطلب</option>';
                    response.data.forEach(function(order) {
                        options += `<option value="${order.id}">${order.order_number} - ${order.customer_name || 'عميل غير محدد'}</option>`;
                    });
                    $('#order_id').html(options);
                }
            },
            error: function(xhr) {
                console.error('Error loading orders:', xhr);
            }
        });
    }

    // Handle Add Transaction form submission
    $('#addTransactionForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();

        // Show loading state
        submitButton.prop('disabled', true).text('جاري الحفظ...');

        $.ajax({
            url: '{{ route("api.transactions.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal("نجح!", "تم إنشاء المعاملة بنجاح", "success");
                    
                    // Reset form and close modal
                    $('#addTransactionForm')[0].reset();
                    $('#addTransactionModal').modal('hide');
                    
                    // Refresh table and statistics
                    table.draw();
                    loadStatistics();
                } else {
                    swal("خطأ!", response.message || "فشل في إنشاء المعاملة", "error");
                }
            },
            error: function(xhr) {
                console.error('Error creating transaction:', xhr);
                let errorMessage = "فشل في إنشاء المعاملة";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Reset modal when closed
    $('#addTransactionModal').on('hidden.bs.modal', function() {
        $('#addTransactionForm')[0].reset();
        $('#order_id').html('<option value="">اختر الطلب</option>');
    });
});
</script>
@endsection
