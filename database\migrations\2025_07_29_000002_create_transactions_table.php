<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->string('transaction_number', 50)->unique()->comment('Unique transaction number');
            $table->decimal('total_amount', 10, 2)->comment('Total transaction amount');
            $table->decimal('paid_amount', 10, 2)->default(0)->comment('Amount already paid');
            $table->decimal('due_amount', 10, 2)->default(0)->comment('Amount still due');
            $table->enum('status', ['due', 'partially_paid', 'paid', 'overpaid', 'refunded', 'cancelled'])
                ->default('due')
                ->comment('Transaction payment status');
            $table->decimal('tax_amount', 10, 2)->default(0)->comment('Tax amount');
            $table->decimal('discount_amount', 10, 2)->default(0)->comment('Discount amount');
            $table->decimal('service_charge', 10, 2)->default(0)->comment('Service charge amount');
            $table->text('notes')->nullable()->comment('Transaction notes');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['order_id', 'status']);
            $table->index('status');
            $table->index('transaction_number');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
