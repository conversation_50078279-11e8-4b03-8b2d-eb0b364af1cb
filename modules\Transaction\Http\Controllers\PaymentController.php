<?php

namespace Modules\Transaction\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Transaction\Services\PaymentService;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StorePaymentRequest;
use Modules\Transaction\Http\Resources\PaymentResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class PaymentController extends Controller
{
    protected PaymentService $paymentService;
    protected TransactionService $transactionService;

    public function __construct(PaymentService $paymentService, TransactionService $transactionService)
    {
        $this->paymentService = $paymentService;
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of payments.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Check if this is a DataTables request
            if ($request->has('draw')) {
                return $this->handleDataTablesRequest($request);
            }

            $filters = $request->only([
                'status', 'transaction_id', 'payment_method_id', 'date_from', 'date_to', 'search',
                'sort_by', 'sort_direction', 'per_page'
            ]);

            $payments = $this->paymentService->getAllPayments($filters);

            return response()->json([
                'success' => true,
                'data' => PaymentResource::collection($payments->items()),
                'meta' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payments',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle DataTables server-side processing request.
     */
    private function handleDataTablesRequest(Request $request): JsonResponse
    {
        try {
            $draw = $request->get('draw');
            $start = $request->get('start', 0);
            $length = $request->get('length', 10);
            $search = $request->get('search.value', '');

            // Get order information
            $orderColumn = $request->get('order.0.column', 0);
            $orderDir = $request->get('order.0.dir', 'desc');

            // Column mapping for ordering
            $columns = ['id', 'transaction.transaction_number', 'transaction.order.order_number', 'paymentMethod.name', 'amount', 'status', 'reference_number', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'created_at';

            $filters = [
                'status' => $request->get('status'),
                'payment_method_id' => $request->get('payment_method_id'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $search,
                'sort_by' => $orderBy,
                'sort_direction' => $orderDir,
                'per_page' => $length,
                'page' => floor($start / $length) + 1,
            ];

            $payments = $this->paymentService->getAllPayments($filters);

            $data = [];
            foreach ($payments->items() as $index => $payment) {
                $data[] = [
                    'DT_RowIndex' => $start + $index + 1,
                    'transaction_number' => $payment->transaction ? $payment->transaction->transaction_number : 'N/A',
                    'order_number' => $payment->transaction && $payment->transaction->order ? $payment->transaction->order->order_number : 'N/A',
                    'payment_method_badge' => '<span class="payment-method method-' . ($payment->paymentMethod ? $payment->paymentMethod->code : 'unknown') . '">' . ($payment->paymentMethod ? $payment->paymentMethod->name : 'Unknown') . '</span>',
                    'formatted_amount' => '$' . number_format($payment->amount, 2),
                    'status_badge' => '<span class="payment-status status-' . $payment->status . '">' . ucfirst(str_replace('_', ' ', $payment->status)) . '</span>',
                    'reference_number' => $payment->reference_number ?: '-',
                    'formatted_date' => $payment->created_at->format('M d, Y H:i'),
                    'action' => $this->getPaymentActions($payment),
                ];
            }

            return response()->json([
                'draw' => intval($draw),
                'recordsTotal' => $payments->total(),
                'recordsFiltered' => $payments->total(),
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'draw' => intval($request->get('draw', 1)),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get action buttons for payment.
     */
    private function getPaymentActions($payment): string
    {
        $actions = '';

        if ($payment->status === 'completed') {
            $actions .= '<button class="btn btn-sm btn-warning refund-payment" data-payment-id="' . $payment->id . '">Refund</button> ';
        }

        if (in_array($payment->status, ['pending', 'completed'])) {
            $actions .= '<button class="btn btn-sm btn-danger cancel-payment" data-payment-id="' . $payment->id . '">Cancel</button>';
        }

        return $actions ?: '<span class="text-muted">No actions</span>';
    }

    /**
     * Store a newly created payment.
     */
    public function store(StorePaymentRequest $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($request->transaction_id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $payment = $this->paymentService->processPayment($transaction, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => new PaymentResource($payment),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not found',
                ], Response::HTTP_NOT_FOUND);
            }

            return response()->json([
                'success' => true,
                'data' => new PaymentResource($payment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Cancel the specified payment.
     */
    public function cancel(int $id, Request $request): JsonResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $reason = $request->input('reason', 'Payment cancelled by user');
            $cancelledPayment = $this->paymentService->cancelPayment($payment, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully',
                'data' => new PaymentResource($cancelledPayment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Refund the specified payment.
     */
    public function refund(int $id, Request $request): JsonResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $refundAmount = $request->input('refund_amount');
            $reason = $request->input('reason', 'Payment refunded by user');
            
            $refundPayment = $this->paymentService->refundPayment($payment, $refundAmount, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'data' => new PaymentResource($refundPayment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to refund payment',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get payment statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['date_from', 'date_to']);
            $statistics = $this->paymentService->getPaymentStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get active payment methods.
     */
    public function paymentMethods(): JsonResponse
    {
        try {
            $paymentMethods = $this->paymentService->getPaymentMethods();

            return response()->json([
                'success' => true,
                'data' => $paymentMethods->map(function ($method) {
                    return [
                        'id' => $method->id,
                        'name' => $method->name,
                        'code' => $method->code,
                        'description' => $method->description,
                        'is_cash' => $method->isCash(),
                        'is_card' => $method->isCard(),
                        'is_digital' => $method->isDigital(),
                    ];
                }),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get payments for a specific transaction.
     */
    public function byTransaction(int $transactionId): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($transactionId);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $payments = $transaction->payments()->with(['paymentMethod', 'processedBy'])->get();

            return response()->json([
                'success' => true,
                'data' => PaymentResource::collection($payments),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payments',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
