<?php

namespace Modules\Customer\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'branch_id' => $this->branch_id,
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->first_name . ($this->last_name ? ' ' . $this->last_name : ''),
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth?->format('Y-m-d'),
            'gender' => $this->gender,
            'address' => $this->address,
            'city' => $this->city,
            'postal_code' => $this->postal_code,
            'loyalty_points' => (float) $this->loyalty_points,
            'preferences' => $this->preferences,
            'notes' => $this->notes,
            'is_active' => $this->is_active,
            'last_visit_at' => $this->last_visit_at?->format('Y-m-d H:i:s'),
            'orders_count' => $this->whenLoaded('orders', function () {
                return $this->orders->count();
            }),
            'loyalty_transactions' => $this->whenLoaded('loyaltyTransactions', function () {
                return LoyaltyTransactionResource::collection($this->loyaltyTransactions);
            }),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}