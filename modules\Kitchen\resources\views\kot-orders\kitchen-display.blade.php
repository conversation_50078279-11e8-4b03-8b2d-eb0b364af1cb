@extends('layouts.master')

@section('title', 'Kitchen Display')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<style>
.kitchen-display {
    background: #1a1a1a;
    color: #fff;
    min-height: 100vh;
    padding: 1rem;
}

.kot-display-card {
    background: #2d2d2d;
    border: 2px solid #444;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.kot-display-card.priority-urgent {
    border-color: #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.kot-display-card.priority-high {
    border-color: #ffc107;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.kot-display-card.priority-normal {
    border-color: #007bff;
}

.kot-display-card.priority-low {
    border-color: #6c757d;
}

.kot-display-card.status-preparing {
    background: #1e3a5f;
}

.kot-display-card.status-ready {
    background: #1e5f3a;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.kot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 1px solid #444;
    padding-bottom: 0.5rem;
}

.kot-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffc107;
}

.kot-time {
    font-size: 1.25rem;
    font-weight: bold;
}

.kot-time.overdue {
    color: #dc3545;
    animation: blink 1s infinite;
}

.kot-time.warning {
    color: #ffc107;
}

.kot-time.normal {
    color: #28a745;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

.kot-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.kot-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #444;
}

.kot-item:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 500;
    flex: 1;
}

.item-quantity {
    background: #007bff;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 1rem;
}

.kitchen-selector {
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.display-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.auto-refresh-indicator {
    color: #28a745;
    font-size: 0.875rem;
}

.auto-refresh-indicator.refreshing {
    color: #ffc107;
}

.kot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
}

.empty-state {
    text-align: center;
    color: #6c757d;
    font-size: 1.25rem;
    margin-top: 3rem;
}
</style>
@endsection

@section('content')
<div class="kitchen-display">
    <!-- Kitchen Selector -->
    <div class="kitchen-selector">
        <div class="row align-items-center">
            <div class="col-md-4">
                <label for="kitchen-select" class="text-white">Select Kitchen:</label>
                <select class="form-control select2" id="kitchen-select">
                    <option value="">All Kitchens</option>
                    @foreach($kitchens as $kitchenOption)
                        <option value="{{ $kitchenOption->id }}" 
                                {{ $kitchen && $kitchen->id == $kitchenOption->id ? 'selected' : '' }}>
                            {{ $kitchenOption->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-4">
                <div class="display-controls">
                    <div class="auto-refresh-indicator" id="refresh-indicator">
                        <i class="fas fa-sync-alt"></i> Auto-refresh: ON
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-right">
                <div class="text-white">
                    <div id="current-time" class="h4"></div>
                    <div class="small">Last updated: <span id="last-updated">--</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- KOT Display Grid -->
    <div class="kot-grid" id="kot-grid">
        <!-- KOTs will be loaded here -->
    </div>

    <!-- Empty State -->
    <div class="empty-state" id="empty-state" style="display: none;">
        <i class="fas fa-utensils fa-3x mb-3"></i>
        <div>No active KOTs</div>
        <div class="small">All orders are completed or no orders assigned to this kitchen</div>
    </div>
</div>
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4'
    });

    let refreshInterval;
    let isRefreshing = false;

    // Update current time
    function updateCurrentTime() {
        const now = new Date();
        $('#current-time').text(now.toLocaleTimeString());
    }

    // Load active KOTs
    function loadActiveKots() {
        if (isRefreshing) return;
        
        isRefreshing = true;
        $('#refresh-indicator').addClass('refreshing').html('<i class="fas fa-sync-alt fa-spin"></i> Refreshing...');

        const kitchenId = $('#kitchen-select').val();

        $.ajax({
            url: '{{ route("api.kot-orders.active") }}',
            method: 'GET',
            data: { kitchen_id: kitchenId },
            success: function(response) {
                if (response.success) {
                    displayKots(response.data);
                    $('#last-updated').text(new Date().toLocaleTimeString());
                }
            },
            error: function(xhr) {
                console.error('Error loading KOTs:', xhr);
            },
            complete: function() {
                isRefreshing = false;
                $('#refresh-indicator').removeClass('refreshing').html('<i class="fas fa-sync-alt"></i> Auto-refresh: ON');
            }
        });
    }

    // Display KOTs
    function displayKots(kots) {
        const kotGrid = $('#kot-grid');
        const emptyState = $('#empty-state');

        if (kots.length === 0) {
            kotGrid.empty();
            emptyState.show();
            return;
        }

        emptyState.hide();
        kotGrid.empty();

        kots.forEach(function(kot) {
            const kotCard = createKotCard(kot);
            kotGrid.append(kotCard);
        });
    }

    // Create KOT card HTML
    function createKotCard(kot) {
        const priorityClass = `priority-${kot.priority}`;
        const statusClass = `status-${kot.status}`;
        
        let timeClass = 'normal';
        if (kot.is_overdue) {
            timeClass = 'overdue';
        } else if (kot.remaining_time !== null && kot.remaining_time <= 5) {
            timeClass = 'warning';
        }

        return `
            <div class="kot-display-card ${priorityClass} ${statusClass}">
                <div class="kot-header">
                    <div class="kot-number">#${kot.kot_number}</div>
                    <div class="kot-time ${timeClass}">${kot.elapsed_time} min</div>
                </div>
                <div class="kot-info mb-2">
                    <div class="small text-muted">Order: ${kot.order_number}</div>
                    <div class="small text-muted">Created: ${kot.created_at}</div>
                    <div class="small">
                        <span class="badge badge-${getPriorityBadgeClass(kot.priority)}">${kot.priority.toUpperCase()}</span>
                        <span class="badge badge-${getStatusBadgeClass(kot.status)} ml-1">${kot.status.toUpperCase()}</span>
                    </div>
                </div>
                <div class="kot-items-info">
                    <div class="small text-muted">${kot.items_count} item(s)</div>
                </div>
            </div>
        `;
    }

    // Get priority badge class
    function getPriorityBadgeClass(priority) {
        const classes = {
            'low': 'secondary',
            'normal': 'primary',
            'high': 'warning',
            'urgent': 'danger'
        };
        return classes[priority] || 'secondary';
    }

    // Get status badge class
    function getStatusBadgeClass(status) {
        const classes = {
            'pending': 'warning',
            'preparing': 'info',
            'ready': 'success',
            'completed': 'primary',
            'cancelled': 'danger'
        };
        return classes[status] || 'secondary';
    }

    // Handle kitchen selection change
    $('#kitchen-select').on('change', function() {
        loadActiveKots();
    });

    // Start auto-refresh
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            loadActiveKots();
            updateCurrentTime();
        }, 10000); // Refresh every 10 seconds
    }

    // Initial load
    updateCurrentTime();
    loadActiveKots();
    startAutoRefresh();

    // Update time every second
    setInterval(updateCurrentTime, 1000);

    // Handle page visibility change to pause/resume refresh
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(refreshInterval);
        } else {
            startAutoRefresh();
            loadActiveKots();
        }
    });
});
</script>
@endsection
