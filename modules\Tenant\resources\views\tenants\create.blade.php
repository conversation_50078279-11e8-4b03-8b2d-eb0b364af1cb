@extends('layouts.master')

@section('title', 'إضافة مستأجر جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">إضافة مستأجر جديد</h3>
                    <a href="{{ route('tenants.index') }}" class="btn btn-secondary">
                        <i class="fa fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
                
                <form action="{{ route('tenants.store') }}" method="POST" id="tenantForm">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>
                                
                                <div class="form-group">
                                    <label for="name">اسم المستأجر <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="code">كود المستأجر</label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                           id="code" name="code" value="{{ old('code') }}" 
                                           placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="business_type">نوع النشاط <span class="text-danger">*</span></label>
                                    <select class="form-control @error('business_type') is-invalid @enderror" 
                                            id="business_type" name="business_type" required>
                                        <option value="">اختر نوع النشاط</option>
                                        <option value="restaurant" {{ old('business_type') == 'restaurant' ? 'selected' : '' }}>مطعم</option>
                                        <option value="cafe" {{ old('business_type') == 'cafe' ? 'selected' : '' }}>مقهى</option>
                                        <option value="fast_food" {{ old('business_type') == 'fast_food' ? 'selected' : '' }}>وجبات سريعة</option>
                                        <option value="bakery" {{ old('business_type') == 'bakery' ? 'selected' : '' }}>مخبز</option>
                                        <option value="other" {{ old('business_type') == 'other' ? 'selected' : '' }}>أخرى</option>
                                    </select>
                                    @error('business_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="country_id">الدولة <span class="text-danger">*</span></label>
                                    <select class="form-control @error('country_id') is-invalid @enderror" 
                                            id="country_id" name="country_id" required>
                                        <option value="">اختر الدولة</option>
                                        @foreach($countries as $country)
                                            <option value="{{ $country->id }}" {{ old('country_id') == $country->id ? 'selected' : '' }}>
                                                {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('country_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات الاتصال</h5>
                                
                                <div class="form-group">
                                    <label for="primary_contact_name">اسم جهة الاتصال الرئيسية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('primary_contact_name') is-invalid @enderror" 
                                           id="primary_contact_name" name="primary_contact_name" value="{{ old('primary_contact_name') }}" required>
                                    @error('primary_contact_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="contact_email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                           id="contact_email" name="contact_email" value="{{ old('contact_email') }}" required>
                                    @error('contact_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="contact_phone">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror" 
                                           id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}" required>
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="business_address">عنوان النشاط <span class="text-danger">*</span></label>
                                    <textarea class="form-control @error('business_address') is-invalid @enderror" 
                                              id="business_address" name="business_address" rows="3" required>{{ old('business_address') }}</textarea>
                                    @error('business_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Business Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات النشاط</h5>
                                
                                <div class="form-group">
                                    <label for="tax_number">الرقم الضريبي</label>
                                    <input type="text" class="form-control @error('tax_number') is-invalid @enderror" 
                                           id="tax_number" name="tax_number" value="{{ old('tax_number') }}">
                                    @error('tax_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="business_license">رقم الترخيص التجاري</label>
                                    <input type="text" class="form-control @error('business_license') is-invalid @enderror" 
                                           id="business_license" name="business_license" value="{{ old('business_license') }}">
                                    @error('business_license')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="website_url">موقع الويب</label>
                                    <input type="url" class="form-control @error('website_url') is-invalid @enderror" 
                                           id="website_url" name="website_url" value="{{ old('website_url') }}" 
                                           placeholder="https://example.com">
                                    @error('website_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- System Settings -->
                            <div class="col-md-6">
                                <h5 class="mb-3">إعدادات النظام</h5>
                                
                                <div class="form-group">
                                    <label for="timezone">المنطقة الزمنية <span class="text-danger">*</span></label>
                                    <select class="form-control @error('timezone') is-invalid @enderror" 
                                            id="timezone" name="timezone" required>
                                        <option value="">اختر المنطقة الزمنية</option>
                                        <option value="Asia/Riyadh" {{ old('timezone') == 'Asia/Riyadh' ? 'selected' : '' }}>آسيا/الرياض</option>
                                        <option value="Asia/Dubai" {{ old('timezone') == 'Asia/Dubai' ? 'selected' : '' }}>آسيا/دبي</option>
                                        <option value="Asia/Kuwait" {{ old('timezone') == 'Asia/Kuwait' ? 'selected' : '' }}>آسيا/الكويت</option>
                                        <option value="Asia/Qatar" {{ old('timezone') == 'Asia/Qatar' ? 'selected' : '' }}>آسيا/قطر</option>
                                        <option value="Asia/Bahrain" {{ old('timezone') == 'Asia/Bahrain' ? 'selected' : '' }}>آسيا/البحرين</option>
                                        <option value="Asia/Muscat" {{ old('timezone') == 'Asia/Muscat' ? 'selected' : '' }}>آسيا/مسقط</option>
                                    </select>
                                    @error('timezone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="currency_code">العملة <span class="text-danger">*</span></label>
                                    <select class="form-control @error('currency_code') is-invalid @enderror" 
                                            id="currency_code" name="currency_code" required>
                                        <option value="">اختر العملة</option>
                                        <option value="SAR" {{ old('currency_code') == 'SAR' ? 'selected' : '' }}>ريال سعودي (SAR)</option>
                                        <option value="AED" {{ old('currency_code') == 'AED' ? 'selected' : '' }}>درهم إماراتي (AED)</option>
                                        <option value="KWD" {{ old('currency_code') == 'KWD' ? 'selected' : '' }}>دينار كويتي (KWD)</option>
                                        <option value="QAR" {{ old('currency_code') == 'QAR' ? 'selected' : '' }}>ريال قطري (QAR)</option>
                                        <option value="BHD" {{ old('currency_code') == 'BHD' ? 'selected' : '' }}>دينار بحريني (BHD)</option>
                                        <option value="OMR" {{ old('currency_code') == 'OMR' ? 'selected' : '' }}>ريال عماني (OMR)</option>
                                        <option value="USD" {{ old('currency_code') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                    </select>
                                    @error('currency_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="language_code">اللغة <span class="text-danger">*</span></label>
                                    <select class="form-control @error('language_code') is-invalid @enderror" 
                                            id="language_code" name="language_code" required>
                                        <option value="">اختر اللغة</option>
                                        <option value="ar" {{ old('language_code') == 'ar' ? 'selected' : '' }}>العربية</option>
                                        <option value="en" {{ old('language_code') == 'en' ? 'selected' : '' }}>English</option>
                                    </select>
                                    @error('language_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> حفظ المستأجر
                        </button>
                        <a href="{{ route('tenants.index') }}" class="btn btn-secondary">
                            <i class="fa fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate code from name
    $('#name').on('input', function() {
        if (!$('#code').val()) {
            let name = $(this).val();
            let code = name.replace(/\s+/g, '_').toUpperCase().substring(0, 10);
            $('#code').val(code);
        }
    });

    // Form validation
    $('#tenantForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        $(this).find('[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });

    // Remove validation error on input
    $('.form-control').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>
@endpush
