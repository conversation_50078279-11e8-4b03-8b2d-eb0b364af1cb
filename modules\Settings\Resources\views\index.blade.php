@extends('layouts.app')

@section('title', 'إعدادات النظام')

@section('content')
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الإعدادات</li>
                    </ol>
                </div>
                <h4 class="page-title">إعدادات النظام</h4>
            </div>
        </div>
    </div>

    <!-- Settings Overview Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="الطابعات">الطابعات</h5>
                            <h3 class="my-2 py-1">{{ $overview['printer_count'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                طابعة مكونة
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="printer-chart" data-colors="#727cf5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="طرق الدفع">طرق الدفع</h5>
                            <h3 class="my-2 py-1">{{ $overview['payment_methods'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                طريقة مفعلة
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="payment-chart" data-colors="#0acf97"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="الأمان">الأمان</h5>
                            <h3 class="my-2 py-1">
                                @if($overview['security_enabled'])
                                    <i class="mdi mdi-shield-check text-success"></i>
                                @else
                                    <i class="mdi mdi-shield-alert text-warning"></i>
                                @endif
                            </h3>
                            <p class="mb-0 text-muted">
                                {{ $overview['security_enabled'] ? 'مفعل' : 'غير مفعل' }}
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="security-chart" data-colors="#fa5c7c"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="الفروع">الفروع</h5>
                            <h3 class="my-2 py-1">{{ $overview['branches_configured'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-info me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                فرع مكون
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="branch-chart" data-colors="#ffbc00"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Categories -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">فئات الإعدادات</h4>
                    
                    <div class="row">
                        <!-- Printer Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-printer widget-icon bg-primary-lighten text-primary"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات الطابعات</h5>
                                    <p class="card-text text-muted">إدارة الطابعات وإعدادات الطباعة</p>
                                    <a href="{{ route('settings.printer.index') }}" class="btn btn-primary btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-credit-card widget-icon bg-success-lighten text-success"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات الدفع</h5>
                                    <p class="card-text text-muted">إدارة طرق الدفع والبوابات</p>
                                    <a href="{{ route('settings.payment.index') }}" class="btn btn-success btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-shield-check widget-icon bg-danger-lighten text-danger"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات الأمان</h5>
                                    <p class="card-text text-muted">إدارة أمان النظام والمصادقة</p>
                                    <a href="{{ route('settings.security.index') }}" class="btn btn-danger btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Branch Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-store widget-icon bg-warning-lighten text-warning"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات الفروع</h5>
                                    <p class="card-text text-muted">إدارة إعدادات الفروع المختلفة</p>
                                    <a href="{{ route('settings.branch.index') }}" class="btn btn-warning btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-cogs widget-icon bg-info-lighten text-info"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات النظام</h5>
                                    <p class="card-text text-muted">إعدادات النظام العامة</p>
                                    <a href="{{ route('settings.system.index') }}" class="btn btn-info btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Restaurant Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-silverware-fork-knife widget-icon bg-secondary-lighten text-secondary"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات المطعم</h5>
                                    <p class="card-text text-muted">إعدادات خاصة بالمطعم والطاولات</p>
                                    <a href="{{ route('settings.restaurant.index') }}" class="btn btn-secondary btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <!-- User/Role Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-account-group widget-icon bg-primary-lighten text-primary"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات المستخدمين</h5>
                                    <p class="card-text text-muted">إدارة المستخدمين والأدوار</p>
                                    <a href="{{ route('settings.user.index') }}" class="btn btn-primary btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Report Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-chart-line widget-icon bg-success-lighten text-success"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات التقارير</h5>
                                    <p class="card-text text-muted">إعدادات التقارير والتصدير</p>
                                    <a href="{{ route('settings.report.index') }}" class="btn btn-success btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Settings -->
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card border border-light">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="mdi mdi-package-variant widget-icon bg-danger-lighten text-danger"></i>
                                    </div>
                                    <h5 class="card-title">إعدادات المخزون</h5>
                                    <p class="card-text text-muted">إعدادات إدارة المخزون</p>
                                    <a href="{{ route('settings.inventory.index') }}" class="btn btn-danger btn-sm">
                                        <i class="mdi mdi-cog me-1"></i> إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    .widget-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .bg-primary-lighten {
        background-color: rgba(114, 124, 245, 0.18);
    }

    .bg-success-lighten {
        background-color: rgba(10, 207, 151, 0.18);
    }

    .bg-danger-lighten {
        background-color: rgba(250, 92, 124, 0.18);
    }

    .bg-warning-lighten {
        background-color: rgba(255, 188, 0, 0.18);
    }

    .bg-info-lighten {
        background-color: rgba(58, 175, 169, 0.18);
    }

    .bg-secondary-lighten {
        background-color: rgba(108, 117, 125, 0.18);
    }
</style>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Add any JavaScript functionality here
        console.log('Settings dashboard loaded');
    });
</script>
@endsection
