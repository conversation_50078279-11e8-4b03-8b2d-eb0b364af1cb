<?php

use Illuminate\Support\Facades\Route;
use Modules\Customer\Http\Controllers\CustomerController;

/*
|--------------------------------------------------------------------------
| Customer API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Customer module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

Route::prefix('api/customers')->middleware(['auth:sanctum'])->group(function () {
    // Customer CRUD operations
    Route::get('/', [CustomerController::class, 'index']); // Get customers by branch
    Route::post('/', [CustomerController::class, 'store']); // Create customer
    Route::get('/{customer}', [CustomerController::class, 'show']); // Get specific customer
    Route::put('/{customer}', [CustomerController::class, 'update']); // Update customer
    Route::delete('/{customer}', [CustomerController::class, 'destroy']); // Delete customer

    // Customer search
    Route::get('/search/customers', [CustomerController::class, 'search']); // Search customers

    // Customer status management
    Route::patch('/{customer}/activate', [CustomerController::class, 'activate']); // Activate customer
    Route::patch('/{customer}/deactivate', [CustomerController::class, 'deactivate']); // Deactivate customer
    Route::patch('/{customer}/update-visit', [CustomerController::class, 'updateLastVisit']); // Update last visit

    // Loyalty points management
    Route::post('/{customer}/loyalty/add', [CustomerController::class, 'addLoyaltyPoints']); // Add loyalty points
    Route::post('/{customer}/loyalty/redeem', [CustomerController::class, 'redeemLoyaltyPoints']); // Redeem loyalty points
    Route::get('/{customer}/loyalty/history', [CustomerController::class, 'loyaltyHistory']); // Get loyalty history

    // Active customers
    Route::get('/active/list', [CustomerController::class, 'activeCustomers']); // Get active customers by branch
});