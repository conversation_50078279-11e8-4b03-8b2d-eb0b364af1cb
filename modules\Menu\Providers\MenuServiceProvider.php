<?php

namespace Modules\Menu\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Services\CategoryService;
use Modules\Menu\Services\MenuItemService;
use Modules\Menu\Services\AddonService;
use Modules\Menu\Services\VariantService;
use Modules\Menu\Services\PricingService;
use Modules\Menu\Services\AvailabilityService;
use Modules\Menu\Services\CodeGeneratorService;

class MenuServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the CodeGeneratorService first (no dependencies)
        $this->app->singleton(CodeGeneratorService::class, function ($app) {
            return new CodeGeneratorService();
        });
        
        // Register the MenuService with dependencies
        $this->app->singleton(MenuService::class, function ($app) {
            return new MenuService($app->make(CodeGeneratorService::class));
        });
        
        // Register the CategoryService with CodeGeneratorService dependency
        $this->app->singleton(CategoryService::class, function ($app) {
            return new CategoryService($app->make(CodeGeneratorService::class));
        });
        
        // Register the MenuItemService with CodeGeneratorService dependency
        $this->app->singleton(MenuItemService::class, function ($app) {
            return new MenuItemService($app->make(CodeGeneratorService::class));
        });
        
        // Register the AddonService with CodeGeneratorService dependency
        $this->app->singleton(AddonService::class, function ($app) {
            return new AddonService($app->make(CodeGeneratorService::class));
        });
        
        // Register the VariantService with CodeGeneratorService dependency
        $this->app->singleton(VariantService::class, function ($app) {
            return new VariantService($app->make(CodeGeneratorService::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'menu');
        
        // Load migrations from module
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
        
        // Publish config
  
      
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'namespace' => 'Modules\\Menu\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Note: Web routes are loaded manually in the main web.php file
        // to avoid conflicts and ensure proper middleware application
    }
}