@extends('layouts.master')

@section('title', 'فئات المواد')

@section('css')
    <!-- DataTables CSS -->
    <link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto">المخزون</h4>
                <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ فئات المواد</span>
            </div>
        </div>
    </div>
    <!-- breadcrumb -->
@endsection

@section('content')
    <!-- row -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card mg-b-20">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between">
                        <h4 class="card-title mg-b-0">فئات المواد</h4>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCategoryModal">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="categoriesTable" class="table key-buttons text-md-nowrap">
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">اسم الفئة</th>
                                    <th class="border-bottom-0">الفئة الأساسية</th>
                                    <th class="border-bottom-0">عدد المنتجات</th>
                                    <th class="border-bottom-0">ترتيب العرض</th>
                                    <th class="border-bottom-0">الحالة</th>
                                    <th class="border-bottom-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- row closed -->

    @include('inventory::categories.modals')
@endsection

@section('js')
    <!-- DataTables JS -->
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#categoriesTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('inventory.api.categories.datatable') }}",
                columns: [
                    {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
                    {data: 'name', name: 'name'},
                    {data: 'parent_name', name: 'parent.name'},
                    {data: 'products_count', name: 'products_count'},
                    {data: 'sort_order', name: 'sort_order'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                language: {
                    url: "{{ asset('assets/plugins/datatable/Arabic.json') }}"
                }
            });

            // Initialize Select2
            $('.select2').select2();

            // Load parent categories
            loadParentCategories();

            // Add Category
            $('#addCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                
                $.ajax({
                    url: "{{ route('inventory.api.categories.store') }}",
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#addCategoryModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم إضافة الفئة بنجاح');
                        $('#addCategoryForm')[0].reset();
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Edit Category
            $(document).on('click', '.edit-category', function() {
                var id = $(this).data('id');
                
                $.ajax({
                    url: "{{ route('inventory.api.categories.show', ':id') }}".replace(':id', id),
                    type: 'GET',
                    success: function(response) {
                        $('#edit_category_id').val(response.id);
                        $('#edit_name').val(response.name);
                        $('#edit_description').val(response.description);
                        $('#edit_parent_id').val(response.parent_id).trigger('change');
                        $('#edit_sort_order').val(response.sort_order);
                        $('#edit_is_active').prop('checked', response.is_active);
                        $('#editCategoryModal').modal('show');
                    }
                });
            });

            // Update Category
            $('#editCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var id = $('#edit_category_id').val();
                var formData = new FormData(this);
                
                $.ajax({
                    url: "{{ route('inventory.api.categories.update', ':id') }}".replace(':id', id),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#editCategoryModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم تحديث الفئة بنجاح');
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Delete Category
            $(document).on('click', '.delete-category', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');
                
                if (confirm('هل أنت متأكد من حذف الفئة "' + name + '"؟')) {
                    $.ajax({
                        url: "{{ route('inventory.api.categories.destroy', ':id') }}".replace(':id', id),
                        type: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            table.ajax.reload();
                            showAlert('success', 'تم حذف الفئة بنجاح');
                        },
                        error: function(xhr) {
                            showAlert('error', xhr.responseJSON.message || 'حدث خطأ أثناء الحذف');
                        }
                    });
                }
            });

            function loadParentCategories() {
                $.ajax({
                    url: "{{ route('inventory.api.categories.list') }}",
                    type: 'GET',
                    success: function(response) {
                        var options = '<option value="">اختر الفئة الأساسية</option>';
                        $.each(response, function(index, category) {
                            options += '<option value="' + category.id + '">' + category.name + '</option>';
                        });
                        $('#parent_id, #edit_parent_id').html(options);
                    }
                });
            }

            function showAlert(type, message) {
                var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                var alert = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                           message +
                           '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                           '<span aria-hidden="true">&times;</span>' +
                           '</button>' +
                           '</div>';
                
                $('.card-body').prepend(alert);
                
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }
        });
    </script>
@endsection