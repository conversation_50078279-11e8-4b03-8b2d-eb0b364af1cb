{"info": {"_postman_id": "c3d4e5f6-g7h8-9012-cdef-345678901234", "name": "Restaurant POS - Inventory Management", "description": "API collection for managing inventory, suppliers, purchase orders, and inventory analytics in the Restaurant POS system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Inventory Items", "item": [{"name": "Get All Inventory Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items?page=1&per_page=15&search=&category=&status=", "host": ["{{base_url}}"], "path": ["inventory", "items"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "search", "value": "", "description": "Search by item name or SKU"}, {"key": "category", "value": "", "description": "Filter by category"}, {"key": "status", "value": "", "description": "Filter by status (active, inactive, low_stock)"}]}}, "response": []}, {"name": "Create Inventory Item", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Tomatoes\",\n    \"sku\": \"TOM001\",\n    \"description\": \"Fresh red tomatoes\",\n    \"category\": \"Vegetables\",\n    \"unit\": \"kg\",\n    \"cost_price\": 2.50,\n    \"selling_price\": 4.00,\n    \"quantity\": 100,\n    \"min_stock_level\": 10,\n    \"max_stock_level\": 500,\n    \"reorder_point\": 20,\n    \"supplier_id\": {{supplier_id}},\n    \"branch_id\": {{branch_id}},\n    \"barcode\": \"1234567890123\",\n    \"expiry_date\": \"2024-12-31\",\n    \"storage_location\": \"Cold Storage A1\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/inventory/items", "host": ["{{base_url}}"], "path": ["inventory", "items"]}}, "response": []}, {"name": "Get Inventory Item by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "{{item_id}}"]}}, "response": []}, {"name": "Update Inventory Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Premium Tomatoes\",\n    \"description\": \"Premium quality fresh red tomatoes\",\n    \"cost_price\": 3.00,\n    \"selling_price\": 4.50,\n    \"min_stock_level\": 15,\n    \"max_stock_level\": 600,\n    \"reorder_point\": 25\n}"}, "url": {"raw": "{{base_url}}/inventory/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "{{item_id}}"]}}, "response": []}, {"name": "Delete Inventory Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "{{item_id}}"]}}, "response": []}, {"name": "Get Low Stock Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/low-stock?branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "low-stock"], "query": [{"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Update Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 50,\n    \"type\": \"addition\",\n    \"reason\": \"Stock replenishment\",\n    \"reference\": \"PO-2024-001\",\n    \"cost_per_unit\": 2.75,\n    \"notes\": \"Received from supplier ABC\"\n}"}, "url": {"raw": "{{base_url}}/inventory/items/{{item_id}}/update-stock", "host": ["{{base_url}}"], "path": ["inventory", "items", "{{item_id}}", "update-stock"]}}, "response": []}, {"name": "Get Item Movements", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/{{item_id}}/movements?page=1&per_page=15&date_from=&date_to=", "host": ["{{base_url}}"], "path": ["inventory", "items", "{{item_id}}", "movements"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}]}}, "response": []}, {"name": "Bulk Update Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"item_id\": 1,\n            \"quantity\": 25,\n            \"type\": \"addition\",\n            \"reason\": \"Stock count adjustment\"\n        },\n        {\n            \"item_id\": 2,\n            \"quantity\": 10,\n            \"type\": \"subtraction\",\n            \"reason\": \"Damaged goods\"\n        },\n        {\n            \"item_id\": 3,\n            \"quantity\": 100,\n            \"type\": \"addition\",\n            \"reason\": \"New delivery\"\n        }\n    ],\n    \"reference\": \"BULK-2024-001\",\n    \"notes\": \"Monthly stock adjustment\"\n}"}, "url": {"raw": "{{base_url}}/inventory/items/bulk-update", "host": ["{{base_url}}"], "path": ["inventory", "items", "bulk-update"]}}, "response": []}, {"name": "Get Inventory Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/analytics?period=month&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "analytics"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Reorder Suggestions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/items/reorder-suggestions?branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "items", "reorder-suggestions"], "query": [{"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}]}, {"name": "Suppliers", "item": [{"name": "Get All Suppliers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers?page=1&per_page=15&search=&category=&status=", "host": ["{{base_url}}"], "path": ["inventory", "suppliers"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "search", "value": "", "description": "Search by supplier name or contact"}, {"key": "category", "value": "", "description": "Filter by supplier category"}, {"key": "status", "value": "", "description": "Filter by status (active, inactive)"}]}}, "response": []}, {"name": "Create Supplier", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Fresh Produce Suppliers Ltd\",\n    \"contact_person\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"******-0123\",\n    \"address\": \"123 Market Street\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"postal_code\": \"10001\",\n    \"country\": \"USA\",\n    \"category\": \"Vegetables\",\n    \"payment_terms\": \"Net 30\",\n    \"credit_limit\": 10000.00,\n    \"tax_id\": \"12-3456789\",\n    \"website\": \"https://freshproduce.com\",\n    \"notes\": \"Reliable supplier for fresh vegetables\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/inventory/suppliers", "host": ["{{base_url}}"], "path": ["inventory", "suppliers"]}}, "response": []}, {"name": "Get Supplier by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}"]}}, "response": []}, {"name": "Update Supplier", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"contact_person\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"******-0124\",\n    \"credit_limit\": 15000.00,\n    \"payment_terms\": \"Net 15\",\n    \"notes\": \"Updated contact information and credit limit\"\n}"}, "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}"]}}, "response": []}, {"name": "Delete Supplier", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}"]}}, "response": []}, {"name": "Get Supplier Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}/products?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}", "products"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Supplier Purchase Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}/purchase-orders?page=1&per_page=15&status=", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}", "purchase-orders"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "status", "value": "", "description": "Filter by status (pending, approved, received, cancelled)"}]}}, "response": []}, {"name": "Get Supplier Performance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}/performance?period=month", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}", "performance"], "query": [{"key": "period", "value": "month", "description": "month, quarter, year"}]}}, "response": []}, {"name": "Add Product to Supplier", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": {{item_id}},\n    \"supplier_sku\": \"SUP-TOM-001\",\n    \"cost_price\": 2.25,\n    \"minimum_order_quantity\": 50,\n    \"lead_time_days\": 3,\n    \"is_preferred\": true\n}"}, "url": {"raw": "{{base_url}}/inventory/suppliers/{{supplier_id}}/add-product", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "{{supplier_id}}", "add-product"]}}, "response": []}, {"name": "Get Supplier Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/suppliers/categories", "host": ["{{base_url}}"], "path": ["inventory", "suppliers", "categories"]}}, "response": []}]}, {"name": "Purchase Orders", "item": [{"name": "Get All Purchase Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders?page=1&per_page=15&status=&supplier_id=&date_from=&date_to=", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "status", "value": "", "description": "Filter by status (draft, pending, approved, received, cancelled)"}, {"key": "supplier_id", "value": "", "description": "Filter by supplier"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}]}}, "response": []}, {"name": "Create Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"supplier_id\": {{supplier_id}},\n    \"branch_id\": {{branch_id}},\n    \"expected_delivery_date\": \"2024-02-15\",\n    \"notes\": \"Urgent order for weekend rush\",\n    \"items\": [\n        {\n            \"product_id\": 1,\n            \"quantity\": 100,\n            \"unit_cost\": 2.50,\n            \"notes\": \"Fresh quality required\"\n        },\n        {\n            \"product_id\": 2,\n            \"quantity\": 50,\n            \"unit_cost\": 1.75,\n            \"notes\": \"Grade A only\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders"]}}, "response": []}, {"name": "Get Purchase Order by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}"]}}, "response": []}, {"name": "Update Purchase Order", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"expected_delivery_date\": \"2024-02-16\",\n    \"notes\": \"Updated delivery date due to supplier availability\",\n    \"items\": [\n        {\n            \"id\": 1,\n            \"quantity\": 120,\n            \"unit_cost\": 2.45,\n            \"notes\": \"Increased quantity for bulk discount\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}"]}}, "response": []}, {"name": "Delete Purchase Order", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}"]}}, "response": []}, {"name": "Approve Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"approved_by\": \"Manager <PERSON>\",\n    \"approval_notes\": \"Approved for immediate processing\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/approve", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "approve"]}}, "response": []}, {"name": "Reject Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"rejected_by\": \"Manager <PERSON>\",\n    \"rejection_reason\": \"Budget constraints for this period\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/reject", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "reject"]}}, "response": []}, {"name": "Receive Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"received_by\": \"Warehouse Staff Mike\",\n    \"received_date\": \"2024-02-15\",\n    \"delivery_note\": \"DN-2024-001\",\n    \"items\": [\n        {\n            \"item_id\": 1,\n            \"received_quantity\": 95,\n            \"condition\": \"good\",\n            \"notes\": \"5 units damaged during transport\"\n        },\n        {\n            \"item_id\": 2,\n            \"received_quantity\": 50,\n            \"condition\": \"excellent\",\n            \"notes\": \"Perfect condition\"\n        }\n    ],\n    \"notes\": \"Partial delivery - 5 units of item 1 damaged\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/receive", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "receive"]}}, "response": []}, {"name": "Cancel Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"cancelled_by\": \"Manager <PERSON>\",\n    \"cancellation_reason\": \"Supplier unable to fulfill order\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/cancel", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "cancel"]}}, "response": []}, {"name": "Generate Purchase Order PDF", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/pdf"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/pdf", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "pdf"]}}, "response": []}]}, {"name": "Purchase Order Items", "item": [{"name": "Get Purchase Order Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/items", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "items"]}}, "response": []}, {"name": "Add Item to Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": 3,\n    \"quantity\": 75,\n    \"unit_cost\": 3.25,\n    \"notes\": \"Additional item requested by kitchen\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/items", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "items"]}}, "response": []}, {"name": "Update Purchase Order Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 80,\n    \"unit_cost\": 3.15,\n    \"notes\": \"Updated quantity and negotiated better price\"\n}"}, "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "items", "{{item_id}}"]}}, "response": []}, {"name": "Re<PERSON><PERSON> from Purchase Order", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/purchase-orders/{{purchase_order_id}}/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["inventory", "purchase-orders", "{{purchase_order_id}}", "items", "{{item_id}}"]}}, "response": []}]}, {"name": "Inventory Logs & Analytics", "item": [{"name": "Get All Inventory Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs?page=1&per_page=15&type=&item_id=&user_id=&date_from=&date_to=", "host": ["{{base_url}}"], "path": ["inventory", "logs"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "type", "value": "", "description": "Filter by log type (addition, subtraction, adjustment)"}, {"key": "item_id", "value": "", "description": "Filter by item ID"}, {"key": "user_id", "value": "", "description": "Filter by user ID"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}]}}, "response": []}, {"name": "Get Item Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/item/{{item_id}}?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["inventory", "logs", "item", "{{item_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get User Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/user/{{user_id}}?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["inventory", "logs", "user", "{{user_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Movements Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/movements/summary?period=month&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "movements", "summary"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Stock History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/stock-history/{{item_id}}?period=month", "host": ["{{base_url}}"], "path": ["inventory", "logs", "stock-history", "{{item_id}}"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}]}}, "response": []}, {"name": "Get Valuation History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/valuation-history?period=month&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "valuation-history"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Low Stock Alerts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/low-stock-alerts?branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "low-stock-alerts"], "query": [{"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Waste Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/waste-report?period=month&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "waste-report"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Export Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/export?format=excel&date_from=2024-01-01&date_to=2024-01-31&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "export"], "query": [{"key": "format", "value": "excel", "description": "excel, csv, pdf"}, {"key": "date_from", "value": "2024-01-01"}, {"key": "date_to", "value": "2024-01-31"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Audit Trail", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/audit-trail/{{item_id}}?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["inventory", "logs", "audit-trail", "{{item_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Discrepancy Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/logs/discrepancy-report?period=month&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "logs", "discrepancy-report"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}]}, {"name": "Utilities", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/utilities/categories", "host": ["{{base_url}}"], "path": ["inventory", "utilities", "categories"]}}, "response": []}, {"name": "Get Units", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/utilities/units", "host": ["{{base_url}}"], "path": ["inventory", "utilities", "units"]}}, "response": []}, {"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/utilities/dashboard-stats?branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["inventory", "utilities", "dashboard-stats"], "query": [{"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Import Inventory", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "branch_id", "value": "{{branch_id}}", "type": "text"}, {"key": "update_existing", "value": "true", "type": "text", "description": "Whether to update existing items"}]}, "url": {"raw": "{{base_url}}/inventory/utilities/import", "host": ["{{base_url}}"], "path": ["inventory", "utilities", "import"]}}, "response": []}, {"name": "Export Inventory", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/inventory/utilities/export?format=excel&branch_id={{branch_id}}&category=&status=", "host": ["{{base_url}}"], "path": ["inventory", "utilities", "export"], "query": [{"key": "format", "value": "excel", "description": "excel, csv, pdf"}, {"key": "branch_id", "value": "{{branch_id}}"}, {"key": "category", "value": "", "description": "Filter by category"}, {"key": "status", "value": "", "description": "Filter by status"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "tenant_id", "value": "1", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "item_id", "value": "1", "type": "string"}, {"key": "supplier_id", "value": "1", "type": "string"}, {"key": "purchase_order_id", "value": "1", "type": "string"}, {"key": "user_id", "value": "1", "type": "string"}]}