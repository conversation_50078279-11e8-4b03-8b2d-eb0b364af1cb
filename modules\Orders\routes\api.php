<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\OrderController;
use Modules\Orders\Http\Middleware\OrderAccessMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes for Orders Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Orders module.
|
*/

Route::prefix('orders')
    ->middleware(['auth', 'web'])
    ->group(function () {
        // Order CRUD operations
        Route::get('/', [OrderController::class, 'index'])->name('api.orders.index');
        Route::post('/', [OrderController::class, 'store'])->name('api.orders.store');
        Route::get('/{id}', [OrderController::class, 'show'])->name('api.orders.show');
        Route::put('/{id}', [OrderController::class, 'update'])->name('api.orders.update');
        Route::delete('/{id}', [OrderController::class, 'destroy'])->name('api.orders.destroy');
        
        // Additional order operations
        Route::patch('/{id}/status', [OrderController::class, 'updateStatus'])->name('api.orders.update-status');
        Route::put('/{id}/status', [OrderController::class, 'updateStatus'])->name('api.orders.update-status-put');
        Route::post('/{id}/confirm', [OrderController::class, 'confirmOrder'])->name('api.orders.confirm');
        Route::get('/{id}/items', [OrderController::class, 'getOrderItems'])->name('api.orders.items');
        Route::post('/{id}/items', [OrderController::class, 'addOrderItem'])->name('api.orders.add-item');
        Route::delete('/{id}/items/{itemId}', [OrderController::class, 'removeOrderItem'])->name('api.orders.remove-item');
        
        // Helper endpoints
        Route::get('/menu-items/{menuItemId}/addons', [OrderController::class, 'getMenuItemAddons'])->name('api.orders.menu-item-addons');
    });

// Public routes (no authentication required)
Route::prefix('orders/public')
    ->group(function () {
        Route::get('/statuses', function () {
            return response()->json(\Modules\Orders\Helpers\OrderHelper::getOrderStatuses());
        })->name('api.orders.public.statuses');
        
        Route::get('/types', function () {
            return response()->json(\Modules\Orders\Helpers\OrderHelper::getOrderTypes());
        })->name('api.orders.public.types');
    });