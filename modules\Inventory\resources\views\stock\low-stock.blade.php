@extends('layouts.master')

@push('inventory-styles')
<style>
.low-stock-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    margin-bottom: 20px;
}

.low-stock-card:hover {
    transform: translateY(-2px);
}

.stock-level-indicator {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 10px;
}

.stock-level-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.stock-level-fill.critical { background: #dc3545; }
.stock-level-fill.low { background: #ffc107; }

.alert-item {
    border-left: 4px solid;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
}

.alert-item.critical {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.alert-item.low {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.stock-status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ المخزون المنخفض</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="bulkReorder()">
                <i class="mdi mdi-cart-plus"></i> طلب إعادة تموين
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="exportLowStock()">
                <i class="mdi mdi-file-export"></i> تصدير التقرير
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Low Stock Summary -->
<div class="row row-sm">
    <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
        <div class="card low-stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-danger">
                        <i class="mdi mdi-alert-circle"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-danger">{{ $lowStockItems->where('current_stock', '<=', 0)->count() }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون نافد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
        <div class="card low-stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-alert"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-warning">{{ $lowStockItems->where('current_stock', '>', 0)->count() }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون منخفض</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
        <div class="card low-stock-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-primary">
                        <i class="mdi mdi-package-variant"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-primary">{{ $lowStockItems->count() }}</h2>
                            <p class="text-muted mb-0 tx-11">إجمالي المواد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock Items Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">المواد ذات المخزون المنخفض</h4>
                <div class="card-options">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                            <i class="mdi mdi-refresh"></i> تحديث
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="bulkReorder()">
                            <i class="mdi mdi-cart-plus"></i> طلب تموين
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if($lowStockItems && $lowStockItems->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-bordered text-md-nowrap" id="low-stock-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <th>المادة</th>
                                <th>الكود</th>
                                <th>الوحدة</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>آخر حركة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($lowStockItems as $item)
                            <tr>
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="{{ $item->id }}" class="form-check-input item-checkbox">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ $item->product->name }}</h6>
                                            <small class="text-muted">{{ $item->product->category ?? 'عام' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-light">{{ $item->product->sku ?? 'N/A' }}</span>
                                </td>
                                <td>{{ $item->product->unit->name ?? 'وحدة' }}</td>
                                <td>
                                    <span class="font-weight-bold {{ $item->current_stock <= 0 ? 'text-danger' : 'text-warning' }}">
                                        {{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-muted">{{ $item->minimum_level }} {{ $item->product->unit->symbol ?? '' }}</span>
                                </td>
                                <td>
                                    @if($item->current_stock <= 0)
                                        <span class="badge badge-danger stock-status-badge">نافد</span>
                                    @else
                                        <span class="badge badge-warning stock-status-badge">منخفض</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="text-muted">{{ $item->last_updated ? $item->last_updated->format('Y-m-d') : 'غير محدد' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="updateStock({{ $item->id }})">
                                            <i class="mdi mdi-cube-outline"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-success" onclick="createPurchaseOrder({{ $item->id }})">
                                            <i class="mdi mdi-cart-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewHistory({{ $item->id }})">
                                            <i class="mdi mdi-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="mdi mdi-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h5 class="text-muted">لا توجد مواد ذات مخزون منخفض</h5>
                    <p class="text-muted">جميع المواد في المخزون ضمن المستوى الطبيعي</p>
                    <a href="{{ route('inventory.stock.index') }}" class="btn btn-primary">
                        <i class="mdi mdi-cube-outline"></i> عرض جميع المواد
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Stock Update Modal -->
<div class="modal fade" id="stockUpdateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث المخزون</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="stockUpdateForm">
                <div class="modal-body">
                    <input type="hidden" id="update_item_id">
                    <div class="form-group">
                        <label>نوع العملية</label>
                        <select class="form-control" id="update_type" required>
                            <option value="in">إضافة مخزون</option>
                            <option value="out">خصم مخزون</option>
                            <option value="adjustment">تعديل المخزون</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الكمية</label>
                        <input type="number" class="form-control" id="update_quantity" min="1" required>
                    </div>
                    <div class="form-group">
                        <label>السبب</label>
                        <input type="text" class="form-control" id="update_reason" required>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea class="form-control" id="update_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث المخزون</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('js')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#low-stock-table').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        order: [[4, 'asc']], // Order by current stock ascending
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable ordering for checkbox and actions
        ]
    });

    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.item-checkbox').prop('checked', this.checked);
    });

    // Stock update form submission
    $('#stockUpdateForm').submit(function(e) {
        e.preventDefault();
        
        const itemId = $('#update_item_id').val();
        const formData = {
            type: $('#update_type').val(),
            quantity: $('#update_quantity').val(),
            reason: $('#update_reason').val(),
            notes: $('#update_notes').val(),
            _token: '{{ csrf_token() }}'
        };

        $.ajax({
            url: `/inventory/stock/${itemId}/update`,
            method: 'POST',
            data: formData,
            success: function(response) {
                $('#stockUpdateModal').modal('hide');
                location.reload(); // Refresh the page to show updated data
            },
            error: function(xhr) {
                alert('حدث خطأ أثناء تحديث المخزون');
            }
        });
    });
});

function updateStock(itemId) {
    $('#update_item_id').val(itemId);
    $('#stockUpdateModal').modal('show');
}

function createPurchaseOrder(itemId) {
    // Redirect to purchase order creation with pre-filled item
    window.location.href = `/inventory/purchase-orders/create?item_id=${itemId}`;
}

function viewHistory(itemId) {
    // Redirect to item history page
    window.location.href = `/inventory/items/${itemId}/history`;
}

function bulkReorder() {
    const selectedItems = $('.item-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedItems.length === 0) {
        alert('يرجى اختيار مادة واحدة على الأقل');
        return;
    }

    // Redirect to bulk purchase order creation
    const itemIds = selectedItems.join(',');
    window.location.href = `/inventory/purchase-orders/create?item_ids=${itemIds}`;
}

function exportLowStock() {
    window.location.href = '{{ route("inventory.stock.low-stock") }}?export=csv';
}

function refreshTable() {
    location.reload();
}
</script>
@endsection