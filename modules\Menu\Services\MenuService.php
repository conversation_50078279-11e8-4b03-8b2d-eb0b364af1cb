<?php

namespace Modules\Menu\Services;

use App\Models\Menu;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class MenuService
{
    protected $codeGeneratorService;

    public function __construct(CodeGeneratorService $codeGeneratorService)
    {
        $this->codeGeneratorService = $codeGeneratorService;
    }
    /**
     * Get menus for a specific branch with pagination and filtering.
     */
    public function getMenusForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = Menu::with(['branch', 'tenant'])
            ->withCount(['categories', 'menuItems'])
            ->where('branch_id', $branchId);

        // Apply filters
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['menu_type'])) {
            $query->where('menu_type', $filters['menu_type']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get menu by ID with relationships.
     */
    public function getMenuById(int $id): ?Menu
    {
        return Menu::with(['branch', 'tenant', 'categories', 'menuItems'])
            ->withCount(['categories', 'menuItems'])
            ->find($id);
    }

    /**
     * Get menu by ID for a specific branch.
     */
    public function getMenuByIdForBranch(int $id, int $branchId): ?Menu
    {
        return Menu::with(['branch', 'tenant', 'categories', 'menuItems'])
            ->withCount(['categories', 'menuItems'])
            ->where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
    }

    /**
     * Create a new menu.
     */
    public function createMenu(array $data): Menu
    {
        // Ensure branch_id is set
        if (!isset($data['branch_id'])) {
            throw new \InvalidArgumentException('Branch ID is required');
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? true;
        $data['sort_order'] = $data['sort_order'] ?? 0;
        
        // Generate code if not provided
        if (!isset($data['code']) || empty($data['code'])) {
            $data['code'] = $this->codeGeneratorService->generateMenuCode($data['name'], $data['branch_id']);
        }

        return Menu::create($data);
    }

    /**
     * Update an existing menu.
     */
    public function updateMenu(int $id, array $data): ?Menu
    {
        $menu = Menu::find($id);
        
        if (!$menu) {
            return null;
        }

        $menu->update($data);
        
        return $menu->fresh(['branch', 'tenant']);
    }

    /**
     * Update a menu for a specific branch.
     */
    public function updateMenuForBranch(int $id, array $data, int $branchId): ?Menu
    {
        $menu = Menu::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
        
        if (!$menu) {
            return null;
        }

        $menu->update($data);
        
        return $menu->fresh(['branch', 'tenant']);
    }

    /**
     * Delete a menu.
     */
    public function deleteMenu(int $id): bool
    {
        $menu = Menu::find($id);
        
        if (!$menu) {
            return false;
        }

        return $menu->delete();
    }

    /**
     * Delete a menu for a specific branch.
     */
    public function deleteMenuForBranch(int $id, int $branchId): bool
    {
        $menu = Menu::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
        
        if (!$menu) {
            return false;
        }

        return $menu->delete();
    }

    /**
     * Get active menus for a branch.
     */
    public function getActiveMenusForBranch(int $branchId): Collection
    {
        return Menu::with(['branch', 'tenant', 'categories', 'menuItems'])
            ->withCount(['categories', 'menuItems'])
            ->where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }


    public function getDefaultMenuForBranch(int $branchId): ?Menu
    {
        return Menu::with(['branch', 'tenant', 'categories', 'menuItems'])
            ->withCount(['categories', 'menuItems'])
            ->where('branch_id', $branchId)
            ->where('is_default', true)
            ->where('is_active', true)
            ->first();
    }


    public function getMenusListForBranch(int $branchId): Collection
    {
        return Menu::where('branch_id', $branchId)
            ->where('is_active', true)
            ->select('id', 'name', 'name_en')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    /**
     * Create a new menu specifically for web interface.
     * Sets default values and generates a unique code automatically.
     * 
     * @param array $data Menu data from web form
     * @return Menu The created menu instance
     */
    public function createMenuForWeb(array $data): Menu
    {
        $data['is_active'] = $data['is_active'] ?? true;
        $data['sort_order'] = $data['sort_order'] ?? 0;
        
        // Generate code if not provided
        if (!isset($data['code'])) {
            $data['code'] = 'MENU-' . time();
        }

        return Menu::create($data);
    }

    /**
     * Update a menu specifically for web interface with branch validation.
     * 
     * @param int $id The ID of the menu to update
     * @param array $data Updated menu data from web form
     * @param int $branchId The ID of the branch that should own the menu
     * @return Menu|null The updated menu or null if not found or not owned by branch
     */
    public function updateMenuForWeb(int $id, array $data, int $branchId): ?Menu
    {
        $menu = Menu::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
        
        if (!$menu) {
            return null;
        }

        $data['is_active'] = $data['is_active'] ?? true;
        $data['sort_order'] = $data['sort_order'] ?? 0;

        $menu->update($data);
        
        return $menu;
    }

    /**
     * Get menus for DataTable processing in web interface.
     * Filters menus by branch and selects all menu fields.
     * 
     * @param int $branchId The ID of the branch to get menus for
     * @return \Illuminate\Database\Eloquent\Builder Query builder for DataTable processing
     */
    public function getMenusForDataTable(int $branchId)
    {
        return Menu::where('branch_id', $branchId)
            ->select('menus.*');
    }
}