@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<!-- Color Picker CSS -->
<link href="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة البانرات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ البانرات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-banner-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة البانرات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع بانرات المطعم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="banners-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">العنوان</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">الموضع</th>
                                <th class="border-bottom-0">تاريخ البداية</th>
                                <th class="border-bottom-0">تاريخ النهاية</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Banner Modal -->
<div class="modal fade" id="bannerModal" tabindex="-1" role="dialog" aria-labelledby="bannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bannerModalLabel">إضافة بانر جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bannerForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="banner_id" name="banner_id">
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title">عنوان البانر <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title_en">عنوان البانر (إنجليزي)</label>
                                <input type="text" class="form-control" id="title_en" name="title_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Display Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">إعدادات العرض</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="banner_type">نوع البانر <span class="text-danger">*</span></label>
                                <select class="form-control" id="banner_type" name="banner_type" required>
                                    <option value="">اختر نوع البانر</option>
                                    <option value="promotional">ترويجي</option>
                                    <option value="informational">إعلامي</option>
                                    <option value="seasonal">موسمي</option>
                                    <option value="event">فعالية</option>
                                    <option value="offer">عرض</option>
                                    <option value="announcement">إعلان</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="position">الموضع <span class="text-danger">*</span></label>
                                <select class="form-control" id="position" name="position" required>
                                    <option value="">اختر الموضع</option>
                                    <option value="header">الرأس</option>
                                    <option value="footer">التذييل</option>
                                    <option value="sidebar">الشريط الجانبي</option>
                                    <option value="hero">البطل</option>
                                    <option value="popup">نافذة منبثقة</option>
                                    <option value="inline">داخل المحتوى</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="display_location">مكان العرض</label>
                                <select class="form-control" id="display_location" name="display_location">
                                    <option value="">جميع الصفحات</option>
                                    <option value="home">الصفحة الرئيسية</option>
                                    <option value="menu">صفحة القائمة</option>
                                    <option value="checkout">صفحة الدفع</option>
                                    <option value="profile">صفحة الملف الشخصي</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">المحتوى</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image_url">صورة البانر</label>
                                <input type="file" class="form-control" id="image_url" name="image_url" accept="image/*">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="background_color">لون الخلفية</label>
                                <input type="text" class="form-control colorpicker" id="background_color" name="background_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="text_color">لون النص</label>
                                <input type="text" class="form-control colorpicker" id="text_color" name="text_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_text">نص الزر</label>
                                <input type="text" class="form-control" id="button_text" name="button_text">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_url">رابط الزر</label>
                                <input type="url" class="form-control" id="button_url" name="button_url">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_color">لون الزر</label>
                                <input type="text" class="form-control colorpicker" id="button_color" name="button_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Scheduling -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الجدولة</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_time">وقت البداية</label>
                                <input type="time" class="form-control" id="start_time" name="start_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_time">وقت النهاية</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Limits -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">حدود الأداء</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_impressions">الحد الأقصى للمشاهدات</label>
                                <input type="number" class="form-control" id="max_impressions" name="max_impressions" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_clicks">الحد الأقصى للنقرات</label>
                                <input type="number" class="form-control" id="max_clicks" name="max_clicks" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="priority">الأولوية</label>
                                <input type="number" class="form-control" id="priority" name="priority" min="0" max="10" value="5">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الإعدادات</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_animated" name="is_animated" value="1">
                                    <label class="form-check-label" for="is_animated">
                                        متحرك
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_hide" name="auto_hide" value="1">
                                    <label class="form-check-label" for="auto_hide">
                                        إخفاء تلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-banner-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Banner Modal -->
<div class="modal fade" id="showBannerModal" tabindex="-1" role="dialog" aria-labelledby="showBannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showBannerModalLabel">تفاصيل البانر</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>عنوان البانر:</strong></label>
                            <p id="show_title"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النوع:</strong></label>
                            <p id="show_banner_type"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الموضع:</strong></label>
                            <p id="show_position"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مكان العرض:</strong></label>
                            <p id="show_display_location"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label><strong>الوصف:</strong></label>
                            <p id="show_description"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ البداية:</strong></label>
                            <p id="show_start_date"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ النهاية:</strong></label>
                            <p id="show_end_date"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مميز:</strong></label>
                            <p id="show_featured"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>المشاهدات:</strong></label>
                            <p id="show_impressions"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النقرات:</strong></label>
                            <p id="show_clicks"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.flash.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>
<!-- Color Picker JS -->
<script src="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize Color Picker
    $('.colorpicker').spectrum({
        showInput: true,
        className: "full-spectrum",
        showInitial: true,
        showPalette: true,
        showSelectionPalette: true,
        maxSelectionSize: 10,
        preferredFormat: "hex",
        localStorageKey: "spectrum.demo",
        palette: [
            ["rgb(0, 0, 0)", "rgb(67, 67, 67)", "rgb(102, 102, 102)",
            "rgb(204, 204, 204)", "rgb(217, 217, 217)","rgb(255, 255, 255)"],
            ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)",
            "rgb(255, 255, 0)", "rgb(0, 255, 0)", "rgb(0, 255, 255)",
            "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(153, 0, 255)",
            "rgb(255, 0, 255)"]
        ]
    });

    // Initialize DataTable
    var bannersTable = $('#banners-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("api.menu.banners.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            {data: 'id', name: 'id'},
            {data: 'title', name: 'title'},
            {data: 'banner_type', name: 'banner_type'},
            {data: 'position', name: 'position'},
            {data: 'start_date', name: 'start_date'},
            {data: 'end_date', name: 'end_date'},
            {data: 'is_active', name: 'is_active', render: function(data) {
                return data ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
            }},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Add Banner Button
    $('#add-banner-btn').click(function() {
        $('#bannerForm')[0].reset();
        $('#banner_id').val('');
        $('#bannerModalLabel').text('إضافة بانر جديد');
        $('#bannerModal').modal('show');
    });

    // Submit Banner Form
    $('#bannerForm').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var bannerId = $('#banner_id').val();
        var url = bannerId ? '{{ route("api.menu.banners.update", ":id") }}'.replace(':id', bannerId) : '{{ route("api.menu.banners.store") }}';
        var method = bannerId ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#bannerModal').modal('hide');
                    bannersTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $('#' + key).addClass('is-invalid');
                        $('#' + key).siblings('.invalid-feedback').text(value[0]);
                    });
                } else {
                    swal('خطأ!', 'حدث خطأ أثناء حفظ البانر', 'error');
                }
            }
        });
    });

    // Edit Banner
    $(document).on('click', '.edit-banner', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.show", ":id") }}'.replace(':id', bannerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var banner = response.data;
                    $('#banner_id').val(banner.id);
                    $('#title').val(banner.title);
                    $('#title_en').val(banner.title_en);
                    $('#description').val(banner.description);
                    $('#banner_type').val(banner.banner_type);
                    $('#position').val(banner.position);
                    $('#display_location').val(banner.display_location);
                    $('#background_color').spectrum("set", banner.background_color);
                    $('#text_color').spectrum("set", banner.text_color);
                    $('#button_text').val(banner.button_text);
                    $('#button_url').val(banner.button_url);
                    $('#button_color').spectrum("set", banner.button_color);
                    $('#start_date').val(banner.start_date);
                    $('#start_time').val(banner.start_time);
                    $('#end_date').val(banner.end_date);
                    $('#end_time').val(banner.end_time);
                    $('#max_impressions').val(banner.max_impressions);
                    $('#max_clicks').val(banner.max_clicks);
                    $('#priority').val(banner.priority);
                    $('#is_active').prop('checked', banner.is_active);
                    $('#is_featured').prop('checked', banner.is_featured);
                    $('#is_animated').prop('checked', banner.is_animated);
                    $('#auto_hide').prop('checked', banner.auto_hide);
                    
                    $('#bannerModalLabel').text('تعديل البانر');
                    $('#bannerModal').modal('show');
                }
            }
        });
    });

    // Show Banner
    $(document).on('click', '.show-banner', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.show", ":id") }}'.replace(':id', bannerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var banner = response.data;
                    $('#show_title').text(banner.title);
                    $('#show_banner_type').text(banner.banner_type);
                    $('#show_position').text(banner.position);
                    $('#show_display_location').text(banner.display_location || 'جميع الصفحات');
                    $('#show_description').text(banner.description || 'غير محدد');
                    $('#show_start_date').text(banner.start_date + ' ' + (banner.start_time || ''));
                    $('#show_end_date').text(banner.end_date + ' ' + (banner.end_time || ''));
                    $('#show_status').html(banner.is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                    $('#show_featured').html(banner.is_featured ? '<span class="badge badge-info">مميز</span>' : '<span class="badge badge-secondary">عادي</span>');
                    $('#show_impressions').text(banner.impressions_count || 0);
                    $('#show_clicks').text(banner.clicks_count || 0);
                    
                    $('#showBannerModal').modal('show');
                }
            }
        });
    });

    // Delete Banner
    $(document).on('click', '.delete-banner', function() {
        var bannerId = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("api.menu.banners.destroy", ":id") }}'.replace(':id', bannerId),
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        bannersTable.ajax.reload();
                        swal('تم الحذف!', response.message, 'success');
                    }
                },
                error: function() {
                    swal('خطأ!', 'حدث خطأ أثناء حذف البانر', 'error');
                }
            });
        });
    });

    // Toggle Banner Status
    $(document).on('click', '.toggle-status', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.toggle-status", ":id") }}'.replace(':id', bannerId),
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    bannersTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function() {
                swal('خطأ!', 'حدث خطأ أثناء تغيير حالة البانر', 'error');
            }
        });
    });
});
</script>
@endsection