{"info": {"_postman_id": "d4e5f6g7-h8i9-0123-def4-456789012345", "name": "Restaurant POS - Payment Management", "description": "API collection for managing payments, transactions, refunds, and payment analytics in the Restaurant POS system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Payment Management", "item": [{"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/methods?branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["payments", "methods"], "query": [{"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 125.50,\n    \"currency\": \"USD\",\n    \"payment_method\": \"credit_card\",\n    \"order_id\": {{order_id}},\n    \"branch_id\": {{branch_id}},\n    \"customer_id\": {{customer_id}},\n    \"payment_details\": {\n        \"card_number\": \"****************\",\n        \"expiry_month\": \"12\",\n        \"expiry_year\": \"2025\",\n        \"cvv\": \"123\",\n        \"cardholder_name\": \"<PERSON>\"\n    },\n    \"billing_address\": {\n        \"street\": \"123 Main St\",\n        \"city\": \"New York\",\n        \"state\": \"NY\",\n        \"postal_code\": \"10001\",\n        \"country\": \"US\"\n    },\n    \"description\": \"Restaurant order payment\",\n    \"metadata\": {\n        \"table_number\": \"5\",\n        \"waiter_id\": \"12\"\n    }\n}"}, "url": {"raw": "{{base_url}}/payments/process", "host": ["{{base_url}}"], "path": ["payments", "process"]}}, "response": []}, {"name": "Get Payment by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/{{payment_id}}", "host": ["{{base_url}}"], "path": ["payments", "{{payment_id}}"]}}, "response": []}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/{{payment_id}}/status", "host": ["{{base_url}}"], "path": ["payments", "{{payment_id}}", "status"]}}, "response": []}, {"name": "Update Payment", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"completed\",\n    \"notes\": \"Payment manually verified\",\n    \"metadata\": {\n        \"verification_method\": \"manual\",\n        \"verified_by\": \"manager_id_123\"\n    }\n}"}, "url": {"raw": "{{base_url}}/payments/{{payment_id}}", "host": ["{{base_url}}"], "path": ["payments", "{{payment_id}}"]}}, "response": []}, {"name": "Cancel Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Customer request\",\n    \"cancelled_by\": \"manager_id_123\",\n    \"notes\": \"Customer changed their mind\"\n}"}, "url": {"raw": "{{base_url}}/payments/{{payment_id}}/cancel", "host": ["{{base_url}}"], "path": ["payments", "{{payment_id}}", "cancel"]}}, "response": []}, {"name": "Get All Payments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments?page=1&per_page=15&status=&payment_method=&date_from=&date_to=&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["payments"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "status", "value": "", "description": "Filter by status (pending, processing, completed, failed, cancelled, refunded)"}, {"key": "payment_method", "value": "", "description": "Filter by payment method (cash, credit_card, debit_card, mobile_payment)"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Payment Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/analytics?period=month&branch_id={{branch_id}}&payment_method=", "host": ["{{base_url}}"], "path": ["payments", "analytics"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}, {"key": "payment_method", "value": "", "description": "Filter by payment method"}]}}, "response": []}, {"name": "Reconcile Payments", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-02-15\",\n    \"branch_id\": {{branch_id}},\n    \"payment_method\": \"credit_card\",\n    \"expected_amount\": 2500.00,\n    \"actual_amount\": 2485.50,\n    \"notes\": \"Small discrepancy due to processing fees\",\n    \"reconciled_by\": \"manager_id_123\"\n}"}, "url": {"raw": "{{base_url}}/payments/reconcile", "host": ["{{base_url}}"], "path": ["payments", "reconcile"]}}, "response": []}]}, {"name": "Refunds", "item": [{"name": "Create Refund", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"payment_id\": {{payment_id}},\n    \"amount\": 50.00,\n    \"reason\": \"Item not available\",\n    \"refund_type\": \"partial\",\n    \"notes\": \"Customer requested partial refund for unavailable dessert\",\n    \"processed_by\": \"manager_id_123\"\n}"}, "url": {"raw": "{{base_url}}/payments/refunds", "host": ["{{base_url}}"], "path": ["payments", "refunds"]}}, "response": []}, {"name": "Get Refund by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/refunds/{{refund_id}}", "host": ["{{base_url}}"], "path": ["payments", "refunds", "{{refund_id}}"]}}, "response": []}, {"name": "Get All Refunds", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/refunds?page=1&per_page=15&status=&date_from=&date_to=&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["payments", "refunds"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "status", "value": "", "description": "Filter by status (pending, processing, completed, failed)"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Update Refund Status", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"completed\",\n    \"notes\": \"Refund processed successfully\",\n    \"processed_by\": \"manager_id_123\"\n}"}, "url": {"raw": "{{base_url}}/payments/refunds/{{refund_id}}", "host": ["{{base_url}}"], "path": ["payments", "refunds", "{{refund_id}}"]}}, "response": []}, {"name": "Get Payment Refunds", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/{{payment_id}}/refunds", "host": ["{{base_url}}"], "path": ["payments", "{{payment_id}}", "refunds"]}}, "response": []}]}, {"name": "Transactions", "item": [{"name": "Create Transaction", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"type\": \"sale\",\n    \"amount\": 85.75,\n    \"currency\": \"USD\",\n    \"payment_method\": \"cash\",\n    \"order_id\": {{order_id}},\n    \"branch_id\": {{branch_id}},\n    \"customer_id\": {{customer_id}},\n    \"description\": \"Cash payment for order\",\n    \"reference\": \"CASH-2024-001\",\n    \"metadata\": {\n        \"table_number\": \"3\",\n        \"cashier_id\": \"15\",\n        \"cash_received\": 100.00,\n        \"change_given\": 14.25\n    }\n}"}, "url": {"raw": "{{base_url}}/transactions", "host": ["{{base_url}}"], "path": ["transactions"]}}, "response": []}, {"name": "Get Transaction by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions/{{transaction_id}}", "host": ["{{base_url}}"], "path": ["transactions", "{{transaction_id}}"]}}, "response": []}, {"name": "Get All Transactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions?page=1&per_page=15&type=&status=&payment_method=&date_from=&date_to=&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["transactions"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "type", "value": "", "description": "Filter by type (sale, refund, adjustment, fee)"}, {"key": "status", "value": "", "description": "Filter by status (pending, completed, failed, cancelled)"}, {"key": "payment_method", "value": "", "description": "Filter by payment method"}, {"key": "date_from", "value": "", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "End date (YYYY-MM-DD)"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Update Transaction", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"completed\",\n    \"notes\": \"Transaction verified and completed\",\n    \"metadata\": {\n        \"verified_by\": \"manager_id_123\",\n        \"verification_time\": \"2024-02-15T14:30:00Z\"\n    }\n}"}, "url": {"raw": "{{base_url}}/transactions/{{transaction_id}}", "host": ["{{base_url}}"], "path": ["transactions", "{{transaction_id}}"]}}, "response": []}, {"name": "Cancel Transaction", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Duplicate transaction\",\n    \"cancelled_by\": \"manager_id_123\",\n    \"notes\": \"Identified as duplicate entry\"\n}"}, "url": {"raw": "{{base_url}}/transactions/{{transaction_id}}/cancel", "host": ["{{base_url}}"], "path": ["transactions", "{{transaction_id}}", "cancel"]}}, "response": []}, {"name": "Get Transaction Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions/summary?period=day&date={{date}}&branch_id={{branch_id}}", "host": ["{{base_url}}"], "path": ["transactions", "summary"], "query": [{"key": "period", "value": "day", "description": "day, week, month, quarter, year"}, {"key": "date", "value": "{{date}}", "description": "Specific date (YYYY-MM-DD)"}, {"key": "branch_id", "value": "{{branch_id}}"}]}}, "response": []}, {"name": "Get Transaction Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions/analytics?period=month&branch_id={{branch_id}}&type=", "host": ["{{base_url}}"], "path": ["transactions", "analytics"], "query": [{"key": "period", "value": "month", "description": "day, week, month, quarter, year"}, {"key": "branch_id", "value": "{{branch_id}}"}, {"key": "type", "value": "", "description": "Filter by transaction type"}]}}, "response": []}, {"name": "Get Transactions by Entity", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions/by-entity?entity_type=order&entity_id={{order_id}}&page=1&per_page=15", "host": ["{{base_url}}"], "path": ["transactions", "by-entity"], "query": [{"key": "entity_type", "value": "order", "description": "Entity type (order, customer, branch)"}, {"key": "entity_id", "value": "{{order_id}}", "description": "Entity ID"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Reconcile Transactions", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-02-15\",\n    \"branch_id\": {{branch_id}},\n    \"payment_method\": \"cash\",\n    \"expected_amount\": 1500.00,\n    \"actual_amount\": 1485.75,\n    \"discrepancy_reason\": \"Cash drawer shortage\",\n    \"reconciled_by\": \"manager_id_123\",\n    \"notes\": \"Minor discrepancy investigated and documented\"\n}"}, "url": {"raw": "{{base_url}}/transactions/reconcile", "host": ["{{base_url}}"], "path": ["transactions", "reconcile"]}}, "response": []}, {"name": "Export Transactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/transactions/export?format=excel&date_from=2024-02-01&date_to=2024-02-29&branch_id={{branch_id}}&type=", "host": ["{{base_url}}"], "path": ["transactions", "export"], "query": [{"key": "format", "value": "excel", "description": "excel, csv, pdf"}, {"key": "date_from", "value": "2024-02-01"}, {"key": "date_to", "value": "2024-02-29"}, {"key": "branch_id", "value": "{{branch_id}}"}, {"key": "type", "value": "", "description": "Filter by transaction type"}]}}, "response": []}]}, {"name": "Payment Helpers", "item": [{"name": "Format Currency", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 1234.56,\n    \"currency\": \"USD\",\n    \"locale\": \"en_US\"\n}"}, "url": {"raw": "{{base_url}}/payments/helpers/format-currency", "host": ["{{base_url}}"], "path": ["payments", "helpers", "format-currency"]}}, "response": []}, {"name": "Get Payment Method Icons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/helpers/payment-method-icons", "host": ["{{base_url}}"], "path": ["payments", "helpers", "payment-method-icons"]}}, "response": []}, {"name": "Get Status Badges", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/helpers/status-badges", "host": ["{{base_url}}"], "path": ["payments", "helpers", "status-badges"]}}, "response": []}, {"name": "Calculate <PERSON>es", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100.00,\n    \"payment_method\": \"credit_card\",\n    \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/payments/helpers/calculate-fees", "host": ["{{base_url}}"], "path": ["payments", "helpers", "calculate-fees"]}}, "response": []}, {"name": "Validate Amount", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 50.00,\n    \"currency\": \"USD\",\n    \"payment_method\": \"credit_card\",\n    \"min_amount\": 1.00,\n    \"max_amount\": 10000.00\n}"}, "url": {"raw": "{{base_url}}/payments/helpers/validate-amount", "host": ["{{base_url}}"], "path": ["payments", "helpers", "validate-amount"]}}, "response": []}, {"name": "Generate Payment Summary", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": {{order_id}},\n    \"payment_method\": \"credit_card\",\n    \"include_fees\": true,\n    \"include_taxes\": true\n}"}, "url": {"raw": "{{base_url}}/payments/helpers/generate-payment-summary", "host": ["{{base_url}}"], "path": ["payments", "helpers", "generate-payment-summary"]}}, "response": []}, {"name": "Calculate Change", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"total_amount\": 87.50,\n    \"paid_amount\": 100.00,\n    \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/payments/helpers/calculate-change", "host": ["{{base_url}}"], "path": ["payments", "helpers", "calculate-change"]}}, "response": []}]}, {"name": "Webhooks", "item": [{"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Stripe-Signature", "value": "{{stripe_signature}}"}], "body": {"mode": "raw", "raw": "{\n    \"id\": \"evt_1234567890\",\n    \"object\": \"event\",\n    \"api_version\": \"2020-08-27\",\n    \"created\": 1609459200,\n    \"data\": {\n        \"object\": {\n            \"id\": \"pi_1234567890\",\n            \"object\": \"payment_intent\",\n            \"amount\": 2000,\n            \"currency\": \"usd\",\n            \"status\": \"succeeded\"\n        }\n    },\n    \"livemode\": false,\n    \"pending_webhooks\": 1,\n    \"request\": {\n        \"id\": \"req_1234567890\",\n        \"idempotency_key\": null\n    },\n    \"type\": \"payment_intent.succeeded\"\n}"}, "url": {"raw": "{{base_url}}/payments/webhooks/stripe", "host": ["{{base_url}}"], "path": ["payments", "webhooks", "stripe"]}}, "response": []}, {"name": "PayPal Webhook", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "PAYPAL-TRANSMISSION-ID", "value": "{{paypal_transmission_id}}"}, {"key": "PAYPAL-CERT-ID", "value": "{{paypal_cert_id}}"}, {"key": "PAYPAL-TRANSMISSION-SIG", "value": "{{paypal_transmission_sig}}"}, {"key": "PAYPAL-TRANSMISSION-TIME", "value": "{{paypal_transmission_time}}"}], "body": {"mode": "raw", "raw": "{\n    \"id\": \"WH-1234567890\",\n    \"event_version\": \"1.0\",\n    \"create_time\": \"2024-02-15T10:30:00Z\",\n    \"resource_type\": \"payment\",\n    \"event_type\": \"PAYMENT.CAPTURE.COMPLETED\",\n    \"summary\": \"Payment completed for $20.00 USD\",\n    \"resource\": {\n        \"id\": \"PAY-1234567890\",\n        \"state\": \"approved\",\n        \"amount\": {\n            \"total\": \"20.00\",\n            \"currency\": \"USD\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/payments/webhooks/paypal", "host": ["{{base_url}}"], "path": ["payments", "webhooks", "paypal"]}}, "response": []}, {"name": "Square Webhook", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-Square-Signature", "value": "{{square_signature}}"}], "body": {"mode": "raw", "raw": "{\n    \"merchant_id\": \"MERCHANT123\",\n    \"type\": \"payment.updated\",\n    \"event_id\": \"EVENT123\",\n    \"created_at\": \"2024-02-15T10:30:00Z\",\n    \"data\": {\n        \"type\": \"payment\",\n        \"id\": \"PAYMENT123\",\n        \"object\": {\n            \"payment\": {\n                \"id\": \"PAYMENT123\",\n                \"status\": \"COMPLETED\",\n                \"amount_money\": {\n                    \"amount\": 2000,\n                    \"currency\": \"USD\"\n                }\n            }\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/payments/webhooks/square", "host": ["{{base_url}}"], "path": ["payments", "webhooks", "square"]}}, "response": []}]}, {"name": "Health Check", "item": [{"name": "Payment System Health", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments/health", "host": ["{{base_url}}"], "path": ["payments", "health"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "tenant_id", "value": "1", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "payment_id", "value": "1", "type": "string"}, {"key": "transaction_id", "value": "1", "type": "string"}, {"key": "refund_id", "value": "1", "type": "string"}, {"key": "order_id", "value": "1", "type": "string"}, {"key": "customer_id", "value": "1", "type": "string"}, {"key": "date", "value": "2024-02-15", "type": "string"}, {"key": "stripe_signature", "value": "", "type": "string"}, {"key": "paypal_transmission_id", "value": "", "type": "string"}, {"key": "paypal_cert_id", "value": "", "type": "string"}, {"key": "paypal_transmission_sig", "value": "", "type": "string"}, {"key": "paypal_transmission_time", "value": "", "type": "string"}, {"key": "square_signature", "value": "", "type": "string"}]}