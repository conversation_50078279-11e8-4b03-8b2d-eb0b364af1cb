<?php

use Illuminate\Support\Facades\Route;
use Modules\Settings\Http\Controllers\SettingController;
use Modules\Settings\Http\Controllers\Web\SettingsWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth', 'tenant'])->prefix('settings')->name('settings.')->group(function () {

    // Main Settings Dashboard
    Route::get('/', [SettingsWebController::class, 'index'])->name('index');

    // Printer Settings Routes
    Route::prefix('printer')->name('printer.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'printerSettings'])->name('index');
        Route::get('/create', [SettingsWebController::class, 'createPrinter'])->name('create');
        Route::get('/{id}/edit', [SettingsWebController::class, 'editPrinter'])->name('edit');
    });

    // Payment Settings Routes
    Route::prefix('payment')->name('payment.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'paymentSettings'])->name('index');
        Route::get('/create', [SettingsWebController::class, 'createPaymentSetting'])->name('create');
        Route::get('/{id}/edit', [SettingsWebController::class, 'editPaymentSetting'])->name('edit');
    });

    // Security Settings Routes
    Route::prefix('security')->name('security.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'securitySettings'])->name('index');
    });

    // Branch Settings Routes
    Route::prefix('branch')->name('branch.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'branchSettings'])->name('index');
        Route::get('/{branchId}', [SettingsWebController::class, 'branchSettings'])->name('show');
    });

    // System Settings Routes
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'systemSettings'])->name('index');
    });

    // Restaurant Settings Routes
    Route::prefix('restaurant')->name('restaurant.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'restaurantSettings'])->name('index');
    });

    // User/Role Settings Routes
    Route::prefix('user')->name('user.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'userSettings'])->name('index');
    });

    // Report Settings Routes
    Route::prefix('report')->name('report.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'reportSettings'])->name('index');
    });

    // Inventory Settings Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'inventorySettings'])->name('index');
    });

    // Backup Settings Routes
    Route::prefix('backup')->name('backup.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'backupSettings'])->name('index');
    });
});

Route::middleware('web')->group(function () {
    // Add your web routes here
});
