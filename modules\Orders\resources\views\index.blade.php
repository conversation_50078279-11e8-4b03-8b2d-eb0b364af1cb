@extends('layouts.master')

@section('title', 'Orders Management')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.order-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}
.order-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}
.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}
.status-pending { background-color: #ffeaa7; color: #2d3436; }
.status-confirmed { background-color: #74b9ff; color: #ffffff; }
.status-preparing { background-color: #fd79a8; color: #ffffff; }
.status-ready { background-color: #00b894; color: #ffffff; }
.status-served { background-color: #6c5ce7; color: #ffffff; }
.status-completed { background-color: #00cec9; color: #ffffff; }
.status-cancelled { background-color: #e17055; color: #ffffff; }
.order-type {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
}
.type-dine_in { background-color: #e8f5e8; color: #2e7d32; }
.type-takeaway { background-color: #fff3e0; color: #f57c00; }
.type-delivery { background-color: #e3f2fd; color: #1976d2; }
.type-online { background-color: #f3e5f5; color: #7b1fa2; }

/* Cards specific styles */
.order-card-item {
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.order-card-item:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.order-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.order-number {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.order-card-body {
    margin-bottom: 1rem;
}

.order-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.order-info-label {
    font-weight: 500;
    color: #6c757d;
}

.order-info-value {
    color: #495057;
}

.order-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid #e9ecef;
}

.order-total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #28a745;
}

.order-actions {
    display: flex;
    gap: 0.25rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.cards-loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.no-orders {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.no-orders i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة الطلبات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الطلبات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('orders.create') }}" class="btn btn-info btn-icon ml-2">
                <i class="mdi mdi-plus"></i>
            </a>
            <a href="{{ route('pos.create') }}" class="btn btn-success btn-icon ml-2">
                <i class="mdi mdi-cash-register"></i>
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-primary-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">إجمالي الطلبات اليوم</h6>
                            <h2 class="text-white mb-0 number-font" id="total-orders-today">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-shopping-cart text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-danger-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">الطلبات المعلقة</h6>
                            <h2 class="text-white mb-0 number-font" id="pending-orders">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-clock text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-success-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">الطلبات المكتملة</h6>
                            <h2 class="text-white mb-0 number-font" id="completed-orders">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-check-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-warning-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">إجمالي المبيعات اليوم</h6>
                            <h2 class="text-white mb-0 number-font" id="total-sales-today">$0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-dollar-sign text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">حالة الطلب</label>
                            <select class="form-control select2" id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="confirmed">مؤكد</option>
                                <option value="preparing">قيد التحضير</option>
                                <option value="ready">جاهز</option>
                                <option value="served">تم التقديم</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع الطلب</label>
                            <select class="form-control select2" id="type-filter">
                                <option value="">جميع الأنواع</option>
                                <option value="dine_in">تناول في المطعم</option>
                                <option value="takeaway">طلب خارجي</option>
                                <option value="delivery">توصيل</option>
                                <option value="online">طلب أونلاين</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date-filter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-secondary" id="clear-filters">مسح الفلاتر</button>
                                <button class="btn btn-primary" id="refresh-orders">تحديث</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Toggle -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="btn-group" role="group" aria-label="View Toggle">
                <button type="button" class="btn btn-outline-primary active" id="table-view-btn">
                    <i class="mdi mdi-table"></i> عرض جدولي
                </button>
                <button type="button" class="btn btn-outline-primary" id="cards-view-btn">
                    <i class="mdi mdi-view-grid"></i> عرض البطاقات
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Table View -->
    <div class="row" id="table-view">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table key-buttons text-md-nowrap" id="orders-table" data-page-length='50'>
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">رقم الطلب</th>
                                    <th class="border-bottom-0">العميل</th>
                                    <th class="border-bottom-0">الطاولة</th>
                                    <th class="border-bottom-0">النوع</th>
                                    <th class="border-bottom-0">الحالة</th>
                                    <th class="border-bottom-0">المجموع</th>
                                    <th class="border-bottom-0">التاريخ</th>
                                    <th class="border-bottom-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Cards View -->
    <div class="row" id="cards-view" style="display: none;">
        <div class="col-12">
            <div id="orders-cards-container">
                <!-- Cards will be loaded here -->
            </div>
            <!-- Pagination for cards -->
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="Orders pagination">
                    <ul class="pagination" id="cards-pagination">
                        <!-- Pagination will be loaded here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    // View toggle functionality
    let currentView = 'table';
    let currentPage = 1;
    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#orders-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("orders.data") }}',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.order_type = $('#type-filter').val();
                    d.date = $('#date-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'order_number', name: 'order_number' },
                { data: 'customer_name', name: 'customer_name', orderable: false },
                { data: 'table_name', name: 'table_name', orderable: false },
                { data: 'type_badge', name: 'order_type' },
                { data: 'status_badge', name: 'status' },
                { data: 'formatted_total', name: 'total_amount' },
                { data: 'formatted_date', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            },
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });
    }

    // View toggle handlers
    $('#table-view-btn').on('click', function() {
        if (currentView !== 'table') {
            currentView = 'table';
            $(this).addClass('active');
            $('#cards-view-btn').removeClass('active');
            $('#table-view').show();
            $('#cards-view').hide();
            
            if (!table) {
                initializeDataTable();
            } else {
                table.draw();
            }
        }
    });

    $('#cards-view-btn').on('click', function() {
        if (currentView !== 'cards') {
            currentView = 'cards';
            $(this).addClass('active');
            $('#table-view-btn').removeClass('active');
            $('#table-view').hide();
            $('#cards-view').show();
            loadCardsData();
        }
    });

    // Load cards data
    function loadCardsData(page = 1) {
        currentPage = page;
        $('#orders-cards-container').html('<div class="cards-loading"><i class="mdi mdi-loading mdi-spin"></i> جاري التحميل...</div>');

        $.ajax({
            url: '{{ route("orders.cards") }}',
            method: 'GET',
            data: {
                page: page,
                per_page: 12,
                status: $('#status-filter').val(),
                order_type: $('#type-filter').val(),
                date: $('#date-filter').val()
            },
            success: function(response) {
                if (response.success && response.data) {
                    renderCards(response.data);
                    renderPagination(response.data);
                } else {
                    $('#orders-cards-container').html('<div class="no-orders"><i class="mdi mdi-cart-off"></i><h5>لا توجد طلبات</h5></div>');
                }
            },
            error: function(xhr) {
                console.error('Error loading cards data:', xhr);
                $('#orders-cards-container').html('<div class="no-orders"><i class="mdi mdi-alert"></i><h5>حدث خطأ في تحميل البيانات</h5></div>');
            }
        });
    }

    // Render cards
    function renderCards(data) {
        let cardsHtml = '';
        
        if (data.data && data.data.length > 0) {
            cardsHtml = '<div class="row">';
            
            data.data.forEach(function(order) {
                cardsHtml += generateOrderCard(order);
            });
            
            cardsHtml += '</div>';
        } else {
            cardsHtml = '<div class="no-orders"><i class="mdi mdi-cart-off"></i><h5>لا توجد طلبات</h5></div>';
        }
        
        $('#orders-cards-container').html(cardsHtml);
    }

    // Generate individual order card
    function generateOrderCard(order) {
        const statusTexts = {
            'pending': 'معلق',
            'confirmed': 'مؤكد',
            'preparing': 'قيد التحضير',
            'ready': 'جاهز',
            'served': 'تم التقديم',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        };

        const typeTexts = {
            'dine_in': 'تناول في المطعم',
            'takeaway': 'طلب خارجي',
            'delivery': 'توصيل',
            'online': 'طلب أونلاين'
        };

        const customerName = order.customer ? order.customer.name : 'عميل مجهول';
        const tableName = order.table ? order.table.name : 'غير محدد';
        const statusText = statusTexts[order.status] || order.status;
        const typeText = typeTexts[order.order_type] || order.order_type;
        const orderDate = new Date(order.created_at).toLocaleString('ar-EG');

        return `
            <div class="col-lg-4 col-md-6 col-sm-12">
                <div class="order-card-item">
                    <div class="order-card-header">
                        <div class="order-number">#${order.order_number}</div>
                        <div>
                            <span class="order-status status-${order.status}">${statusText}</span>
                        </div>
                    </div>
                    
                    <div class="order-card-body">
                        <div class="order-info-row">
                            <span class="order-info-label">العميل:</span>
                            <span class="order-info-value">${customerName}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">الطاولة:</span>
                            <span class="order-info-value">${tableName}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">النوع:</span>
                            <span class="order-type type-${order.order_type}">${typeText}</span>
                        </div>
                        <div class="order-info-row">
                            <span class="order-info-label">التاريخ:</span>
                            <span class="order-info-value">${orderDate}</span>
                        </div>
                    </div>
                    
                    <div class="order-card-footer">
                        <div class="order-total">$${parseFloat(order.total_amount).toFixed(2)}</div>
                        <div class="order-actions">
                            ${order.status === 'pending' ? `
                                <button class="btn btn-sm btn-success confirm-order-btn" data-order-id="${order.id}" title="تأكيد الطلب">
                                    <i class="mdi mdi-check"></i>
                                </button>
                            ` : ''}
                            <a href="/orders/${order.id}" class="btn btn-sm btn-outline-primary" title="عرض">
                                <i class="mdi mdi-eye"></i>
                            </a>
                            <a href="/orders/${order.id}/edit" class="btn btn-sm btn-outline-warning" title="تعديل">
                                <i class="mdi mdi-pencil"></i>
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-order" data-id="${order.id}" title="حذف">
                                <i class="mdi mdi-delete"></i>
                            </button>
                            ${order.status !== 'cancelled' ? `
                                <a href="/pos/orders/${order.id}/kot" class="btn btn-sm btn-success" title="طباعة KOT">
                                    <i class="mdi mdi-printer"></i>
                                </a>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Render pagination
    function renderPagination(data) {
        let paginationHtml = '';
        
        if (data.last_page > 1) {
            paginationHtml = '<li class="page-item ' + (data.current_page === 1 ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="' + (data.current_page - 1) + '">السابق</a></li>';
            
            for (let i = 1; i <= data.last_page; i++) {
                paginationHtml += '<li class="page-item ' + (data.current_page === i ? 'active' : '') + '">';
                paginationHtml += '<a class="page-link" href="#" data-page="' + i + '">' + i + '</a></li>';
            }
            
            paginationHtml += '<li class="page-item ' + (data.current_page === data.last_page ? 'disabled' : '') + '">';
            paginationHtml += '<a class="page-link" href="#" data-page="' + (data.current_page + 1) + '">التالي</a></li>';
        }
        
        $('#cards-pagination').html(paginationHtml);
    }

    // Pagination click handler
    $(document).on('click', '#cards-pagination .page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && page !== currentPage) {
            loadCardsData(page);
        }
    });

    // Initialize table view by default
    initializeDataTable();

    // Filter functionality
    $('#status-filter, #type-filter, #date-filter').on('change', function() {
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(1);
        }
    });

    $('#clear-filters').on('click', function() {
        $('#status-filter, #type-filter, #date-filter').val('');
        $('.select2').trigger('change');
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(1);
        }
    });

    $('#refresh-orders').on('click', function() {
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(currentPage);
        }
        loadStatistics();
    });

    // Delete order functionality
    $(document).on('click', '.delete-order', function() {
        var orderId = $(this).data('id');

        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: `/orders/${orderId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function() {
                    swal("تم الحذف!", "تم حذف الطلب بنجاح.", "success");
                    if (currentView === 'table') {
                        table.draw();
                    } else {
                        loadCardsData(currentPage);
                    }
                    loadStatistics();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف الطلب", "error");
                }
            });
        });
    });

    // Order confirmation functionality
    $(document).on('click', '.confirm-order-btn', function() {
        const orderId = $(this).data('order-id');
        const button = $(this);
        const originalText = button.html();
        
        // Show loading state
        button.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> جاري التأكيد...');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order number and KOT information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show toast notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                } else {
                    showOrderErrorToast(response.message || "حدث خطأ أثناء تأكيد الطلب");
                }
                
                // Refresh the current view
                if (currentView === 'table') {
                    table.draw();
                } else {
                    loadCardsData(currentPage);
                }
                loadStatistics();
                
                // Restore button state
                button.prop('disabled', false).html(originalText);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "حدث خطأ أثناء تأكيد الطلب";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                button.prop('disabled', false).html(originalText);
            }
        });
    });

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("orders.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.statistics) {
                    $('#total-orders-today').text(response.statistics.total_today || 0);
                    $('#pending-orders').text(response.statistics.pending || 0);
                    $('#completed-orders').text(response.statistics.completed || 0);
                    $('#total-sales-today').text('$' + (response.statistics.sales_today || 0).toFixed(2));
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Initial statistics load
    loadStatistics();
});
</script>
@endsection