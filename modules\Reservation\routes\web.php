<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\RestaurantTableController;
use Modules\Reservation\Http\Controllers\QRTestController;
use Modules\Reservation\Http\Controllers\ReservationDataTableController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


// QR Code Test Routes
Route::prefix('reservation/qr-test')->name('reservation.qr.')->group(function () {
    Route::get('/', [QRTestController::class, 'index'])->name('test');
    Route::post('/generate', [QRTestController::class, 'generateQR'])->name('generate');
    Route::post('/validate', [QRTestController::class, 'validateQR'])->name('validate');
    Route::post('/custom', [QRTestController::class, 'generateCustomQR'])->name('custom');
    Route::post('/batch', [QRTestController::class, 'batchGenerate'])->name('batch');
    Route::get('/table-info', [QRTestController::class, 'getTableInfo'])->name('table-info');
});

Route::prefix('reservation')->group(function () {
    // Dashboard
    Route::get('/', [ReservationDataTableController::class, 'dashboard'])->name('reservation.dashboard');
    
    // DataTable Views
    Route::get('/reservations', [ReservationDataTableController::class, 'reservationsIndex'])->name('reservation.reservations');
    
    // CRUD Routes for Reservations
    Route::resource('reservations', ReservationDataTableController::class)->except(['index']);
    
    // CRUD Routes for Waiter Requests
    Route::get('/waiter-requests', [ReservationDataTableController::class, 'waiterRequestsIndex'])->name('reservation.waiter-requests');
    Route::resource('waiter-requests', ReservationDataTableController::class)->except(['index']);
    
    // API Routes for fetching lists
    Route::get('/api/customers', [ReservationDataTableController::class, 'getCustomers']);
    Route::get('/api/areas', [ReservationDataTableController::class, 'getAreas']);
    Route::get('/api/tables', [ReservationDataTableController::class, 'getTables']);
    Route::get('/api/areas/{id}/tables', [ReservationDataTableController::class, 'getAvailableTablesByArea']);
    Route::get('/api/waiters', [ReservationDataTableController::class, 'getWaiters']);
    Route::get('/api/reservation-statuses', [ReservationDataTableController::class, 'getReservationStatuses']);
});

// Separate Areas Management
Route::middleware('auth')->prefix('areas')->group(function () {
    Route::get('/', [ReservationDataTableController::class, 'areasIndex'])->name('areas.index');
    Route::resource('areas', ReservationDataTableController::class)->except(['index']);
});

// Separate Tables Management  
Route::middleware('auth')->prefix('tables')->group(function () {
    Route::get('/', [ReservationDataTableController::class, 'tablesIndex'])->name('tables.index');
    Route::resource('tables', ReservationDataTableController::class)->except(['index']);
    
    // DataTable AJAX endpoint
    Route::get('/data/tables', [ReservationDataTableController::class, 'tablesDataTable'])->name('reservation.tables.data');
    
    // QR Code Management
    Route::post('/{id}/generate-qr', [ReservationDataTableController::class, 'generateTableQR'])->name('tables.generate-qr');
    Route::post('/{id}/regenerate-qr', [ReservationDataTableController::class, 'regenerateTableQR'])->name('tables.regenerate-qr');
    Route::post('/{id}/set-manual-qr', [ReservationDataTableController::class, 'setManualTableQR'])->name('tables.set-manual-qr');
});

