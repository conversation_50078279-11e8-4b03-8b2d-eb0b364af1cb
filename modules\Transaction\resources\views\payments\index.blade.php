@extends('layouts.master')

@section('title', 'Payment History')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.payment-status {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}
.status-pending { background-color: #ffeaa7; color: #2d3436; }
.status-completed { background-color: #00b894; color: #ffffff; }
.status-failed { background-color: #e17055; color: #ffffff; }
.status-cancelled { background-color: #636e72; color: #ffffff; }
.status-refunded { background-color: #6c5ce7; color: #ffffff; }

.payment-method {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
}
.method-cash { background-color: #e8f5e8; color: #2e7d32; }
.method-card { background-color: #e3f2fd; color: #1976d2; }
.method-bank_transfer { background-color: #fff3e0; color: #f57c00; }
.method-digital_wallet { background-color: #f3e5f5; color: #7b1fa2; }
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Payment History</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ Payments</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button class="btn btn-info btn-icon ml-2" id="refresh-payments">
                <i class="mdi mdi-refresh"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-primary-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Total Payments</h6>
                            <h2 class="text-white mb-0 number-font" id="total-payments">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-credit-card text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-success-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Completed</h6>
                            <h2 class="text-white mb-0 number-font" id="completed-payments">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-check-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-danger-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Failed</h6>
                            <h2 class="text-white mb-0 number-font" id="failed-payments">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-times-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-warning-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Total Amount</h6>
                            <h2 class="text-white mb-0 number-font" id="total-amount">$0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-dollar-sign text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select class="form-control select2" id="status-filter">
                                <option value="">All Statuses</option>
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="failed">Failed</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="refunded">Refunded</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Payment Method</label>
                            <select class="form-control select2" id="method-filter">
                                <option value="">All Methods</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date-from-filter">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date-to-filter">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-secondary" id="clear-filters">Clear</button>
                                <button class="btn btn-primary" id="apply-filters">Apply</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table key-buttons text-md-nowrap" id="payments-table" data-page-length='25'>
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">Transaction #</th>
                                    <th class="border-bottom-0">Order #</th>
                                    <th class="border-bottom-0">Payment Method</th>
                                    <th class="border-bottom-0">Amount</th>
                                    <th class="border-bottom-0">Status</th>
                                    <th class="border-bottom-0">Reference</th>
                                    <th class="border-bottom-0">Date</th>
                                    <th class="border-bottom-0">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#payments-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("api.payments.index") }}',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.payment_method_id = $('#method-filter').val();
                    d.date_from = $('#date-from-filter').val();
                    d.date_to = $('#date-to-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'transaction_number', name: 'transaction.transaction_number' },
                { data: 'order_number', name: 'transaction.order.order_number' },
                { data: 'payment_method_badge', name: 'paymentMethod.name' },
                { data: 'formatted_amount', name: 'amount' },
                { data: 'status_badge', name: 'status' },
                { data: 'reference_number', name: 'reference_number' },
                { data: 'formatted_date', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[7, 'desc']] // Order by date descending
        });
    }

    // Load payment methods for filter
    function loadPaymentMethods() {
        $.ajax({
            url: '{{ route("api.payments.payment-methods") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    let options = '<option value="">All Methods</option>';
                    
                    response.data.forEach(function(method) {
                        options += `<option value="${method.id}">${method.name}</option>`;
                    });
                    
                    $('#method-filter').html(options);
                }
            },
            error: function(xhr) {
                console.error('Error loading payment methods:', xhr);
            }
        });
    }

    // Initialize table
    initializeDataTable();
    
    // Load payment methods
    loadPaymentMethods();

    // Filter functionality
    $('#apply-filters').on('click', function() {
        table.draw();
    });

    $('#clear-filters').on('click', function() {
        $('#status-filter, #method-filter, #date-from-filter, #date-to-filter').val('');
        $('.select2').trigger('change');
        table.draw();
    });

    $('#refresh-payments').on('click', function() {
        table.draw();
        loadStatistics();
    });

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("api.payments.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    $('#total-payments').text(response.data.total_payments || 0);
                    $('#completed-payments').text(response.data.completed_payments || 0);
                    $('#failed-payments').text(response.data.failed_payments || 0);
                    $('#total-amount').text('$' + (response.data.total_amount || 0).toFixed(2));
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Initial statistics load
    loadStatistics();

    // Handle payment actions
    $(document).on('click', '.cancel-payment', function() {
        const paymentId = $(this).data('payment-id');
        
        swal({
            title: "Are you sure?",
            text: "This will cancel the payment and cannot be undone!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, cancel it!",
            cancelButtonText: "No, keep it",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: `{{ url('api/payments') }}/${paymentId}/cancel`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        swal("Cancelled!", "Payment has been cancelled.", "success");
                        table.draw();
                        loadStatistics();
                    } else {
                        swal("Error!", response.message || "Failed to cancel payment", "error");
                    }
                },
                error: function(xhr) {
                    console.error('Error cancelling payment:', xhr);
                    swal("Error!", "Failed to cancel payment", "error");
                }
            });
        });
    });

    $(document).on('click', '.refund-payment', function() {
        const paymentId = $(this).data('payment-id');
        
        swal({
            title: "Refund Payment",
            text: "Enter refund reason:",
            type: "input",
            showCancelButton: true,
            confirmButtonText: "Refund",
            cancelButtonText: "Cancel",
            closeOnConfirm: false,
            inputPlaceholder: "Refund reason..."
        }, function(inputValue) {
            if (inputValue === false) return false;
            
            if (inputValue === "") {
                swal.showInputError("Please enter a refund reason!");
                return false;
            }
            
            $.ajax({
                url: `{{ url('api/payments') }}/${paymentId}/refund`,
                method: 'POST',
                data: {
                    reason: inputValue
                },
                success: function(response) {
                    if (response.success) {
                        swal("Refunded!", "Payment has been refunded.", "success");
                        table.draw();
                        loadStatistics();
                    } else {
                        swal("Error!", response.message || "Failed to refund payment", "error");
                    }
                },
                error: function(xhr) {
                    console.error('Error refunding payment:', xhr);
                    swal("Error!", "Failed to refund payment", "error");
                }
            });
        });
    });
});
</script>
@endsection
