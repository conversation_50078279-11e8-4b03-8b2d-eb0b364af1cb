@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">سجل نقاط الولاء</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ {{ $customer->first_name }} {{ $customer->last_name }}</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('customers.loyalty.show', $customer) }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left"></i> العودة لنقاط الولاء
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

<!-- Customer Info Row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header">
                <h4 class="card-title mg-b-0">معلومات العميل</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>الاسم الكامل:</h6>
                        <p>{{ $customer->first_name }} {{ $customer->last_name }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>رقم الهاتف:</h6>
                        <p>{{ $customer->phone ?: '-' }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>البريد الإلكتروني:</h6>
                        <p>{{ $customer->email ?: '-' }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>الرصيد الحالي:</h6>
                        <h4 class="text-success">{{ number_format($customer->loyalty_points, 2) }} نقطة</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loyalty Stats Row -->
<div class="row">
    <div class="col-lg-3">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-success rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-plus text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">إجمالي المكتسب</h6>
                        <h4 class="text-success mb-0">{{ number_format($totalEarned, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-warning rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-minus text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">إجمالي المستخدم</h6>
                        <h4 class="text-warning mb-0">{{ number_format($totalRedeemed, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-danger rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-clock-o text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">المنتهي الصلاحية</h6>
                        <h4 class="text-danger mb-0">{{ number_format($totalExpired, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-primary rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-star text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">الرصيد المتاح</h6>
                        <h4 class="text-primary mb-0">{{ number_format($customer->loyalty_points, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">سجل المعاملات الكامل</h4>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" id="exportBtn">
                            <i class="fa fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">جميع معاملات نقاط الولاء للعميل</p>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="typeFilter">نوع المعاملة:</label>
                        <select id="typeFilter" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="earned">مكتسبة</option>
                            <option value="redeemed">مستخدمة</option>
                            <option value="expired">منتهية الصلاحية</option>
                            <option value="adjusted">معدلة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="dateFromFilter">من تاريخ:</label>
                        <input type="date" id="dateFromFilter" class="form-control">
                    </div>
                    <div class="col-md-3">
                        <label for="dateToFilter">إلى تاريخ:</label>
                        <input type="date" id="dateToFilter" class="form-control">
                    </div>
                    <div class="col-md-3">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" id="filterBtn" class="btn btn-primary">
                                <i class="fa fa-filter"></i> تطبيق الفلتر
                            </button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="history-table" class="table key-buttons text-md-nowrap" data-page-length='25'>
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">النقاط</th>
                                <th class="border-bottom-0">الوصف</th>
                                <th class="border-bottom-0">رقم الطلب</th>
                                <th class="border-bottom-0">تمت بواسطة</th>
                                <th class="border-bottom-0">التاريخ</th>
                                <th class="border-bottom-0">تاريخ الانتهاء</th>
                                <th class="border-bottom-0">الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jszip.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/pdfmake.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/vfs_fonts.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.html5.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.print.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable for transaction history
    var table = $('#history-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("customers.loyalty.history.data", $customer) }}',
            data: function(d) {
                d.type = $('#typeFilter').val();
                d.date_from = $('#dateFromFilter').val();
                d.date_to = $('#dateToFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'type_badge', name: 'type' },
            { data: 'points_formatted', name: 'points' },
            { data: 'description', name: 'description' },
            { data: 'order_link', name: 'order_id', orderable: false, searchable: false },
            { data: 'processed_by_name', name: 'processedBy.name' },
            { data: 'date_formatted', name: 'created_at' },
            { data: 'expires_at_formatted', name: 'expires_at' },
            { data: 'status_badge', name: 'status', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        order: [[6, 'desc']], // Order by date descending
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Filter functionality
    $('#filterBtn').click(function() {
        table.draw();
    });

    $('#resetBtn').click(function() {
        $('#typeFilter').val('');
        $('#dateFromFilter').val('');
        $('#dateToFilter').val('');
        table.draw();
    });

    // Export functionality
    $('#exportBtn').click(function() {
        // Trigger the Excel export button
        $('.buttons-excel').click();
    });

    // Set default date range (last 30 days)
    var today = new Date();
    var thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#dateToFilter').val(today.toISOString().split('T')[0]);
    $('#dateFromFilter').val(thirtyDaysAgo.toISOString().split('T')[0]);
});
</script>
@endsection
