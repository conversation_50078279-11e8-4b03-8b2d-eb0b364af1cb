{"info": {"_postman_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "Restaurant POS - Users, Roles & Permissions", "description": "API collection for managing users, roles, and permissions in the Restaurant POS system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"tenant_id\": {{tenant_id}},\n    \"branch_id\": {{branch_id}},\n    \"employee_id\": \"EMP001\",\n    \"position\": \"Manager\",\n    \"department\": \"Operations\",\n    \"hourly_rate\": 25.00,\n    \"salary\": 50000.00\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"current_password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"password123\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/change-password", "host": ["{{base_url}}"], "path": ["auth", "change-password"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}, {"name": "Logout All Devices", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/logout-all", "host": ["{{base_url}}"], "path": ["auth", "logout-all"]}}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/auth/forgot-password", "host": ["{{base_url}}"], "path": ["auth", "forgot-password"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"reset_token_here\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/reset-password", "host": ["{{base_url}}"], "path": ["auth", "reset-password"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/users?page=1&per_page=15&search=&role=&branch_id=", "host": ["{{base_url}}"], "path": ["auth", "admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "search", "value": "", "description": "Search by name or email"}, {"key": "role", "value": "", "description": "Filter by role name"}, {"key": "branch_id", "value": "", "description": "Filter by branch ID"}]}}, "response": []}, {"name": "Get Users by Branch (Current User's Branch)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/users/branch?page=1&per_page=15&search=&role=", "host": ["{{base_url}}"], "path": ["auth", "admin", "users", "branch"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "search", "value": "", "description": "Search by name or email"}, {"key": "role", "value": "", "description": "Filter by role name"}]}}, "response": []}, {"name": "Assign Roles to User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"roles\": [\"manager\", \"cashier\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["auth", "admin", "users", "{{user_id}}", "roles"]}}, "response": []}, {"name": "Revoke Roles from User", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"roles\": [\"cashier\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["auth", "admin", "users", "{{user_id}}", "roles"]}}, "response": []}, {"name": "Assign Permissions to User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"permissions\": [\"view-orders\", \"create-orders\", \"edit-orders\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/users/{{user_id}}/permissions", "host": ["{{base_url}}"], "path": ["auth", "admin", "users", "{{user_id}}", "permissions"]}}, "response": []}, {"name": "Revoke Permissions from User", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"permissions\": [\"edit-orders\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/users/{{user_id}}/permissions", "host": ["{{base_url}}"], "path": ["auth", "admin", "users", "{{user_id}}", "permissions"]}}, "response": []}]}, {"name": "Role Management", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/roles?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"branch-manager\",\n    \"display_name\": \"Branch Manager\",\n    \"description\": \"Manages a specific branch operations\",\n    \"guard_name\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/admin/roles", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles"]}}, "response": []}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"display_name\": \"Updated Branch Manager\",\n    \"description\": \"Updated description for branch manager role\"\n}"}, "url": {"raw": "{{base_url}}/auth/admin/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Assign Permissions to Role", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"permissions\": [\"view-orders\", \"create-orders\", \"edit-orders\", \"view-reports\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles", "{{role_id}}", "permissions"]}}, "response": []}, {"name": "<PERSON><PERSON> Permissions from Role", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"permissions\": [\"edit-orders\"]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/roles/{{role_id}}/permissions", "host": ["{{base_url}}"], "path": ["auth", "admin", "roles", "{{role_id}}", "permissions"]}}, "response": []}]}, {"name": "Permission Management", "item": [{"name": "Get All Permissions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/permissions?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Grouped Permissions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/permissions/grouped", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions", "grouped"]}}, "response": []}, {"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"manage-inventory\",\n    \"display_name\": \"Manage Inventory\",\n    \"description\": \"Can manage inventory items and stock levels\",\n    \"group\": \"inventory\",\n    \"guard_name\": \"web\"\n}"}, "url": {"raw": "{{base_url}}/auth/admin/permissions", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions"]}}, "response": []}, {"name": "Bulk Create Permissions", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"permissions\": [\n        {\n            \"name\": \"view-kitchen-orders\",\n            \"display_name\": \"View Kitchen Orders\",\n            \"description\": \"Can view orders in kitchen display\",\n            \"group\": \"kitchen\"\n        },\n        {\n            \"name\": \"update-order-status\",\n            \"display_name\": \"Update Order Status\",\n            \"description\": \"Can update order preparation status\",\n            \"group\": \"kitchen\"\n        },\n        {\n            \"name\": \"manage-menu-items\",\n            \"display_name\": \"Manage Menu Items\",\n            \"description\": \"Can create, edit, and delete menu items\",\n            \"group\": \"menu\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/auth/admin/permissions/bulk", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions", "bulk"]}}, "response": []}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions", "{{permission_id}}"]}}, "response": []}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"display_name\": \"Updated Permission Name\",\n    \"description\": \"Updated permission description\",\n    \"group\": \"updated-group\"\n}"}, "url": {"raw": "{{base_url}}/auth/admin/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions", "{{permission_id}}"]}}, "response": []}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/admin/permissions/{{permission_id}}", "host": ["{{base_url}}"], "path": ["auth", "admin", "permissions", "{{permission_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "tenant_id", "value": "1", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "user_id", "value": "1", "type": "string"}, {"key": "role_id", "value": "1", "type": "string"}, {"key": "permission_id", "value": "1", "type": "string"}]}