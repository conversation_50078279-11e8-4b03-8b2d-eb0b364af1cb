<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('employee_id')->nullable()->after('branch_id');
            $table->string('position')->nullable()->after('role');
            $table->string('department')->nullable()->after('position');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('department');
            $table->decimal('base_salary', 10, 2)->nullable()->after('salary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['employee_id', 'position', 'department', 'hourly_rate', 'base_salary']);
        });
    }
};
