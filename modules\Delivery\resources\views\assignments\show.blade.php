@extends('layouts.app')

@section('title', 'Delivery Assignment Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-truck mr-2"></i>
                        Delivery Assignment #{{ $assignment->id }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('delivery.assignments.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Assignment Details -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Assignment Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Order Number:</strong></td>
                                            <td>{{ $assignment->order->order_number ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'pending' => 'warning',
                                                        'assigned' => 'info',
                                                        'picked_up' => 'primary',
                                                        'in_transit' => 'secondary',
                                                        'delivered' => 'success',
                                                        'cancelled' => 'danger',
                                                        'failed' => 'danger'
                                                    ];
                                                    $color = $statusColors[$assignment->status] ?? 'secondary';
                                                @endphp
                                                <span class="badge bg-{{ $color }}">
                                                    {{ ucfirst(str_replace('_', ' ', $assignment->status)) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Assigned At:</strong></td>
                                            <td>{{ $assignment->assigned_at ? $assignment->assigned_at->format('M d, Y H:i') : 'Not assigned' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Estimated Delivery:</strong></td>
                                            <td>{{ $assignment->estimated_delivery_time ? $assignment->estimated_delivery_time->format('M d, Y H:i') : 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Actual Delivery:</strong></td>
                                            <td>{{ $assignment->delivered_at ? $assignment->delivered_at->format('M d, Y H:i') : 'Not delivered' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Delivery Fee:</strong></td>
                                            <td>${{ number_format($assignment->delivery_fee ?? 0, 2) }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Branch:</strong></td>
                                            <td>{{ $assignment->order->branch->name ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Customer:</strong></td>
                                            <td>{{ $assignment->order->customer->name ?? 'Guest' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Customer Phone:</strong></td>
                                            <td>{{ $assignment->order->customer->phone ?? $assignment->order->customer_phone ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Personnel:</strong></td>
                                            <td>{{ $assignment->personnel->name ?? 'Unassigned' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Personnel Phone:</strong></td>
                                            <td>{{ $assignment->personnel->phone ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Vehicle Type:</strong></td>
                                            <td>{{ $assignment->personnel->vehicle_type ?? 'N/A' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            @if($assignment->notes)
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Notes:</h6>
                                    <div class="alert alert-info">
                                        {{ $assignment->notes }}
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Delivery Address -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Delivery Address</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Address:</strong><br>
                                    {{ $assignment->delivery_address ?? $assignment->order->delivery_address ?? 'N/A' }}</p>
                                    
                                    @if($assignment->delivery_latitude && $assignment->delivery_longitude)
                                    <p><strong>Coordinates:</strong><br>
                                    Lat: {{ $assignment->delivery_latitude }}, Lng: {{ $assignment->delivery_longitude }}</p>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <div id="deliveryMap" style="height: 200px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-map-marker-alt fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">Map view</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    @if($assignment->order && $assignment->order->orderItems)
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Order Items</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($assignment->order->orderItems as $item)
                                        <tr>
                                            <td>{{ $item->menuItem->name ?? 'Unknown Item' }}</td>
                                            <td>{{ $item->quantity }}</td>
                                            <td>${{ number_format($item->unit_price, 2) }}</td>
                                            <td>${{ number_format($item->total_price, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3">Subtotal:</th>
                                            <th>${{ number_format($assignment->order->subtotal ?? 0, 2) }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3">Delivery Fee:</th>
                                            <th>${{ number_format($assignment->delivery_fee ?? 0, 2) }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3">Total:</th>
                                            <th>${{ number_format(($assignment->order->total ?? 0) + ($assignment->delivery_fee ?? 0), 2) }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Actions & Tracking -->
                <div class="col-md-4">
                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            @if($assignment->status === 'pending')
                            <button type="button" class="btn btn-success btn-block assign-personnel" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-user-plus"></i> Assign Personnel
                            </button>
                            @endif

                            @if(in_array($assignment->status, ['assigned', 'picked_up', 'in_transit']))
                            <button type="button" class="btn btn-primary btn-block update-status" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-edit"></i> Update Status
                            </button>
                            
                            <button type="button" class="btn btn-info btn-block track-assignment" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-map-marker-alt"></i> Track Delivery
                            </button>
                            @endif

                            @if($assignment->personnel)
                            <a href="tel:{{ $assignment->personnel->phone }}" class="btn btn-warning btn-block">
                                <i class="fas fa-phone"></i> Call Personnel
                            </a>
                            @endif

                            @if($assignment->order->customer)
                            <a href="tel:{{ $assignment->order->customer->phone ?? $assignment->order->customer_phone }}" 
                               class="btn btn-secondary btn-block">
                                <i class="fas fa-phone"></i> Call Customer
                            </a>
                            @endif
                        </div>
                    </div>

                    <!-- Tracking History -->
                    @if($assignment->tracking && $assignment->tracking->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Tracking History</h3>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach($assignment->tracking->sortByDesc('recorded_at') as $track)
                                <div class="time-label">
                                    <span class="bg-blue">{{ $track->recorded_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-map-marker-alt bg-blue"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            <strong>Location Update</strong><br>
                                            @if($track->latitude && $track->longitude)
                                            Lat: {{ $track->latitude }}, Lng: {{ $track->longitude }}<br>
                                            @endif
                                            @if($track->address)
                                            Address: {{ $track->address }}<br>
                                            @endif
                                            Speed: {{ $track->speed ?? 0 }} km/h
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Assignment Timeline -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Assignment Timeline</h3>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @if($assignment->assigned_at)
                                <div class="time-label">
                                    <span class="bg-green">{{ $assignment->assigned_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-user-plus bg-green"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Assignment created and personnel assigned
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if($assignment->picked_up_at)
                                <div class="time-label">
                                    <span class="bg-yellow">{{ $assignment->picked_up_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-box bg-yellow"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Order picked up from restaurant
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if($assignment->delivered_at)
                                <div class="time-label">
                                    <span class="bg-blue">{{ $assignment->delivered_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-check-circle bg-blue"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Order delivered to customer
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include modals from index page -->
@include('Delivery::assignments.partials.modals')
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Assign Personnel Modal
    $(document).on('click', '.assign-personnel', function() {
        const assignmentId = $(this).data('assignment-id');
        $('#assignmentId').val(assignmentId);
        
        // Load available personnel
        $.get('{{ route("delivery.assignments.available-personnel") }}')
        .done(function(personnel) {
            const select = $('#personnelSelect');
            select.empty().append('<option value="">Choose personnel...</option>');
            
            personnel.forEach(function(person) {
                select.append(`<option value="${person.id}">${person.name} (${person.vehicle_type})</option>`);
            });
            
            $('#assignPersonnelModal').modal('show');
        })
        .fail(function() {
            toastr.error('Failed to load available personnel');
        });
    });

    // Confirm assignment
    $('#confirmAssign').on('click', function() {
        const assignmentId = $('#assignmentId').val();
        const personnelId = $('#personnelSelect').val();
        
        if (!personnelId) {
            toastr.error('Please select personnel');
            return;
        }

        $.post(`/delivery/assignments/${assignmentId}/assign-personnel`, {
            personnel_id: personnelId,
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#assignPersonnelModal').modal('hide');
                location.reload(); // Reload to show updated assignment
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to assign personnel');
        });
    });

    // Update Status Modal
    $(document).on('click', '.update-status', function() {
        const assignmentId = $(this).data('assignment-id');
        $('#statusAssignmentId').val(assignmentId);
        $('#updateStatusModal').modal('show');
    });

    // Confirm status update
    $('#confirmStatusUpdate').on('click', function() {
        const assignmentId = $('#statusAssignmentId').val();
        const status = $('#statusSelect').val();
        const notes = $('#statusNotes').val();
        
        if (!status) {
            toastr.error('Please select a status');
            return;
        }

        $.post(`/delivery/assignments/${assignmentId}/update-status`, {
            status: status,
            notes: notes,
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#updateStatusModal').modal('hide');
                location.reload(); // Reload to show updated status
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to update status');
        });
    });

    // Track Assignment Modal
    $(document).on('click', '.track-assignment', function() {
        const assignmentId = $(this).data('assignment-id');
        
        // Load tracking information
        $.get(`/delivery/tracking/assignment/${assignmentId}`)
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#trackingStatus').text(data.status || '-');
                $('#trackingPersonnel').text(data.personnel_name || '-');
                $('#trackingLastUpdate').text(data.last_update || '-');
                $('#trackingEstimated').text(data.estimated_delivery || '-');
                
                $('#trackAssignmentModal').modal('show');
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to load tracking information');
        });
    });
});
</script>
@endpush