@extends('layouts.master')

@section('title', 'KOT Orders Management')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.kot-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.kot-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.kot-priority-urgent {
    border-left: 4px solid #dc3545;
}

.kot-priority-high {
    border-left: 4px solid #ffc107;
}

.kot-priority-normal {
    border-left: 4px solid #007bff;
}

.kot-priority-low {
    border-left: 4px solid #6c757d;
}

.time-indicator {
    font-weight: 600;
}

.time-overdue {
    color: #dc3545;
    animation: blink 1s infinite;
}

.time-warning {
    color: #ffc107;
}

.time-normal {
    color: #28a745;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

.filter-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.status-filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-filter-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    background: #fff;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
}

.status-filter-btn.active {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.status-filter-btn:hover {
    background: #e9ecef;
}

.status-filter-btn.active:hover {
    background: #0056b3;
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Kitchen</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ KOT Orders</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('kitchen-display.index') }}" class="btn btn-success">
                <i class="fas fa-tv"></i> Kitchen Display
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-warning-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Pending KOTs</h6>
                            <h2 class="text-white mb-0 number-font" id="pending-kots">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-clock text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-info-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Preparing</h6>
                            <h2 class="text-white mb-0 number-font" id="preparing-kots">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-fire text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-success-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Ready</h6>
                            <h2 class="text-white mb-0 number-font" id="ready-kots">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-check-circle text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
            <div class="card overflow-hidden sales-card bg-primary-gradient">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="my-auto">
                            <h6 class="card-title mb-0 text-white">Completed Today</h6>
                            <h2 class="text-white mb-0 number-font" id="completed-kots">0</h2>
                        </div>
                        <div class="my-auto ml-auto">
                            <i class="fas fa-trophy text-white fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group mb-0">
                    <label for="kitchen-filter">Kitchen</label>
                    <select class="form-control select2" id="kitchen-filter">
                        <option value="">All Kitchens</option>
                        @foreach($kitchens as $kitchen)
                            <option value="{{ $kitchen->id }}">{{ $kitchen->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-0">
                    <label for="status-filter">Status</label>
                    <select class="form-control" id="status-filter">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status }}">{{ ucfirst($status) }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-0">
                    <label for="priority-filter">Priority</label>
                    <select class="form-control" id="priority-filter">
                        <option value="">All Priorities</option>
                        @foreach($priorities as $priority)
                            <option value="{{ $priority }}">{{ ucfirst($priority) }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-0">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="button" class="btn btn-primary mr-2" id="apply-filters">
                            <i class="fas fa-filter"></i> Apply
                        </button>
                        <button type="button" class="btn btn-secondary" id="clear-filters">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KOT Orders Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">KOT Orders</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table key-buttons text-md-nowrap" id="kot-orders-table" data-page-length='25'>
                    <thead>
                        <tr>
                            <th class="border-bottom-0">#</th>
                            <th class="border-bottom-0">KOT Number</th>
                            <th class="border-bottom-0">Kitchen</th>
                            <th class="border-bottom-0">Order #</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Priority</th>
                            <th class="border-bottom-0">Assigned To</th>
                            <th class="border-bottom-0">Elapsed Time</th>
                            <th class="border-bottom-0">Remaining Time</th>
                            <th class="border-bottom-0">Created</th>
                            <th class="border-bottom-0">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update KOT Status</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="updateStatusForm">
                <input type="hidden" id="kot_order_id" name="kot_order_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="status">Status <span class="text-danger">*</span></label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="preparing">Preparing</option>
                            <option value="ready">Ready</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="form-group" id="assigned-to-group" style="display: none;">
                        <label for="assigned_to">Assign To</label>
                        <select class="form-control select2" id="assigned_to" name="assigned_to">
                            <option value="">Select User</option>
                            <!-- Users will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group" id="reason-group" style="display: none;">
                        <label for="reason">Cancellation Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#kot-orders-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("kot-orders.index") }}',
                type: 'GET',
                data: function(d) {
                    d.kitchen_id = $('#kitchen-filter').val();
                    d.status = $('#status-filter').val();
                    d.priority = $('#priority-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'kot_number', name: 'kot_number' },
                { data: 'kitchen_name', name: 'kitchen.name' },
                { data: 'order_number', name: 'order.order_number' },
                { data: 'status_badge', name: 'status' },
                { data: 'priority_badge', name: 'priority' },
                { data: 'assigned_to_name', name: 'assignedTo.name' },
                { data: 'elapsed_time', name: 'elapsed_time' },
                { data: 'remaining_time', name: 'remaining_time' },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[9, 'desc']] // Order by created_at descending
        });
    }

    // Initialize table
    initializeDataTable();

    // Apply filters
    $('#apply-filters').on('click', function() {
        table.draw();
    });

    // Clear filters
    $('#clear-filters').on('click', function() {
        $('#kitchen-filter').val('').trigger('change');
        $('#status-filter').val('');
        $('#priority-filter').val('');
        table.draw();
    });

    // Handle status update
    $(document).on('click', '.update-status', function() {
        const kotOrderId = $(this).data('kot-id');
        const currentStatus = $(this).data('current-status');

        $('#kot_order_id').val(kotOrderId);
        $('#status').val(currentStatus);

        $('#updateStatusModal').modal('show');
    });

    // Handle status form submission
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Updating...');

        const kotOrderId = $('#kot_order_id').val();
        const formData = new FormData(this);

        $.ajax({
            url: `{{ url('kot-orders') }}/${kotOrderId}/status`,
            method: 'PATCH',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal("Success!", "KOT status updated successfully", "success");

                    // Reset form and close modal
                    $('#updateStatusForm')[0].reset();
                    $('#updateStatusModal').modal('hide');

                    // Refresh table
                    table.draw();
                } else {
                    swal("Error!", response.message || "Failed to update KOT status", "error");
                }
            },
            error: function(xhr) {
                console.error('Error updating KOT status:', xhr);
                let errorMessage = "Failed to update KOT status";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                swal("Error!", errorMessage, "error");
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Auto-refresh every 30 seconds
    setInterval(function() {
        table.draw(false); // false to maintain current page
    }, 30000);
});
</script>
@endsection
