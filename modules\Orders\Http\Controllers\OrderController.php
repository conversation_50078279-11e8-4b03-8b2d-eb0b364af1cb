<?php

namespace Modules\Orders\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Http\Requests\UpdateOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class OrderController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display a listing of the resource (API).
     */
    public function index(Request $request)
    {
        try {
            $user = auth()->user();

            // Check if user is assigned to a branch
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // Automatically set branch_id filter from authenticated user
            $filters = $request->all();
            $filters['branch_id'] = $user->branch_id;

            $orders = $this->orderService->getAllOrders($filters);

            return response()->json([
                'success' => true,
                'data' => $orders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display orders management view (Web).
     */
    public function indexView()
    {
        return view('orders::index');
    }

    /**
     * Show the form for creating a new order (Web).
     */
    public function create()
    {
        $user = auth()->user();

        // Get customers for the current branch
        $customers = \App\Models\Customer::where('branch_id', $user->branch_id)->get();

        // Get tables for the current branch
        $tables = \App\Models\Table::where('branch_id', $user->branch_id)->get();

        return view('orders::create', compact('customers', 'tables'));
    }

    /**
     * Display the specified order (Web).
     */
    public function showView(string $id)
    {
        try {
            $order = $this->orderService->getOrderById($id);
            return view('orders::show', compact('order'));
        } catch (\Exception $e) {
            return redirect()->route('orders.index')->with('error', 'Order not found');
        }
    }

    /**
     * Show the form for editing the specified order (Web).
     */
    public function edit(string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->getOrderById($id);

            // Get customers for the current branch
            $customers = \App\Models\Customer::where('branch_id', $user->branch_id)->get();

            // Get tables for the current branch
            $tables = \App\Models\Table::where('branch_id', $user->branch_id)->get();

            return view('orders::edit', compact('order', 'customers', 'tables'));
        } catch (\Exception $e) {
            return redirect()->route('orders.index')->with('error', 'Order not found');
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            $user = auth()->user();
            
            // Check if user is assigned to a branch
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            // Automatically set branch_id and tenant_id from authenticated user
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;

            $order = $this->orderService->createOrder($data);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => $order
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $order = $this->orderService->getOrderById($id);

            return response()->json([
                'success' => true,
                'data' => $order
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateOrderRequest $request, string $id)
    {
        try {
            $order = $this->orderService->updateOrder($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Order updated successfully',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $this->orderService->deleteOrder($id);

            return response()->json([
                'success' => true,
                'message' => 'Order deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,preparing,ready,delivered,cancelled'
        ]);

        try {
            $order = $this->orderService->updateOrderStatus($id, $request->status);
            
            // Generate KOT if order is confirmed and doesn't already have an active KOT
            if ($request->status === 'confirmed') {
                try {
                    // Check if order already has an active KOT
                    if (!$order->hasActiveKot()) {
                        // Load order items with menu items for KOT creation
                        $order->load(['orderItems.menuItem']);
                        $this->kitchenService->createKotFromOrder($order);
                        \Log::info('KOT created for order ' . $order->id . ' during status update');
                    } else {
                        \Log::info('Order ' . $order->id . ' already has an active KOT, skipping creation');
                    }
                } catch (\Exception $e) {
                    // Log the error but don't fail the order status update
                    \Log::error('Failed to create KOT for order ' . $order->id . ': ' . $e->getMessage());
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'data' => $order
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'error' => 'Order not found',
                'message' => 'The requested order does not exist'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update order status',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order items.
     */
    public function getOrderItems(string $id)
    {
        $orderItems = $this->orderService->getOrderItems($id);
        return response()->json($orderItems);
    }

    /**
     * Add item to order.
     */
    public function addOrderItem(Request $request, string $id)
    {
        $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        $orderItem = $this->orderService->addOrderItem($id, $request->all());
        return response()->json($orderItem, 201);
    }

    /**
     * Remove item from order.
     */
    public function removeOrderItem(string $id, string $itemId)
    {
        $this->orderService->removeOrderItem($id, $itemId);
        return response()->json(null, 204);
    }

    /**
     * Get valid addons for a menu item.
     */
    public function getMenuItemAddons(string $menuItemId)
    {
        $addons = \App\Models\MenuItemAddon::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(['id', 'name', 'price', 'addon_group_name', 'is_required', 'max_quantity']);

        return response()->json($addons);
    }

    /**
     * Get orders data for DataTable (Web)
     */
    public function getOrdersData(Request $request)
    {
        $user = auth()->user();

        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = \App\Models\Order::with(['customer', 'table', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('order_type')) {
            $query->where('order_type', $request->order_type);
        }

        if ($request->filled('date')) {
            $query->whereDate('created_at', $request->date);
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('customer_name', function ($order) {
                return $order->customer ? $order->customer->name : 'عميل مجهول';
            })
            ->addColumn('table_name', function ($order) {
                return $order->table ? $order->table->name : 'غير محدد';
            })
            ->addColumn('status_badge', function ($order) {
                $statusClasses = [
                    'pending' => 'badge-warning',
                    'confirmed' => 'badge-info',
                    'preparing' => 'badge-primary',
                    'ready' => 'badge-success',
                    'served' => 'badge-secondary',
                    'completed' => 'badge-success',
                    'cancelled' => 'badge-danger'
                ];
                $statusTexts = [
                    'pending' => 'معلق',
                    'confirmed' => 'مؤكد',
                    'preparing' => 'قيد التحضير',
                    'ready' => 'جاهز',
                    'served' => 'تم التقديم',
                    'completed' => 'مكتمل',
                    'cancelled' => 'ملغي'
                ];
                $class = $statusClasses[$order->status] ?? 'badge-secondary';
                $text = $statusTexts[$order->status] ?? $order->status;
                return '<span class="badge ' . $class . '">' . $text . '</span>';
            })
            ->addColumn('type_badge', function ($order) {
                $typeClasses = [
                    'dine_in' => 'badge-success',
                    'takeaway' => 'badge-warning',
                    'delivery' => 'badge-info',
                    'online' => 'badge-primary'
                ];
                $typeTexts = [
                    'dine_in' => 'تناول في المطعم',
                    'takeaway' => 'طلب خارجي',
                    'delivery' => 'توصيل',
                    'online' => 'طلب أونلاين'
                ];
                $class = $typeClasses[$order->order_type] ?? 'badge-secondary';
                $text = $typeTexts[$order->order_type] ?? $order->order_type;
                return '<span class="badge ' . $class . '">' . $text . '</span>';
            })
            ->addColumn('formatted_total', function ($order) {
                return '$' . number_format($order->total_amount, 2);
            })
            ->addColumn('formatted_date', function ($order) {
                return $order->created_at->format('Y-m-d H:i');
            })
            ->addColumn('action', function ($order) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('orders.show', $order->id) . '" class="btn btn-sm btn-outline-primary" title="عرض"><i class="mdi mdi-eye"></i></a>';
                $actions .= '<a href="' . route('orders.edit', $order->id) . '" class="btn btn-sm btn-outline-warning" title="تعديل"><i class="mdi mdi-pencil"></i></a>';
                $actions .= '<button class="btn btn-sm btn-outline-danger delete-order" data-id="' . $order->id . '" title="حذف"><i class="mdi mdi-delete"></i></button>';
                if ($order->status !== 'cancelled') {
                    $actions .= '<a href="' . route('pos.orders.kot', $order->id) . '" class="btn btn-sm btn-success" title="طباعة KOT"><i class="mdi mdi-printer"></i></a>';
                }
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['status_badge', 'type_badge', 'action'])
            ->make(true);
    }

    /**
     * Get orders statistics for web view
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $today = now()->toDateString();
            $branchId = $user->branch_id;

            $statistics = [
                'total_today' => \App\Models\Order::where('branch_id', $branchId)
                    ->whereDate('created_at', $today)
                    ->count(),
                'pending' => \App\Models\Order::where('branch_id', $branchId)
                    ->where('status', 'pending')
                    ->count(),
                'completed' => \App\Models\Order::where('branch_id', $branchId)
                    ->where('status', 'completed')
                    ->count(),
                'sales_today' => \App\Models\Order::where('branch_id', $branchId)
                    ->whereDate('created_at', $today)
                    ->where('status', '!=', 'cancelled')
                    ->sum('total_amount')
            ];

            return response()->json([
                'success' => true,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get orders for cards view (Web)
     */
    public function getCardsData(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = \App\Models\Order::with(['customer', 'table', 'branch'])
                ->where('branch_id', $user->branch_id);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('order_type')) {
                $query->where('order_type', $request->order_type);
            }

            if ($request->filled('date')) {
                $query->whereDate('created_at', $request->date);
            }

            // Order by latest first
            $query->orderBy('created_at', 'desc');

            // Paginate
            $perPage = $request->get('per_page', 12);
            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders->items(),
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm an order and create KOT
     */
    public function confirmOrder(Request $request, string $id)
    {
        try {
            $user = auth()->user();
            
            // Get the order
            $order = \App\Models\Order::findOrFail($id);
            
            // Verify order belongs to user's branch
            if ($order->branch_id !== $user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }
            
            // Check if order can be confirmed
            if ($order->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be confirmed. Current status: ' . $order->status
                ], 400);
            }
            
            // Update order status to confirmed
            $order = $this->orderService->updateOrderStatus($id, 'confirmed');
            
            // Generate KOT if not already exists
            try {
                // Check if order already has an active KOT
                if (!$order->hasActiveKot()) {
                    // Load order items with menu items for KOT creation
                    $order->load(['orderItems.menuItem']);
                    $kotOrders = $this->kitchenService->createKotFromOrder($order);

                    return response()->json([
                        'success' => true,
                        'message' => 'Order confirmed successfully and KOT created',
                        'data' => [
                            'order' => $order,
                            'kot_orders' => $kotOrders
                        ]
                    ]);
                } else {
                    // Order already has KOT, just return success
                    return response()->json([
                        'success' => true,
                        'message' => 'Order confirmed successfully (KOT already exists)',
                        'data' => [
                            'order' => $order,
                            'kot_orders' => $order->kotOrders
                        ]
                    ]);
                }
            } catch (\Exception $e) {
                // Log the error but still return success for order confirmation
                \Log::error('Failed to create KOT for order ' . $order->id . ': ' . $e->getMessage());

                return response()->json([
                    'success' => true,
                    'message' => 'Order confirmed successfully, but KOT creation failed',
                    'data' => [
                        'order' => $order,
                        'kot_error' => $e->getMessage()
                    ]
                ]);
            }
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm order: ' . $e->getMessage()
            ], 500);
        }
    }
}