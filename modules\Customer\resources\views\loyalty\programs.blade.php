@extends('layouts.app')

@section('title', 'برامج الولاء')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">برامج الولاء</h3>
                    <div class="card-tools">
                        <a href="{{ route('loyalty-programs.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> إضافة برنامج جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="loyalty-programs-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم البرنامج</th>
                                    <th>النوع</th>
                                    <th>نقاط لكل وحدة</th>
                                    <th>عملة لكل نقطة</th>
                                    <th>الحد الأدنى للاستبدال</th>
                                    <th>فترة البرنامج</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا البرنامج؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#loyalty-programs-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: '{{ route("loyalty-programs.data") }}',
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'type_formatted', name: 'type'},
            {data: 'points_per_currency_unit', name: 'points_per_currency_unit'},
            {data: 'currency_per_point', name: 'currency_per_point'},
            {data: 'minimum_points_redemption', name: 'minimum_points_redemption'},
            {data: 'date_range', name: 'date_range', orderable: false},
            {data: 'status', name: 'is_active'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Delete program
    $(document).on('click', '.delete-program', function() {
        var programId = $(this).data('id');
        var deleteUrl = '{{ route("loyalty-programs.destroy", ":id") }}';
        deleteUrl = deleteUrl.replace(':id', programId);
        
        $('#deleteForm').attr('action', deleteUrl);
        $('#deleteModal').modal('show');
    });
});
</script>
@endpush