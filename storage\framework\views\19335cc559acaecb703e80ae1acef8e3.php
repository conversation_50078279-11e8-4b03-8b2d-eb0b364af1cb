<?php $__env->startSection('css'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<style>
.order-status {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 600;
    text-transform: uppercase;
}
.status-pending { background-color: #ffeaa7; color: #2d3436; }
.status-confirmed { background-color: #74b9ff; color: #ffffff; }
.status-preparing { background-color: #fd79a8; color: #ffffff; }
.status-ready { background-color: #00b894; color: #ffffff; }
.status-served { background-color: #6c5ce7; color: #ffffff; }
.status-completed { background-color: #00cec9; color: #ffffff; }
.status-cancelled { background-color: #e17055; color: #ffffff; }

.order-type {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}
.type-dine_in { background-color: #e8f5e8; color: #2e7d32; }
.type-takeaway { background-color: #fff3e0; color: #f57c00; }
.type-delivery { background-color: #e3f2fd; color: #1976d2; }
.type-online { background-color: #f3e5f5; color: #7b1fa2; }

@media print {
    .no-print { display: none !important; }
    .card { border: none !important; box-shadow: none !important; }
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">تفاصيل الطلب</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الطلبات / التفاصيل</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content no-print">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary btn-icon ml-2">
                <i class="mdi mdi-arrow-left"></i>
            </a>
            <a href="<?php echo e(route('orders.edit', $order->id ?? 1)); ?>" class="btn btn-warning btn-icon ml-2">
                <i class="mdi mdi-pencil"></i>
            </a>
            <button onclick="window.print()" class="btn btn-info btn-icon ml-2">
                <i class="mdi mdi-printer"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Order Header Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">الطلب #<?php echo e($order->order_number ?? 'ORD-001'); ?></h3>
                    <div class="d-flex align-items-center">
                        <span class="order-status status-<?php echo e($order->status ?? 'pending'); ?> ml-2">
                            <?php echo e($order->status_text ?? 'معلق'); ?>

                        </span>
                        <span class="order-type type-<?php echo e($order->order_type ?? 'dine_in'); ?>">
                            <?php echo e($order->order_type_text ?? 'تناول في المطعم'); ?>

                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات العميل</h6>
                            <p class="mb-1"><strong>الاسم:</strong> <?php echo e($order->customer->name ?? 'عميل مجهول'); ?></p>
                            <p class="mb-1"><strong>الهاتف:</strong> <?php echo e($order->customer->phone ?? 'غير محدد'); ?></p>
                            <p class="mb-1"><strong>البريد الإلكتروني:</strong> <?php echo e($order->customer->email ?? 'غير محدد'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">تفاصيل الطلب</h6>
                            <p class="mb-1"><strong>الطاولة:</strong> <?php echo e($order->table->name ?? 'غير محدد'); ?></p>
                            <p class="mb-1"><strong>تاريخ الطلب:</strong> <?php echo e($order->created_at ? $order->created_at->format('Y-m-d H:i') : now()->format('Y-m-d H:i')); ?></p>
                            <p class="mb-1"><strong>الموظف:</strong> <?php echo e($order->user->name ?? auth()->user()->name); ?></p>
                        </div>
                    </div>
                    
                    <?php if($order->special_instructions ?? false): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-muted">ملاحظات خاصة</h6>
                            <p class="alert alert-info"><?php echo e($order->special_instructions); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">عناصر الطلب</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>العنصر</th>
                                    <th class="text-center">السعر</th>
                                    <th class="text-center">الكمية</th>
                                    <th class="text-center">المجموع</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $sampleItems = [
                                        ['name' => 'برجر كلاسيك', 'price' => 15.99, 'quantity' => 2, 'notes' => 'بدون بصل'],
                                        ['name' => 'بيتزا مارجريتا', 'price' => 22.50, 'quantity' => 1, 'notes' => ''],
                                        ['name' => 'كوكا كولا', 'price' => 3.50, 'quantity' => 2, 'notes' => 'مع ثلج']
                                    ];
                                    $subtotal = 0;
                                ?>
                                
                                <?php $__empty_1 = true; $__currentLoopData = $order->items ?? $sampleItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <?php
                                        $itemTotal = ($item['price'] ?? $item->price ?? 0) * ($item['quantity'] ?? $item->quantity ?? 1);
                                        $subtotal += $itemTotal;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e($item['name'] ?? $item->menu_item->name ?? 'عنصر غير محدد'); ?></strong>
                                            <?php if(isset($item->variants) && $item->variants->count() > 0): ?>
                                                <br><small class="text-muted">
                                                    <?php $__currentLoopData = $item->variants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php echo e($variant->name); ?><?php echo e(!$loop->last ? ', ' : ''); ?>

                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">$<?php echo e(number_format($item['price'] ?? $item->price ?? 0, 2)); ?></td>
                                        <td class="text-center"><?php echo e($item['quantity'] ?? $item->quantity ?? 1); ?></td>
                                        <td class="text-center"><strong>$<?php echo e(number_format($itemTotal, 2)); ?></strong></td>
                                        <td><?php echo e($item['notes'] ?? $item->notes ?? ''); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">لا توجد عناصر في هذا الطلب</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Summary Card -->
    <div class="row">
        <div class="col-md-6 ml-auto">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ملخص الطلب</h3>
                </div>
                <div class="card-body">
                    <?php
                        $tax = $subtotal * 0.15; // 15% tax
                        $discount = $order->discount_amount ?? 0;
                        $total = $subtotal + $tax - $discount;
                    ?>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>المجموع الفرعي:</strong></td>
                            <td class="text-right">$<?php echo e(number_format($subtotal, 2)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الضريبة (15%):</strong></td>
                            <td class="text-right">$<?php echo e(number_format($tax, 2)); ?></td>
                        </tr>
                        <?php if($discount > 0): ?>
                        <tr>
                            <td><strong>الخصم:</strong></td>
                            <td class="text-right text-danger">-$<?php echo e(number_format($discount, 2)); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr class="table-active">
                            <td><strong>المجموع الكلي:</strong></td>
                            <td class="text-right"><strong>$<?php echo e(number_format($total, 2)); ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Actions Card -->
    <div class="row no-print">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إجراءات الطلب</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>تحديث حالة الطلب</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <button class="btn btn-outline-warning btn-sm" onclick="updateOrderStatus('confirmed')">
                                    <i class="mdi mdi-check"></i> تأكيد الطلب
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="updateOrderStatus('preparing')">
                                    <i class="mdi mdi-chef-hat"></i> بدء التحضير
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="updateOrderStatus('ready')">
                                    <i class="mdi mdi-bell"></i> جاهز للتقديم
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="updateOrderStatus('served')">
                                    <i class="mdi mdi-silverware"></i> تم التقديم
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="updateOrderStatus('completed')">
                                    <i class="mdi mdi-check-all"></i> مكتمل
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>إجراءات أخرى</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="<?php echo e(route('pos.orders.kot', $order->id ?? 1)); ?>" class="btn btn-success btn-sm">
                                    <i class="mdi mdi-printer"></i> طباعة KOT
                                </a>
                                <a href="<?php echo e(route('orders.edit', $order->id ?? 1)); ?>" class="btn btn-warning btn-sm">
                                    <i class="mdi mdi-pencil"></i> تعديل الطلب
                                </a>
                                <button class="btn btn-info btn-sm" onclick="window.print()">
                                    <i class="mdi mdi-printer"></i> طباعة الفاتورة
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="cancelOrder()">
                                    <i class="mdi mdi-cancel"></i> إلغاء الطلب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
});

// Update order status
function updateOrderStatus(status) {
    const orderId = <?php echo e($order->id ?? 1); ?>;
    
    // For confirmation, work directly without modal
    if (status === 'confirmed') {
        // Show loading indicator on the button
        const confirmBtn = $('button[onclick="updateOrderStatus(\'confirmed\')"]');
        const originalText = confirmBtn.html();
        confirmBtn.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> جاري التأكيد...');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order number and KOT information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show toast notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                    
                    // Update the order status display without reloading
                    $('.order-status').removeClass('status-pending').addClass('status-confirmed').text('مؤكد');
                    
                    // Hide the confirm button since order is now confirmed
                    confirmBtn.closest('.btn-group-vertical').find('button[onclick="updateOrderStatus(\'confirmed\')"]').hide();
                } else {
                    showOrderErrorToast(response.message || "حدث خطأ أثناء تأكيد الطلب");
                }
                
                // Restore button state
                confirmBtn.prop('disabled', false).html(originalText);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "حدث خطأ أثناء تأكيد الطلب";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                confirmBtn.prop('disabled', false).html(originalText);
            }
        });
    } else {
        // For other statuses, show confirmation modal
        swal({
            title: "تأكيد التحديث",
            text: "هل أنت متأكد من تحديث حالة الطلب؟",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "نعم، حدث!",
            cancelButtonText: "إلغاء"
        }, function() {
            $.ajax({
                url: `/api/orders/${orderId}/status`,
                method: 'PUT',
                data: { status: status },
                success: function(response) {
                    swal("تم التحديث!", "تم تحديث حالة الطلب بنجاح", "success");
                    location.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء تحديث حالة الطلب", "error");
                }
            });
        });
    }
}

// Cancel order
function cancelOrder() {
    const orderId = <?php echo e($order->id ?? 1); ?>;
    
    swal({
        title: "إلغاء الطلب",
        text: "هل أنت متأكد من إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "نعم، ألغ الطلب!",
        cancelButtonText: "تراجع"
    }, function() {
        updateOrderStatus('cancelled');
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\test\epis - Copy\modules\Orders\Providers/../resources/views/show.blade.php ENDPATH**/ ?>