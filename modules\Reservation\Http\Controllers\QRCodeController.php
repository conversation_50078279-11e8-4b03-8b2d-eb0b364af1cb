<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Reservation\Services\QRCodeService;
use Modules\Reservation\Services\TableService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class QRCodeController extends Controller
{
    protected QRCodeService $qrCodeService;
    protected TableService $tableService;

    public function __construct(QRCodeService $qrCodeService, TableService $tableService)
    {
        $this->qrCodeService = $qrCodeService;
        $this->tableService = $tableService;
    }

    /**
     * Generate QR code for a table.
     */
    public function generateTableQR(Request $request, int $tableId): JsonResponse
    {
        try {
            $table = $this->tableService->getTableById($tableId);
            
            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            $qrUrl = $this->qrCodeService->generateTableQR($table);

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $tableId,
                    'qr_url' => $qrUrl,
                    'qr_content' => $this->qrCodeService->generateQRContent($table)
                ],
                'message' => 'QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Generate menu QR code for a table.
     */
    public function generateMenuQR(Request $request, int $tableId): JsonResponse
    {
        try {
            $table = $this->tableService->getTableById($tableId);
            
            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            $qrUrl = $this->qrCodeService->generateMenuQR($table);

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $tableId,
                    'menu_qr_url' => $qrUrl,
                    'type' => 'menu'
                ],
                'message' => 'Menu QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate menu QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Generate order QR code for a table.
     */
    public function generateOrderQR(Request $request, int $tableId): JsonResponse
    {
        try {
            $table = $this->tableService->getTableById($tableId);
            
            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            $qrUrl = $this->qrCodeService->generateOrderQR($table);

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $tableId,
                    'order_qr_url' => $qrUrl,
                    'type' => 'order'
                ],
                'message' => 'Order QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate order QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Validate QR code and get table information.
     */
    public function validateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'qr_code' => 'required|string'
            ]);

            $qrCode = $request->get('qr_code');
            $validation = $this->qrCodeService->validateQRCode($qrCode);

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid QR code',
                    'error' => $validation['error'] ?? 'QR code validation failed'
                ], 400);
            }

            $tableData = $this->qrCodeService->getTableFromQR($qrCode);

            return response()->json([
                'success' => true,
                'data' => $tableData,
                'validation' => $validation,
                'message' => 'QR code validated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate QR code',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Batch generate QR codes for multiple tables.
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_ids' => 'required|array',
                'table_ids.*' => 'integer|exists:tables,id',
                'types' => 'array',
                'types.*' => 'string|in:table,menu,order'
            ]);

            $tableIds = $request->get('table_ids');
            $types = $request->get('types', ['table']);

            $results = $this->qrCodeService->batchGenerate($tableIds, ['types' => $types]);

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'QR codes generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR codes',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get QR code content for a table.
     */
    public function getQRContent(int $tableId): JsonResponse
    {
        try {
            $table = $this->tableService->getTableById($tableId);
            
            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            $content = $this->qrCodeService->generateQRContent($table);

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $tableId,
                    'qr_content' => $content,
                    'decoded_content' => json_decode($content, true)
                ],
                'message' => 'QR content retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get QR content',
                'error' => $e->getMessage()
            ], 400);
        }
    }
}