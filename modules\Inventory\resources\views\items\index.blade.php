@extends('layouts.master')

@push('inventory-styles')
<style>
.item-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.stock-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.stock-indicator.high { background-color: #28a745; }
.stock-indicator.medium { background-color: #ffc107; }
.stock-indicator.low { background-color: #dc3545; }
.stock-indicator.out { background-color: #6c757d; }

.filter-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إدارة المواد</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="exportItems()">
                <i class="mdi mdi-download"></i> تصدير
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="importItems()">
                <i class="mdi mdi-upload"></i> استيراد
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary" onclick="addItem()">
                <i class="mdi mdi-plus"></i> إضافة مادة جديدة
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Filters -->
<div class="row">
    <div class="col-xl-12">
        <div class="filter-card">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>البحث</label>
                        <input type="text" id="search-input" class="form-control" placeholder="البحث بالاسم أو الرمز...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الفئة</label>
                        <select id="category-filter" class="form-control select2">
                            <option value="">جميع الفئات</option>
                            <option value="ingredients">المكونات</option>
                            <option value="beverages">المشروبات</option>
                            <option value="packaging">التعبئة والتغليف</option>
                            <option value="cleaning">مواد التنظيف</option>
                            <option value="equipment">المعدات</option>
                            <option value="general">عام</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>حالة المخزون</label>
                        <select id="stock-filter" class="form-control select2">
                            <option value="">جميع الحالات</option>
                            <option value="in_stock">متوفر</option>
                            <option value="low_stock">مخزون منخفض</option>
                            <option value="out_of_stock">نفد المخزون</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-primary mr-2" onclick="applyFilters()">
                                <i class="mdi mdi-filter"></i> تطبيق
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="mdi mdi-filter-remove"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Items Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card table-container">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">قائمة المواد</h4>
                    <div class="card-options">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                            <i class="mdi mdi-refresh"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="items-table" class="table table-striped table-bordered text-md-nowrap">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الصورة</th>
                                <th>اسم المادة</th>
                                <th>الرمز</th>
                                <th>الفئة</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>الحد الأقصى</th>
                                <th>التكلفة</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals will be included here -->
@include('inventory::items.modals')

@endsection

@push('inventory-scripts')
<script>
let itemsTable;

$(document).ready(function() {
    initializeDataTable();
});

function initializeDataTable() {
    itemsTable = $('#items-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.items.datatable") }}',
            data: function(d) {
                d.search = $('#search-input').val();
                d.category = $('#category-filter').val();
                d.stock_status = $('#stock-filter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { 
                data: 'image', 
                name: 'image', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    if (data) {
                        return `<img src="${data}" class="item-image" alt="صورة المادة">`;
                    }
                    return `<div class="item-image bg-light d-flex align-items-center justify-content-center">
                        <i class="mdi mdi-package-variant text-muted"></i>
                    </div>`;
                }
            },
            { data: 'product.name', name: 'product.name' },
            { data: 'product.sku', name: 'product.sku' },
            { 
                data: 'product.category', 
                name: 'product.category',
                render: function(data) {
                    const categories = {
                        'ingredients': 'المكونات',
                        'beverages': 'المشروبات',
                        'packaging': 'التعبئة والتغليف',
                        'cleaning': 'مواد التنظيف',
                        'equipment': 'المعدات',
                        'general': 'عام'
                    };
                    return categories[data] || data;
                }
            },
            { 
                data: 'current_stock', 
                name: 'current_stock',
                render: function(data, type, row) {
                    const indicator = getStockIndicator(data, row.minimum_stock, row.maximum_stock);
                    return `<span class="stock-indicator ${indicator}"></span>${data} ${row.product.unit?.symbol || ''}`;
                }
            },
            { data: 'minimum_stock', name: 'minimum_stock' },
            { data: 'maximum_stock', name: 'maximum_stock' },
            { 
                data: 'cost_per_unit', 
                name: 'cost_per_unit',
                render: function(data) {
                    return InventoryModule.formatCurrency(data);
                }
            },
            { 
                data: 'status', 
                name: 'status',
                render: function(data, type, row) {
                    return getStatusBadge(row.current_stock, row.minimum_stock);
                }
            },
            { 
                data: 'last_updated', 
                name: 'last_updated',
                render: function(data) {
                    return InventoryModule.formatDate(data);
                }
            },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="table-actions">
                            <button type="button" class="btn btn-sm btn-info btn-action" onclick="viewItem(${row.id})" title="عرض">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary btn-action" onclick="editItem(${row.id})" title="تعديل">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-success btn-action" onclick="updateStock(${row.id})" title="تحديث المخزون">
                                <i class="mdi mdi-cube-outline"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-warning btn-action" onclick="viewMovements(${row.id})" title="الحركات">
                                <i class="mdi mdi-history"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger btn-action" onclick="deleteItem(${row.id})" title="حذف">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[2, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: 'طباعة',
                className: 'btn btn-info btn-sm'
            }
        ]
    });
}

function getStockIndicator(current, minimum, maximum) {
    if (current <= 0) return 'out';
    if (current <= minimum) return 'low';
    if (maximum && current >= maximum * 0.8) return 'high';
    return 'medium';
}

function getStatusBadge(current, minimum) {
    if (current <= 0) {
        return '<span class="badge badge-secondary">نفد المخزون</span>';
    } else if (current <= minimum) {
        return '<span class="badge badge-danger">مخزون منخفض</span>';
    } else {
        return '<span class="badge badge-success">متوفر</span>';
    }
}

function applyFilters() {
    itemsTable.ajax.reload();
}

function clearFilters() {
    $('#search-input').val('');
    $('#category-filter').val('').trigger('change');
    $('#stock-filter').val('').trigger('change');
    itemsTable.ajax.reload();
}

function refreshTable() {
    itemsTable.ajax.reload();
}

function addItem() {
    $('#addItemModal').modal('show');
}

function viewItem(id) {
    // Load item details and show modal
    $.get(`{{ route('inventory.api.items.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateViewModal(data);
            $('#viewItemModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات المادة');
        });
}

function editItem(id) {
    // Load item details and show edit modal
    $.get(`{{ route('inventory.api.items.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateEditModal(data);
            $('#editItemModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات المادة');
        });
}

function updateStock(id) {
    // Show stock update modal
    $('#updateStockModal').modal('show');
    $('#updateStockModal').data('item-id', id);
}

function viewMovements(id) {
    // Redirect to movements page
    window.location.href = `{{ route('inventory.stock.movements.item', ':id') }}`.replace(':id', id);
}

function deleteItem(id) {
    InventoryModule.confirm('هل أنت متأكد من حذف هذه المادة؟', function() {
        $.ajax({
            url: `{{ route('inventory.api.items.destroy', ':id') }}`.replace(':id', id),
            type: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function() {
                InventoryModule.showSuccess('تم حذف المادة بنجاح');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء حذف المادة';
                InventoryModule.showError(message);
            }
        });
    });
}

function exportItems() {
    window.location.href = '{{ route("inventory.api.export") }}?format=excel';
}

function importItems() {
    $('#importItemsModal').modal('show');
}
</script>
@endpush
