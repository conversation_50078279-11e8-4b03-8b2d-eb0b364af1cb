<!-- Modern Header -->
<header class="main-header d-flex align-items-center px-4">
    <style>
        .main-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #64748b;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        .search-box {
            position: relative;
            max-width: 400px;
            width: 100%;
        }

        .search-input {
            border: 1px solid #e2e8f0;
            border-radius: 25px;
            padding: 10px 20px 10px 45px;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-btn {
            position: relative;
            background: none;
            border: none;
            padding: 10px;
            border-radius: 50%;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-btn:hover {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: #f1f5f9;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e2e8f0;
        }

        .user-info h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .user-info span {
            font-size: 12px;
            color: #64748b;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 10px 0;
            min-width: 200px;
        }

        .dropdown-item {
            padding: 10px 20px;
            font-size: 14px;
            color: #475569;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: #f1f5f9;
            color: var(--primary-color);
        }

        .dropdown-item i {
            width: 20px;
            margin-right: 10px;
        }

        .notification-dropdown {
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .notification-item:hover {
            background: #f8fafc;
            text-decoration: none;
            color: inherit;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .notification-content h6 {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .notification-content p {
            margin: 0;
            font-size: 12px;
            color: #64748b;
        }

        @media (max-width: 768px) {
            .search-box {
                display: none;
            }

            .user-info {
                display: none;
            }
        }
    </style>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Search Box -->
    <div class="search-box mx-4">
        <i class="fas fa-search search-icon"></i>
        <input type="text" class="form-control search-input" placeholder="البحث في النظام..." id="globalSearch">
    </div>

    <!-- Header Actions -->
    <div class="header-actions ms-auto">
        <!-- Fullscreen Toggle -->
        <button class="header-btn" onclick="toggleFullscreen()" title="ملء الشاشة">
            <i class="fas fa-expand"></i>
        </button>

        <!-- Notifications -->
        <div class="dropdown">
            <button class="header-btn" data-bs-toggle="dropdown" title="الإشعارات">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">4</span>
            </button>
            <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                <div class="notification-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">الإشعارات</h6>
                        <span class="badge bg-warning">4 جديد</span>
                    </div>
                </div>
                <div class="notification-list">
                    <a href="#" class="notification-item">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon bg-success me-3">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6>طلب جديد #1252</h6>
                                <p>طاولة 8 - طلب جديد بقيمة $45.50</p>
                                <small class="text-muted">منذ دقيقتين</small>
                            </div>
                        </div>
                    </a>
                    <a href="#" class="notification-item">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon bg-info me-3">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6>طلب جاهز للتقديم</h6>
                                <p>طلب #1248 - طاولة 3 جاهز</p>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                        </div>
                    </a>
                    <a href="#" class="notification-item">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon bg-primary me-3">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6>حجز جديد</h6>
                                <p>طاولة 12 - حجز لـ 6 أشخاص الساعة 8:00 م</p>
                                <small class="text-muted">منذ 10 دقائق</small>
                            </div>
                        </div>
                    </a>
                    <a href="#" class="notification-item">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon bg-warning me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6>تنبيه مخزون</h6>
                                <p>مكونات البيتزا تحتاج إعادة تموين</p>
                                <small class="text-muted">منذ 15 دقيقة</small>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="text-center p-3 border-top">
                    <a href="#" class="btn btn-sm btn-primary">عرض جميع الإشعارات</a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <button class="header-btn" title="الرسائل">
            <i class="fas fa-envelope"></i>
            <span class="notification-badge">2</span>
        </button>

        <!-- User Profile -->
        <div class="dropdown">
            <div class="user-profile" data-bs-toggle="dropdown">
                <img src="https://ui-avatars.com/api/?name=<?php echo e(Auth::user()->name ?? 'Admin'); ?>&background=6366f1&color=fff"
                     alt="User Avatar" class="user-avatar">
                <div class="user-info d-none d-md-block">
                    <h6><?php echo e(Auth::user()->name ?? 'مدير النظام'); ?></h6>
                    <span><?php echo e(Auth::user()->email ?? '<EMAIL>'); ?></span>
                </div>
                <i class="fas fa-chevron-down ms-2"></i>
            </div>
            <div class="dropdown-menu dropdown-menu-end">
                <a class="dropdown-item" href="#">
                    <i class="fas fa-user"></i>الملف الشخصي
                </a>
                <a class="dropdown-item" href="#">
                    <i class="fas fa-cog"></i>الإعدادات
                </a>
                <a class="dropdown-item" href="#">
                    <i class="fas fa-envelope"></i>الرسائل
                </a>
                <div class="dropdown-divider"></div>
                <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <a class="dropdown-item" href="#" onclick="event.preventDefault(); this.closest('form').submit();">
                        <i class="fas fa-sign-out-alt"></i>تسجيل الخروج
                    </a>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Fullscreen toggle
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // Global search functionality
        document.getElementById('globalSearch').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // Add your search logic here
            console.log('Searching for:', searchTerm);
        });
    </script>
</header><?php /**PATH D:\my dehive work\test\epis - Copy\resources\views/layouts/main-header.blade.php ENDPATH**/ ?>