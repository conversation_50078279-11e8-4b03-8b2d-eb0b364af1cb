<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'menu_item_id',
        'menu_item_name', // Store menu item name for historical reference
        'variant_id',
        'quantity',
        'unit_price',
        'total_price',
        'notes', // Item-specific notes/special instructions
        'special_instructions',
        'status',
        'sent_to_kitchen_at',
        'ready_at',
        'served_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'total_price' => 'decimal:2',
            'sent_to_kitchen_at' => 'datetime',
            'ready_at' => 'datetime',
            'served_at' => 'datetime',
        ];
    }

    // Relationships
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    public function variant()
    {
        return $this->belongsTo(MenuItemVariant::class);
    }

    public function addons()
    {
        return $this->hasMany(OrderItemAddon::class);
    }

    /**
     * Get KOT order items for this order item.
     */
    public function kotOrderItems()
    {
        return $this->hasMany(\Modules\Kitchen\Models\KotOrderItem::class);
    }

    /**
     * Get the active KOT order item for this order item.
     */
    public function activeKotOrderItem()
    {
        return $this->hasOne(\Modules\Kitchen\Models\KotOrderItem::class)
            ->whereIn('status', ['pending', 'preparing', 'ready']);
    }

    /**
     * Check if this order item has an active KOT.
     */
    public function hasActiveKot(): bool
    {
        return $this->activeKotOrderItem()->exists();
    }

    /**
     * Get the kitchen assigned to this order item's menu item.
     */
    public function assignedKitchen()
    {
        return $this->menuItem->assignedKitchen();
    }
}