<?php

namespace Modules\Kitchen\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Services\KotService;
use Modules\Kitchen\Http\Requests\UpdateKotStatusRequest;
use Modules\Kitchen\Http\Requests\UpdateKotPriorityRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class KotOrderController extends Controller
{
    public function __construct(
        private KotService $kotService
    ) {}

    /**
     * Display a listing of KOT orders.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            $kotOrders = KotOrder::with(['kitchen', 'order', 'assignedTo'])
                ->select([
                    'id', 'kitchen_id', 'order_id', 'kot_number', 'status', 'priority',
                    'estimated_prep_time_minutes', 'actual_prep_time_minutes',
                    'assigned_to', 'created_at', 'started_at', 'completed_at'
                ]);

            // Apply filters
            if ($request->filled('kitchen_id')) {
                $kotOrders->where('kitchen_id', $request->kitchen_id);
            }

            if ($request->filled('status')) {
                $kotOrders->where('status', $request->status);
            }

            if ($request->filled('priority')) {
                $kotOrders->where('priority', $request->priority);
            }

            return DataTables::of($kotOrders)
                ->addColumn('kitchen_name', function ($kotOrder) {
                    return $kotOrder->kitchen?->name ?? 'N/A';
                })
                ->addColumn('order_number', function ($kotOrder) {
                    return $kotOrder->order?->order_number ?? 'N/A';
                })
                ->addColumn('assigned_to_name', function ($kotOrder) {
                    return $kotOrder->assignedTo?->name ?? 'Unassigned';
                })
                ->addColumn('status_badge', function ($kotOrder) {
                    $statusClasses = [
                        'pending' => 'warning',
                        'preparing' => 'info',
                        'ready' => 'success',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                    ];
                    $statusClass = $statusClasses[$kotOrder->status] ?? 'secondary';
                    return "<span class='badge badge-{$statusClass}'>" . ucfirst($kotOrder->status) . "</span>";
                })
                ->addColumn('priority_badge', function ($kotOrder) {
                    $priorityClasses = [
                        'low' => 'secondary',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'urgent' => 'danger',
                    ];
                    $priorityClass = $priorityClasses[$kotOrder->priority] ?? 'secondary';
                    return "<span class='badge badge-{$priorityClass}'>" . ucfirst($kotOrder->priority) . "</span>";
                })
                ->addColumn('elapsed_time', function ($kotOrder) {
                    if ($kotOrder->status === 'completed') {
                        return $kotOrder->actual_prep_time_minutes ? 
                            $kotOrder->actual_prep_time_minutes . ' min' : 'N/A';
                    }
                    return $kotOrder->getElapsedTimeMinutes() . ' min';
                })
                ->addColumn('remaining_time', function ($kotOrder) {
                    if ($kotOrder->status === 'completed') {
                        return 'Completed';
                    }
                    
                    $remaining = $kotOrder->getRemainingTimeMinutes();
                    if ($remaining === null) {
                        return 'N/A';
                    }
                    
                    $class = $remaining <= 0 ? 'text-danger' : ($remaining <= 5 ? 'text-warning' : 'text-success');
                    return "<span class='{$class}'>{$remaining} min</span>";
                })
                ->addColumn('actions', function ($kotOrder) {
                    return view('kitchen::partials.kot-actions', compact('kotOrder'))->render();
                })
                ->editColumn('created_at', function ($kotOrder) {
                    return $kotOrder->created_at->format('M d, Y H:i');
                })
                ->rawColumns(['status_badge', 'priority_badge', 'remaining_time', 'actions'])
                ->make(true);
        }

        $kitchens = Kitchen::active()->get();
        $statuses = ['pending', 'preparing', 'ready', 'completed', 'cancelled'];
        $priorities = ['low', 'normal', 'high', 'urgent'];

        return view('kitchen::kot-orders.index', compact('kitchens', 'statuses', 'priorities'));
    }

    /**
     * Display the specified KOT order.
     */
    public function show(KotOrder $kotOrder): View
    {
        $kotOrder->load([
            'kitchen',
            'order.customer',
            'assignedTo',
            'creator',
            'kotOrderItems.menuItem',
            'kotOrderItems.orderItem'
        ]);

        return view('kitchen::kot-orders.show', compact('kotOrder'));
    }

    /**
     * Update KOT status.
     */
    public function updateStatus(UpdateKotStatusRequest $request, KotOrder $kotOrder): JsonResponse
    {
        try {
            $status = $request->status;
            $userId = $request->assigned_to;

            switch ($status) {
                case 'preparing':
                    $kotOrder = $this->kotService->startKot($kotOrder, $userId);
                    break;
                case 'ready':
                    $kotOrder = $this->kotService->markKotReady($kotOrder);
                    break;
                case 'completed':
                    $kotOrder = $this->kotService->completeKot($kotOrder);
                    break;
                case 'cancelled':
                    $kotOrder = $this->kotService->cancelKot($kotOrder, $request->reason);
                    break;
                default:
                    throw new \Exception('Invalid status transition');
            }

            return response()->json([
                'success' => true,
                'message' => 'KOT status updated successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update KOT priority.
     */
    public function updatePriority(UpdateKotPriorityRequest $request, KotOrder $kotOrder): JsonResponse
    {
        try {
            $kotOrder = $this->kotService->updateKotPriority($kotOrder, $request->priority);

            return response()->json([
                'success' => true,
                'message' => 'KOT priority updated successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT priority: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign KOT to user.
     */
    public function assign(Request $request, KotOrder $kotOrder): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id'
        ]);

        try {
            $kotOrder = $this->kotService->assignKot($kotOrder, $request->user_id);

            return response()->json([
                'success' => true,
                'message' => 'KOT assigned successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign KOT: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Kitchen display view for active KOTs.
     */
    public function kitchenDisplay(Request $request, Kitchen $kitchen = null): View
    {
        $kitchenId = $kitchen?->id ?? $request->kitchen_id;
        $activeKots = $this->kotService->getActiveKotsForDisplay($kitchenId);
        $kitchens = Kitchen::active()->get();

        return view('kitchen::kot-orders.kitchen-display', compact('activeKots', 'kitchens', 'kitchen'));
    }

    /**
     * Get KOT statistics for dashboard.
     */
    public function statistics(Request $request): JsonResponse
    {
        $kitchenId = $request->kitchen_id;
        $statistics = $this->kotService->getKotStatistics($kitchenId);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Print KOT.
     */
    public function print(KotOrder $kotOrder): View
    {
        $kotOrder->load([
            'kitchen',
            'order.customer',
            'kotOrderItems.menuItem'
        ]);

        return view('kitchen::kot-orders.print', compact('kotOrder'));
    }

    /**
     * Get active KOTs for real-time updates.
     */
    public function activeKots(Request $request): JsonResponse
    {
        $kitchenId = $request->kitchen_id;
        $activeKots = $this->kotService->getActiveKotsForDisplay($kitchenId);

        return response()->json([
            'success' => true,
            'data' => $activeKots->map(function ($kot) {
                return [
                    'id' => $kot->id,
                    'kot_number' => $kot->kot_number,
                    'status' => $kot->status,
                    'priority' => $kot->priority,
                    'order_number' => $kot->order?->order_number,
                    'elapsed_time' => $kot->getElapsedTimeMinutes(),
                    'remaining_time' => $kot->getRemainingTimeMinutes(),
                    'is_overdue' => $kot->isOverdue(),
                    'items_count' => $kot->kotOrderItems->count(),
                    'created_at' => $kot->created_at->format('H:i'),
                ];
            })
        ]);
    }
}
