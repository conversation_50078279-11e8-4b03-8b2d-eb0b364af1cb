@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة موظفي التوصيل</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ قائمة الموظفين</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('delivery.personnel.create') }}" class="btn btn-primary">
                <i class="fa fa-plus"></i> إضافة موظف توصيل جديد
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>نجح!</strong> {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>خطأ!</strong> {{ session('error') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة موظفي التوصيل</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع موظفي التوصيل والمركبات</p>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="branchFilter">الفرع:</label>
                        <select id="branchFilter" class="form-control select2">
                            <option value="">جميع الفروع</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter">الحالة:</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="on_delivery">في التوصيل</option>
                            <option value="break">استراحة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="vehicleFilter">نوع المركبة:</label>
                        <select id="vehicleFilter" class="form-control">
                            <option value="">جميع المركبات</option>
                            <option value="motorcycle">دراجة نارية</option>
                            <option value="car">سيارة</option>
                            <option value="bicycle">دراجة هوائية</option>
                            <option value="scooter">سكوتر</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" id="filterBtn" class="btn btn-primary">
                                <i class="fa fa-filter"></i> تطبيق الفلتر
                            </button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="personnel-table" class="table key-buttons text-md-nowrap" data-page-length='50'>
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">الاسم</th>
                                <th class="border-bottom-0">معلومات الاتصال</th>
                                <th class="border-bottom-0">الفرع</th>
                                <th class="border-bottom-0">معلومات المركبة</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">حالة التحقق</th>
                                <th class="border-bottom-0">التقييم</th>
                                <th class="border-bottom-0">عدد التوصيلات</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

<!-- Action Modals -->
<!-- Verify Modal -->
<div class="modal fade" id="verifyModal" tabindex="-1" role="dialog" aria-labelledby="verifyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verifyModalLabel">تأكيد التحقق</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد التحقق من هذا الموظف؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="verifyForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-warning">تحقق</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Suspend Modal -->
<div class="modal fade" id="suspendModal" tabindex="-1" role="dialog" aria-labelledby="suspendModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendModalLabel">تأكيد الإيقاف</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد إيقاف هذا الموظف؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="suspendForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-secondary">إيقاف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Activate Modal -->
<div class="modal fade" id="activateModal" tabindex="-1" role="dialog" aria-labelledby="activateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateModalLabel">تأكيد التفعيل</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد تفعيل هذا الموظف؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="activateForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-success">تفعيل</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد حذف هذا الموظف؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jszip.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/pdfmake.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/vfs_fonts.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.html5.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.print.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Initialize DataTable
    var table = $('#personnel-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.personnel.data") }}',
            data: function(d) {
                d.branch_id = $('#branchFilter').val();
                d.status = $('#statusFilter').val();
                d.vehicle_type = $('#vehicleFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'user.name' },
            { data: 'contact', name: 'contact', orderable: false, searchable: false },
            { data: 'branch_name', name: 'branch.name' },
            { data: 'vehicle_info', name: 'vehicle_info', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'verification_status', name: 'is_verified' },
            { data: 'rating', name: 'rating', orderable: false, searchable: false },
            { data: 'deliveries_count', name: 'total_deliveries' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Filter functionality
    $('#filterBtn').click(function() {
        table.draw();
    });

    $('#resetBtn').click(function() {
        $('#branchFilter').val('').trigger('change');
        $('#statusFilter').val('');
        $('#vehicleFilter').val('');
        table.draw();
    });

    // Action functionality
    $(document).on('click', '.verify-personnel', function() {
        var personnelId = $(this).data('id');
        var verifyUrl = '{{ route("delivery.personnel.verify", ":id") }}';
        verifyUrl = verifyUrl.replace(':id', personnelId);
        
        $('#verifyForm').attr('action', verifyUrl);
        $('#verifyModal').modal('show');
    });

    $(document).on('click', '.suspend-personnel', function() {
        var personnelId = $(this).data('id');
        var suspendUrl = '{{ route("delivery.personnel.suspend", ":id") }}';
        suspendUrl = suspendUrl.replace(':id', personnelId);
        
        $('#suspendForm').attr('action', suspendUrl);
        $('#suspendModal').modal('show');
    });

    $(document).on('click', '.activate-personnel', function() {
        var personnelId = $(this).data('id');
        var activateUrl = '{{ route("delivery.personnel.activate", ":id") }}';
        activateUrl = activateUrl.replace(':id', personnelId);
        
        $('#activateForm').attr('action', activateUrl);
        $('#activateModal').modal('show');
    });

    $(document).on('click', '.delete-personnel', function() {
        var personnelId = $(this).data('id');
        var deleteUrl = '{{ route("delivery.personnel.destroy", ":id") }}';
        deleteUrl = deleteUrl.replace(':id', personnelId);
        
        $('#deleteForm').attr('action', deleteUrl);
        $('#deleteModal').modal('show');
    });
});
</script>
@endsection
