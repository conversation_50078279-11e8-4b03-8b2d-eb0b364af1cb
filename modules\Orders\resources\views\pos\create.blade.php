@extends('layouts.master')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="manifest" href="{{ asset('pos-manifest.json') }}">
<link rel="stylesheet" href="{{ asset('assets/css/pos.css') }}">
@endsection

@section('page-header')
<div class="breadcrumb-header justify-content-between">
    <div class="left-content">
        <span class="main-content-title mg-b-0 mg-b-lg-1">POS System</span>
    </div>
    <div class="justify-content-center mt-2">
        <ol class="breadcrumb">
            <li class="breadcrumb-item tx-15"><a href="javascript:void(0);">Orders</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create Order</li>
        </ol>
    </div>
</div>
@endsection

@section('js')
<script src="{{ asset('assets/js/pos/pos-storage.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-sync.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-ui.js') }}"></script>
<script src="{{ asset('assets/js/pos/pos-core.js') }}"></script>

@if(config('app.debug'))
<!-- Test suite for development -->
<script src="{{ asset('assets/js/pos/pos-test.js') }}"></script>
@endif
<script>
// Initialize POS System when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize POS with server data
    const serverData = {
        menuItems: @json($menuItems),
        customers: @json($customers),
        tables: @json($tables),
        deliveryPersonnel: @json($deliveryPersonnel),
        csrfToken: '{{ csrf_token() }}',
        routes: {
            store: '{{ route("orders.store") }}',
            menuItems: '/api/menu/menu-items',
            customers: '/api/customers',
            tables: '/api/tables',
            deliveryPersonnel: '/api/delivery/personnel'
        }
    };

    // Initialize POS system
    POS.init(serverData);

    // Register service worker for offline functionality
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/pos-sw.js')
            .then(function(registration) {
                console.log('Service Worker registered successfully:', registration);
            })
            .catch(function(error) {
                console.log('Service Worker registration failed:', error);
            });
    }

    // Handle online/offline events
    window.addEventListener('online', function() {
        POS.handleOnline();
    });

    window.addEventListener('offline', function() {
        POS.handleOffline();
    });
});
</script>
@endsection

@section('content')
<div class="pos-container" id="posContainer">
    <!-- POS Header -->
    <div class="pos-header">
        <div class="pos-header-content">
            <div class="pos-title">
                <h1>POS System</h1>
                <span class="pos-subtitle">Create New Order</span>
            </div>

            <div class="pos-status">
                <div class="connection-status" id="connectionStatus">
                    <i class="mdi mdi-wifi" id="connectionIcon"></i>
                    <span id="connectionText">Online</span>
                </div>
                <div class="sync-status" id="syncStatus" style="display: none;">
                    <i class="mdi mdi-sync" id="syncIcon"></i>
                    <span id="syncText">Syncing...</span>
                </div>
            </div>

            <div class="pos-actions">
                <button type="button" class="btn btn-outline-secondary" onclick="POS.clearOrder()" title="Clear Order">
                    <i class="mdi mdi-refresh"></i>
                </button>
                <button type="button" class="btn btn-outline-info" onclick="POS.syncData()" title="Sync Data">
                    <i class="mdi mdi-cloud-sync"></i>
                </button>
                <a href="{{ route('pos.index') }}" class="btn btn-secondary">
                    <i class="mdi mdi-arrow-left"></i> Back
                </a>
            </div>
        </div>
    </div>

    <!-- Loading Screen -->
    <div class="pos-loading" id="posLoading">
        <div class="loading-spinner"></div>
        <p>Loading POS System...</p>
    </div>

    <!-- Offline Banner -->
    <div class="offline-banner" id="offlineBanner" style="display: none;">
        <i class="mdi mdi-wifi-off"></i>
        <span>You're offline. Orders will be saved locally and synced when connection is restored.</span>
        <button type="button" onclick="POS.retryConnection()">Retry</button>
    </div>

    <!-- Main POS Interface -->
    <div class="pos-main" id="posMain" style="display: none;">
        <div class="pos-layout">
            <!-- Menu Section -->
            <section class="pos-menu" aria-label="Menu Items">
                <header class="menu-header">
                    <h2>Menu Items</h2>
                    <div class="menu-controls">
                        <div class="search-container">
                            <input
                                type="search"
                                id="menuSearch"
                                class="search-input"
                                placeholder="Search menu items..."
                                aria-label="Search menu items"
                            >
                            <i class="mdi mdi-magnify search-icon"></i>
                        </div>
                        <button
                            type="button"
                            class="view-toggle"
                            onclick="POS.toggleView()"
                            aria-label="Toggle view mode"
                        >
                            <i class="mdi mdi-view-grid" id="viewIcon"></i>
                        </button>
                    </div>
                </header>

                <!-- Category Tabs -->
                <nav class="category-nav" aria-label="Menu categories">
                    <button
                        class="category-tab active"
                        data-category="all"
                        aria-pressed="true"
                    >
                        All Items
                    </button>
                    @if($menuCategories && $menuCategories->count() > 0)
                        @foreach($menuCategories as $categoryName => $items)
                            <button
                                class="category-tab"
                                data-category="{{ $categoryName }}"
                                aria-pressed="false"
                            >
                                {{ $categoryName }}
                            </button>
                        @endforeach
                    @endif
                </nav>

                <!-- Menu Items Grid -->
                <div class="menu-items" id="menuItemsContainer" role="grid">
                    @if($menuItems && $menuItems->count() > 0)
                        @foreach($menuItems as $item)
                            <article
                                class="menu-item"
                                data-category="{{ $item->category ? $item->category->name : 'Uncategorized' }}"
                                role="gridcell"
                            >
                                <div
                                    class="menu-item-card"
                                    data-item-id="{{ $item->id }}"
                                    data-item-name="{{ $item->name }}"
                                    data-item-price="{{ $item->base_price }}"
                                    tabindex="0"
                                    role="button"
                                    aria-label="Add {{ $item->name }} to order"
                                >
                                    @if($item->image)
                                        <div class="item-image">
                                            <img
                                                src="{{ $item->image }}"
                                                alt="{{ $item->name }}"
                                                loading="lazy"
                                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                            >
                                            <div class="item-image-placeholder" style="display:none;">
                                                <i class="mdi mdi-food"></i>
                                            </div>
                                        </div>
                                    @else
                                        <div class="item-image-placeholder">
                                            <i class="mdi mdi-food"></i>
                                        </div>
                                    @endif

                                    <div class="item-content">
                                        <h3 class="item-name">{{ $item->name }}</h3>
                                        @if($item->description)
                                            <p class="item-description">{{ Str::limit($item->description, 60) }}</p>
                                        @endif

                                        <div class="item-footer">
                                            <span class="item-price">${{ number_format($item->base_price, 2) }}</span>

                                            <div class="item-badges">
                                                @if($item->is_spicy)
                                                    <span class="badge badge-spicy" title="Spicy">🌶️</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    @else
                        <div class="no-items">
                            <i class="mdi mdi-food-off"></i>
                            <p>No menu items available</p>
                        </div>
                    @endif
                </div>
            </section>

            <!-- Order Summary Section -->
            <aside class="pos-order" aria-label="Order Summary">
                <header class="order-header">
                    <h2>
                        <i class="mdi mdi-cart" aria-hidden="true"></i>
                        Order Summary
                    </h2>
                    <div class="order-count" id="orderCount">0 items</div>
                </header>

                <!-- Order Type Selection -->
                <section class="order-type-section">
                    <h3>Order Type</h3>
                    <div class="order-type-buttons" role="radiogroup" aria-label="Order type">
                        <button
                            type="button"
                            class="order-type-btn active"
                            data-type="dine_in"
                            role="radio"
                            aria-checked="true"
                        >
                            <i class="mdi mdi-table-chair" aria-hidden="true"></i>
                            <span>Dine In</span>
                        </button>
                        <button
                            type="button"
                            class="order-type-btn"
                            data-type="takeaway"
                            role="radio"
                            aria-checked="false"
                        >
                            <i class="mdi mdi-bag-personal" aria-hidden="true"></i>
                            <span>Takeaway</span>
                        </button>
                        <button
                            type="button"
                            class="order-type-btn"
                            data-type="delivery"
                            role="radio"
                            aria-checked="false"
                        >
                           
                            <span>Delivery</span>
                        </button>
                    </div>
                </section>

                <!-- Order Details Form -->
                <form class="order-form" id="orderForm">
                    <!-- Customer Selection -->
                    <div class="form-group conditional-field" id="customerField">
                        <label for="customerId">Customer</label>
                        <select class="form-control" id="customerId" name="customer_id">
                            <option value="">Select Customer</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">
                                    {{ $customer->first_name }} {{ $customer->last_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Table Selection -->
                    <div class="form-group conditional-field show" id="tableField">
                        <label for="tableId">Table</label>
                        <select class="form-control" id="tableId" name="table_id">
                            <option value="">Select Table</option>
                            @foreach($tables as $table)
                                <option value="{{ $table->id }}">
                                    Table {{ $table->table_number }} ({{ $table->capacity }} seats)
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Number of People -->
                    <div class="form-group conditional-field show" id="paxField">
                        <label for="pax">Number of People</label>
                        <input
                            type="number"
                            class="form-control"
                            id="pax"
                            name="pax"
                            min="1"
                            max="20"
                            value="1"
                        >
                    </div>

                    <!-- Delivery Fields -->
                    <div class="form-group conditional-field" id="deliveryManField">
                        <label for="deliveryManId">Delivery Person</label>
                        <select class="form-control" id="deliveryManId" name="delivery_man_id">
                            <option value="">Select Delivery Person</option>
                            @foreach($deliveryPersonnel as $person)
                                <option value="{{ $person->id }}">{{ $person->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group conditional-field" id="deliveryAddressField">
                        <label for="deliveryAddress">Delivery Address</label>
                        <textarea
                            class="form-control"
                            id="deliveryAddress"
                            name="delivery_address"
                            rows="3"
                            placeholder="Enter delivery address"
                        ></textarea>
                    </div>
                </form>

                <!-- Order Items -->
                <section class="order-items-section">
                    <h3>Order Items</h3>
                    <div class="order-items" id="orderItemsContainer">
                        <div class="empty-order">
                            <i class="mdi mdi-cart-outline" aria-hidden="true"></i>
                            <p>No items added yet</p>
                            <small>Select items from the menu to add them to your order</small>
                        </div>
                    </div>
                </section>

                <!-- Discount Section -->
                <section class="discount-section">
                    <h3>Discount</h3>
                    <div class="discount-controls">
                        <select class="form-control" id="discountType" name="discount_type">
                            <option value="percentage">Percentage (%)</option>
                            <option value="fixed">Fixed Amount ($)</option>
                        </select>
                        <input
                            type="number"
                            class="form-control"
                            id="discountValue"
                            name="discount_value"
                            placeholder="0"
                            min="0"
                            step="0.01"
                        >
                    </div>
                </section>

                <!-- Order Total -->
                <section class="order-total">
                    <div class="total-line">
                        <span>Subtotal:</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    <div class="total-line">
                        <span>Discount:</span>
                        <span id="discountAmount">-$0.00</span>
                    </div>
                    <div class="total-line">
                        <span>Tax (10%):</span>
                        <span id="taxAmount">$0.00</span>
                    </div>
                    <div class="total-line total-final">
                        <span>Total:</span>
                        <span id="totalAmount">$0.00</span>
                    </div>
                </section>

                <!-- Notes -->
                <div class="form-group">
                    <label for="notes">Order Notes</label>
                    <textarea
                        class="form-control"
                        id="notes"
                        name="notes"
                        rows="2"
                        placeholder="Special instructions..."
                    ></textarea>
                </div>

                <!-- Action Buttons -->
                <div class="order-actions">
                    <button
                        type="button"
                        class="btn btn-primary btn-submit"
                        onclick="POS.submitOrder()"
                        disabled
                    >
                        <i class="mdi mdi-check" aria-hidden="true"></i>
                        <span>Submit Order</span>
                    </button>
                </div>
            </aside>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div class="alert-container" id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
</div>
@endsection