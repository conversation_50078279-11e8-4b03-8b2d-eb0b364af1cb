{"info": {"_postman_id": "public-menu-collection-id", "name": "Public Menu API - Restaurant Access", "description": "Public API endpoints for accessing restaurant menus by tenant name. These endpoints allow customers to browse menus without authentication.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Restaurant Info", "item": [{"name": "Get Restaurant Info", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/info", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "info"]}, "description": "Get restaurant information including business hours, contact details, and branch info"}, "response": []}]}, {"name": "Menus", "item": [{"name": "Get All Menus", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/menus?menu_type=dine_in&search=", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "menus"], "query": [{"key": "menu_type", "value": "dine_in", "description": "Filter by menu type (dine_in, takeaway, delivery)"}, {"key": "search", "value": "", "description": "Search in menu name or description"}]}, "description": "Get all active menus for a restaurant with optional filtering"}, "response": []}, {"name": "Get Menu by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/menus/{{menu_id}}", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "menus", "{{menu_id}}"]}, "description": "Get a specific menu with all categories and items"}, "response": []}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/categories?menu_id={{menu_id}}&search=", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "categories"], "query": [{"key": "menu_id", "value": "{{menu_id}}", "description": "Filter by specific menu ID"}, {"key": "search", "value": "", "description": "Search in category name or description"}]}, "description": "Get all active categories for a restaurant"}, "response": []}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "categories", "{{category_id}}"]}, "description": "Get a specific category with all menu items"}, "response": []}]}, {"name": "Menu Items", "item": [{"name": "Get All Menu Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/menu-items?category_id={{category_id}}&menu_id={{menu_id}}&search=&price_min=0&price_max=100&is_vegetarian=true&is_vegan=false&is_gluten_free=false&per_page=15", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "menu-items"], "query": [{"key": "category_id", "value": "{{category_id}}", "description": "Filter by category ID"}, {"key": "menu_id", "value": "{{menu_id}}", "description": "Filter by menu ID"}, {"key": "search", "value": "", "description": "Search in item name or description"}, {"key": "price_min", "value": "0", "description": "Minimum price filter"}, {"key": "price_max", "value": "100", "description": "Maximum price filter"}, {"key": "is_vegetarian", "value": "true", "description": "Filter vegetarian items"}, {"key": "is_vegan", "value": "false", "description": "Filter vegan items"}, {"key": "is_gluten_free", "value": "false", "description": "Filter gluten-free items"}, {"key": "per_page", "value": "15", "description": "Items per page for pagination"}]}, "description": "Get all active menu items with filtering and pagination"}, "response": []}, {"name": "Get Menu Item by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/menu-items/{{item_id}}", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "menu-items", "{{item_id}}"]}, "description": "Get a specific menu item with variants and addons"}, "response": []}]}, {"name": "Addons", "item": [{"name": "Get All Addons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/addons?menu_item_id={{item_id}}&search=", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "addons"], "query": [{"key": "menu_item_id", "value": "{{item_id}}", "description": "Filter by menu item ID"}, {"key": "search", "value": "", "description": "Search in addon name or description"}]}, "description": "Get all active addons for a restaurant"}, "response": []}]}, {"name": "Variants", "item": [{"name": "Get All Variants", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{restaurant_name}}/variants?menu_item_id={{item_id}}&search=", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{restaurant_name}}", "variants"], "query": [{"key": "menu_item_id", "value": "{{item_id}}", "description": "Filter by menu item ID"}, {"key": "search", "value": "", "description": "Search in variant name or description"}]}, "description": "Get all active variants for a restaurant"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set restaurant name if not provided", "if (!pm.collectionVariables.get('restaurant_name')) {", "    pm.collectionVariables.set('restaurant_name', 'demo-restaurant');", "}", "", "// Auto-set base URL if not provided", "if (!pm.collectionVariables.get('base_url')) {", "    pm.collectionVariables.set('base_url', 'http://localhost:8000/api');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract IDs from responses for chaining requests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    ", "    // Extract menu ID from menu responses", "    if (responseJson.menus && responseJson.menus.length > 0) {", "        pm.collectionVariables.set('menu_id', responseJson.menus[0].id);", "    }", "    if (responseJson.menu && responseJson.menu.id) {", "        pm.collectionVariables.set('menu_id', responseJson.menu.id);", "    }", "    ", "    // Extract category ID from category responses", "    if (responseJson.categories && responseJson.categories.length > 0) {", "        pm.collectionVariables.set('category_id', responseJson.categories[0].id);", "    }", "    if (responseJson.category && responseJson.category.id) {", "        pm.collectionVariables.set('category_id', responseJson.category.id);", "    }", "    ", "    // Extract item ID from menu item responses", "    if (responseJson.menu_items && responseJson.menu_items.length > 0) {", "        pm.collectionVariables.set('item_id', responseJson.menu_items[0].id);", "    }", "    if (responseJson.menu_item && responseJson.menu_item.id) {", "        pm.collectionVariables.set('item_id', responseJson.menu_item.id);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "restaurant_name", "value": "demo-restaurant", "type": "string", "description": "Restaurant name or code to access menus"}, {"key": "menu_id", "value": "1", "type": "string", "description": "Menu ID for testing specific menu endpoints"}, {"key": "category_id", "value": "1", "type": "string", "description": "Category ID for testing specific category endpoints"}, {"key": "item_id", "value": "1", "type": "string", "description": "Menu item ID for testing specific item endpoints"}]}