<?php

namespace Modules\Menu\Services;

use App\Models\Offer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Offer Service
 * 
 * Handles business logic for offer management including creation,
 * updates, discount calculations, usage tracking, and analytics.
 * 
 * @package Modules\Menu\Services
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class OfferService
{
    /**
     * Get all offers with optional filtering
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllOffers(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Offer::query();

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->forBranch($filters['branch_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_public'])) {
            $query->public();
        }

        if (isset($filters['is_featured'])) {
            $query->featured();
        }

        if (isset($filters['offer_type'])) {
            $query->byType($filters['offer_type']);
        }

        if (isset($filters['status'])) {
            switch ($filters['status']) {
                case 'current':
                    $query->current();
                    break;
                case 'scheduled':
                    $query->where('start_date', '>', now());
                    break;
                case 'expired':
                    $query->where('end_date', '<', now());
                    break;
            }
        }

        if (isset($filters['promo_code'])) {
            $query->byPromoCode($filters['promo_code']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('code', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('promo_code', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->with(['tenant', 'branch'])
                    ->byPriority()
                    ->paginate($perPage);
    }

    /**
     * Get offer by ID
     *
     * @param int $id
     * @return Offer|null
     */
    public function getOfferById(int $id): ?Offer
    {
        return Offer::with(['tenant', 'branch'])->find($id);
    }

    /**
     * Get offer by promo code
     *
     * @param string $promoCode
     * @param int|null $branchId
     * @return Offer|null
     */
    public function getOfferByPromoCode(string $promoCode, ?int $branchId = null): ?Offer
    {
        $query = Offer::byPromoCode($promoCode)->active();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->first();
    }

    /**
     * Create a new offer
     *
     * @param array $data
     * @return Offer
     * @throws Exception
     */
    public function createOffer(array $data): Offer
    {
        try {
            DB::beginTransaction();

            // Generate unique code if not provided
            if (!isset($data['code'])) {
                $data['code'] = $this->generateOfferCode($data['name']);
            }

            // Set default values
            $data['current_usage_count'] = 0;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['priority'] = $data['priority'] ?? 'medium';

            $offer = Offer::create($data);

            DB::commit();

            Log::info('Offer created successfully', ['offer_id' => $offer->id]);

            return $offer;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create offer', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * Update an existing offer
     *
     * @param int $id
     * @param array $data
     * @return Offer
     * @throws Exception
     */
    public function updateOffer(int $id, array $data): Offer
    {
        try {
            DB::beginTransaction();

            $offer = Offer::findOrFail($id);
            $offer->update($data);

            DB::commit();

            Log::info('Offer updated successfully', ['offer_id' => $offer->id]);

            return $offer->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to update offer', ['error' => $e->getMessage(), 'offer_id' => $id]);
            throw $e;
        }
    }

    /**
     * Delete an offer
     *
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function deleteOffer(int $id): bool
    {
        try {
            DB::beginTransaction();

            $offer = Offer::findOrFail($id);
            
            // Check if offer has been used
            if ($offer->current_usage_count > 0) {
                throw new Exception('Cannot delete offer that has been used');
            }

            $offer->delete();

            DB::commit();

            Log::info('Offer deleted successfully', ['offer_id' => $id]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete offer', ['error' => $e->getMessage(), 'offer_id' => $id]);
            throw $e;
        }
    }

    /**
     * Get featured offers
     *
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getFeaturedOffers(?int $branchId = null, int $limit = 5): Collection
    {
        $query = Offer::featured()->active()->current()->public();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->byPriority()
                    ->limit($limit)
                    ->get();
    }

    /**
     * Get current offers
     *
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getCurrentOffers(?int $branchId = null, int $limit = 10): Collection
    {
        $query = Offer::current()->active()->public();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->byPriority()
                    ->limit($limit)
                    ->get();
    }

    /**
     * Apply offer to order
     *
     * @param int $offerId
     * @param array $orderData
     * @param int|null $customerId
     * @return array
     * @throws Exception
     */
    public function applyOfferToOrder(int $offerId, array $orderData, ?int $customerId = null): array
    {
        try {
            $offer = Offer::findOrFail($offerId);

            // Validate offer
            if (!$offer->isValid()) {
                throw new Exception('Offer is not valid or has expired');
            }

            // Check customer eligibility
            if (!$offer->isCustomerEligible($customerId)) {
                throw new Exception('Customer is not eligible for this offer');
            }

            // Check usage limits
            if (!$offer->canBeUsedByCustomer($customerId)) {
                throw new Exception('Customer has reached usage limit for this offer');
            }

            $orderTotal = $orderData['total'] ?? 0;
            $orderItems = $orderData['items'] ?? [];
            $orderCategories = $orderData['categories'] ?? [];

            // Check item/category applicability
            if (!empty($orderItems) && !$offer->isApplicableToItems($orderItems)) {
                throw new Exception('Offer is not applicable to the selected items');
            }

            if (!empty($orderCategories) && !$offer->isApplicableToCategories($orderCategories)) {
                throw new Exception('Offer is not applicable to the selected categories');
            }

            // Calculate discount
            $discountAmount = $offer->calculateDiscount($orderTotal);

            if ($discountAmount <= 0) {
                throw new Exception('No discount applicable for this order');
            }

            return [
                'offer_id' => $offer->id,
                'offer_name' => $offer->name,
                'offer_type' => $offer->offer_type,
                'discount_type' => $offer->discount_type,
                'discount_value' => $offer->discount_value,
                'discount_amount' => $discountAmount,
                'original_total' => $orderTotal,
                'final_total' => max(0, $orderTotal - $discountAmount),
                'savings' => $discountAmount,
                'terms_conditions' => $offer->terms_conditions,
            ];
        } catch (Exception $e) {
            Log::error('Failed to apply offer to order', [
                'error' => $e->getMessage(),
                'offer_id' => $offerId,
                'order_data' => $orderData
            ]);
            throw $e;
        }
    }

    /**
     * Validate promo code
     *
     * @param string $promoCode
     * @param array $orderData
     * @param int|null $customerId
     * @param int|null $branchId
     * @return array
     * @throws Exception
     */
    public function validatePromoCode(string $promoCode, array $orderData, ?int $customerId = null, ?int $branchId = null): array
    {
        $offer = $this->getOfferByPromoCode($promoCode, $branchId);

        if (!$offer) {
            throw new Exception('Invalid promo code');
        }

        return $this->applyOfferToOrder($offer->id, $orderData, $customerId);
    }

    /**
     * Record offer usage
     *
     * @param int $offerId
     * @param array $usageData
     * @return bool
     * @throws Exception
     */
    public function recordOfferUsage(int $offerId, array $usageData): bool
    {
        try {
            DB::beginTransaction();

            $offer = Offer::findOrFail($offerId);

            $customerId = $usageData['customer_id'] ?? null;
            $orderTotal = $usageData['order_total'] ?? 0;
            $discountAmount = $usageData['discount_amount'] ?? 0;

            $offer->recordUsage($customerId, $orderTotal, $discountAmount);

            DB::commit();

            Log::info('Offer usage recorded', [
                'offer_id' => $offerId,
                'usage_data' => $usageData
            ]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to record offer usage', [
                'error' => $e->getMessage(),
                'offer_id' => $offerId,
                'usage_data' => $usageData
            ]);
            throw $e;
        }
    }

    /**
     * Get offer analytics
     *
     * @param int $offerId
     * @return array
     */
    public function getOfferAnalytics(int $offerId): array
    {
        $offer = Offer::findOrFail($offerId);

        return [
            'offer_id' => $offer->id,
            'name' => $offer->name,
            'status' => $offer->getStatus(),
            'usage_stats' => $offer->getUsageStats(),
            'performance' => [
                'total_savings' => $this->calculateTotalSavings($offer),
                'average_order_value' => $this->calculateAverageOrderValue($offer),
                'conversion_rate' => $this->calculateConversionRate($offer),
            ],
            'analytics_data' => $offer->analytics_data ?? [],
        ];
    }

    /**
     * Get offers performance report
     *
     * @param array $filters
     * @return array
     */
    public function getOffersPerformanceReport(array $filters = []): array
    {
        $query = Offer::query();

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->forBranch($filters['branch_id']);
        }

        if (isset($filters['offer_type'])) {
            $query->byType($filters['offer_type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('start_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('end_date', '<=', $filters['date_to']);
        }

        $offers = $query->get();

        $totalUsage = $offers->sum('current_usage_count');
        $totalSavings = $offers->sum(function ($offer) {
            return $this->calculateTotalSavings($offer);
        });

        return [
            'summary' => [
                'total_offers' => $offers->count(),
                'active_offers' => $offers->where('is_active', true)->count(),
                'total_usage' => $totalUsage,
                'total_savings' => $totalSavings,
                'average_usage_per_offer' => $offers->count() > 0 ? round($totalUsage / $offers->count(), 2) : 0,
            ],
            'top_performers' => $offers->sortByDesc('current_usage_count')->take(5)->values(),
            'by_type' => $offers->groupBy('offer_type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_usage' => $group->sum('current_usage_count'),
                    'average_usage' => round($group->avg('current_usage_count'), 2),
                ];
            }),
        ];
    }

    /**
     * Generate unique offer code
     *
     * @param string $name
     * @return string
     */
    private function generateOfferCode(string $name): string
    {
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));
        $counter = 1;
        $code = $baseCode;

        while (Offer::where('code', $code)->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Calculate total savings for an offer
     *
     * @param Offer $offer
     * @return float
     */
    private function calculateTotalSavings(Offer $offer): float
    {
        $analytics = $offer->analytics_data ?? [];
        $totalSavings = 0;

        if (isset($analytics['daily_usage'])) {
            foreach ($analytics['daily_usage'] as $day => $data) {
                $totalSavings += $data['total_discount'] ?? 0;
            }
        }

        return $totalSavings;
    }

    /**
     * Calculate average order value for an offer
     *
     * @param Offer $offer
     * @return float
     */
    private function calculateAverageOrderValue(Offer $offer): float
    {
        $analytics = $offer->analytics_data ?? [];
        $totalOrderValue = 0;
        $totalOrders = 0;

        if (isset($analytics['daily_usage'])) {
            foreach ($analytics['daily_usage'] as $day => $data) {
                $totalOrderValue += $data['total_order_value'] ?? 0;
                $totalOrders += $data['count'] ?? 0;
            }
        }

        return $totalOrders > 0 ? round($totalOrderValue / $totalOrders, 2) : 0;
    }

    /**
     * Calculate conversion rate for an offer
     *
     * @param Offer $offer
     * @return float
     */
    private function calculateConversionRate(Offer $offer): float
    {
        // This would need more complex logic based on views vs usage
        // For now, return a simplified calculation
        return $offer->total_usage_limit > 0 ? 
            round(($offer->current_usage_count / $offer->total_usage_limit) * 100, 2) : 0;
    }

    /**
     * Toggle offer status
     *
     * @param int $id
     * @return Offer
     * @throws Exception
     */
    public function toggleOfferStatus(int $id): Offer
    {
        try {
            $offer = Offer::findOrFail($id);
            $offer->update(['is_active' => !$offer->is_active]);

            Log::info('Offer status toggled', [
                'offer_id' => $id,
                'new_status' => $offer->is_active ? 'active' : 'inactive'
            ]);

            return $offer;
        } catch (Exception $e) {
            Log::error('Failed to toggle offer status', ['error' => $e->getMessage(), 'offer_id' => $id]);
            throw $e;
        }
    }

    /**
     * Duplicate an offer
     *
     * @param int $id
     * @param array $overrides
     * @return Offer
     * @throws Exception
     */
    public function duplicateOffer(int $id, array $overrides = []): Offer
    {
        try {
            DB::beginTransaction();

            $originalOffer = Offer::findOrFail($id);
            $offerData = $originalOffer->toArray();

            // Remove fields that shouldn't be duplicated
            unset($offerData['id'], $offerData['created_at'], $offerData['updated_at']);
            
            // Reset usage count
            $offerData['current_usage_count'] = 0;
            
            // Generate new codes
            $offerData['code'] = $this->generateOfferCode($offerData['name'] . ' Copy');
            if ($offerData['promo_code']) {
                $offerData['promo_code'] = $offerData['promo_code'] . '_COPY';
            }
            
            // Update name
            $offerData['name'] = $offerData['name'] . ' (Copy)';
            
            // Apply overrides
            $offerData = array_merge($offerData, $overrides);

            $newOffer = Offer::create($offerData);

            DB::commit();

            Log::info('Offer duplicated successfully', [
                'original_id' => $id,
                'new_id' => $newOffer->id
            ]);

            return $newOffer;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to duplicate offer', ['error' => $e->getMessage(), 'offer_id' => $id]);
            throw $e;
        }
    }
}