@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ المستخدمين</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" data-toggle="modal" data-target="#addUserModal">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة المستخدمين</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة المستخدمين وأدوارهم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="usersTable" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">الاسم</th>
                                <th class="border-bottom-0">البريد الإلكتروني</th>
                                <th class="border-bottom-0">الأدوار</th>
                                <th class="border-bottom-0">الحالة</th>
                                {{-- <th class="border-bottom-0">تاريخ الإنشاء</th> --}}
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addUserForm" action="{{ route('users.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">الاسم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password_confirmation">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>الأدوار:</label>
                        <div class="roles-container">
                            @foreach($roles as $role)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="roles[]" value="{{ $role->name }}" id="role_{{ $role->id }}">
                                    <label class="form-check-label" for="role_{{ $role->id }}">
                                        {{ $role->name }}
                                        <small class="text-muted">({{ $role->guard_name }})</small>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">تعديل المستخدم</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editUserForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_name">الاسم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_password">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="edit_password" name="password">
                                <small class="text-muted">اتركها فارغة إذا كنت لا تريد تغييرها</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_password_confirmation">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" id="edit_password_confirmation" name="password_confirmation">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>الأدوار:</label>
                        <div class="edit-roles-container">
                            <!-- Roles will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign Roles Modal -->
<div class="modal fade" id="assignRolesModal" tabindex="-1" role="dialog" aria-labelledby="assignRolesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignRolesModalLabel">إدارة أدوار المستخدم</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="assignRolesForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="assign-roles-container">
                        <!-- Roles will be loaded dynamically -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الأدوار</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('js')
<!-- Internal Data tables -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!--Internal  Datatable js -->
<script src="{{URL::asset('assets/js/table-data.js')}}"></script>
<!-- Select2 -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#usersTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('users.data') }}",
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'email', name: 'email'},
            {data: 'roles', name: 'roles', orderable: false},
            {data: 'status_badge', name: 'is_active'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Handle Add User Form Submission
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#addUserModal').modal('hide');
                $('#addUserForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة المستخدم:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Edit User Form Submission
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                $('#editUserModal').modal('hide');
                swal("تم التحديث!", "تم تحديث بيانات المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث المستخدم:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Assign Roles Form Submission
    $('#assignRolesForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#assignRolesModal').modal('hide');
                swal("تم الحفظ!", "تم تحديث أدوار المستخدم بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تحديث الأدوار.", "error");
            }
        });
    });

    // Edit user function
    window.editUser = function(id) {
        var showUrl = "{{ route('users.show', ':id') }}".replace(':id', id);
        $.get(showUrl, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_email').val(data.email);
            $('#edit_password').val('');
            $('#edit_password_confirmation').val('');
            
            var updateUrl = "{{ route('users.update', ':id') }}".replace(':id', id);
            $('#editUserForm').attr('action', updateUrl);
            
            // Load roles for editing
            loadRolesForEdit(data.roles || []);
            
            $('#editUserModal').modal('show');
        }).fail(function() {
            swal("خطأ!", "حدث خطأ أثناء تحميل بيانات المستخدم.", "error");
        });
    };

    // Load roles for edit modal
    function loadRolesForEdit(userRoles) {
        var rolesHtml = '';
        @if(isset($roles))
            @foreach($roles as $role)
                rolesHtml += '<div class="form-check">';
                rolesHtml += '<input class="form-check-input" type="checkbox" name="roles[]" value="{{ $role->name }}" id="edit_role_{{ $role->id }}"';
                
                // Check if user has this role
                var hasRole = false;
                if (userRoles && Array.isArray(userRoles)) {
                    hasRole = userRoles.some(function(r) { 
                        return r.name === '{{ $role->name }}'; 
                    });
                }
                if (hasRole) rolesHtml += ' checked';
                
                rolesHtml += '>';
                rolesHtml += '<label class="form-check-label" for="edit_role_{{ $role->id }}">';
                rolesHtml += '{{ $role->name }} <small class="text-muted">({{ $role->guard_name }})</small>';
                rolesHtml += '</label>';
                rolesHtml += '</div>';
            @endforeach
        @endif
        $('.edit-roles-container').html(rolesHtml);
    }

    // Assign roles to user
    window.assignRoles = function(id) {
        var rolesUrl = "{{ route('users.roles', ':id') }}".replace(':id', id);
        $.get(rolesUrl, function(data) {
            var assignUrl = "{{ route('users.roles.assign', ':id') }}".replace(':id', id);
            $('#assignRolesForm').attr('action', assignUrl);
            
            var rolesHtml = '';
            @if(isset($roles))
                @foreach($roles as $role)
                    rolesHtml += '<div class="form-check">';
                    rolesHtml += '<input class="form-check-input" type="checkbox" name="roles[]" value="{{ $role->name }}" id="assign_role_{{ $role->id }}"';
                    
                    // Check if user has this role
                    var hasRole = false;
                    if (data.userRoles && Array.isArray(data.userRoles)) {
                        hasRole = data.userRoles.includes('{{ $role->name }}');
                    }
                    if (hasRole) rolesHtml += ' checked';
                    
                    rolesHtml += '>';
                    rolesHtml += '<label class="form-check-label" for="assign_role_{{ $role->id }}">';
                    rolesHtml += '{{ $role->name }} <small class="text-muted">({{ $role->guard_name }})</small>';
                    rolesHtml += '</label>';
                    rolesHtml += '</div>';
                @endforeach
            @endif
            $('.assign-roles-container').html(rolesHtml);
            
            $('#assignRolesModal').modal('show');
        }).fail(function() {
            swal("خطأ!", "حدث خطأ أثناء تحميل أدوار المستخدم.", "error");
        });
    };

    // Change user status (activate/deactivate)
    window.changeStatus = function(id, action) {
        var url = "{{ route('users.activate', ':id') }}".replace(':id', id);
        var message = "تم تفعيل المستخدم بنجاح.";
        var title = "تم التفعيل!";
        
        if (action === 'deactivate') {
            url = "{{ route('users.deactivate', ':id') }}".replace(':id', id);
            message = "تم إلغاء تفعيل المستخدم بنجاح.";
            title = "تم إلغاء التفعيل!";
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                swal(title, message, "success");
                table.draw();
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تغيير حالة المستخدم.", "error");
            }
        });
    };

    // Reset user password
    window.resetPassword = function(id) {
        swal({
            title: "إعادة تعيين كلمة المرور",
            text: "هل أنت متأكد من إعادة تعيين كلمة المرور لهذا المستخدم؟",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، أعد تعيين!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                var resetUrl = "{{ route('users.reset-password', ':id') }}".replace(':id', id);
                $.ajax({
                    url: resetUrl,
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم إعادة التعيين!", "تم إعادة تعيين كلمة المرور بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء إعادة تعيين كلمة المرور.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم إعادة تعيين كلمة المرور.", "error");
            }
        });
    };

    // Delete user
    window.deleteUser = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                var deleteUrl = "{{ route('users.destroy', ':id') }}".replace(':id', id);
                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف المستخدم بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف المستخدم.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف المستخدم.", "error");
            }
        });
    };

    // Clear form when modal is hidden
    $('#addUserModal').on('hidden.bs.modal', function () {
        $('#addUserForm')[0].reset();
    });

    $('#editUserModal').on('hidden.bs.modal', function () {
        $('#editUserForm')[0].reset();
        $('.edit-roles-container').html('');
    });

    $('#assignRolesModal').on('hidden.bs.modal', function () {
        $('#assignRolesForm')[0].reset();
        $('.assign-roles-container').html('');
    });
});
</script>
@endsection
