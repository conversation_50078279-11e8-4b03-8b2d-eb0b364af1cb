<?php

namespace Modules\Customer\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Branch;
use Modules\Customer\Services\CustomerService;
use Modules\Customer\Http\Requests\StoreCustomerRequest;
use Modules\Customer\Http\Requests\UpdateCustomerRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class CustomerWebController extends Controller
{
    protected $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Display customers management page
     */
    public function index()
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        
        return view('customers::index', compact('branches'));
    }

    /**
     * Show create customer form
     */
    public function create()
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        
        return view('customers::create', compact('branches'));
    }

    /**
     * Store new customer
     */
    public function store(StoreCustomerRequest $request)
    {
        $user = Auth::user();
        
        $data = $request->validated();
        $data['tenant_id'] = $user->tenant_id;
        
        if (!isset($data['branch_id'])) {
            $data['branch_id'] = $user->branch_id;
        }
        
        $customer = $this->customerService->createCustomer($data);

        return redirect()->route('customers.index')
            ->with('success', 'تم إنشاء العميل بنجاح');
    }

    /**
     * Show customer details
     */
    public function show(Request $request, Customer $customer)
    {
        $customer->load(['branch', 'orders', 'loyaltyTransactions']);
        
        // If it's an AJAX request, return JSON data
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => $customer
            ]);
        }
        
        return view('customers::show', compact('customer'));
    }

    /**
     * Show edit customer form
     */
    public function edit(Customer $customer)
    {
        $user = Auth::user();
        $branches = Branch::where('tenant_id', $user->tenant_id)->get();
        
        return view('customers::edit', compact('customer', 'branches'));
    }

    /**
     * Update customer
     */
    public function update(UpdateCustomerRequest $request, Customer $customer)
    {
        $data = $request->validated();
        $updatedCustomer = $this->customerService->updateCustomer($customer->id, $data);

        return redirect()->route('customers.index')
            ->with('success', 'تم تحديث بيانات العميل بنجاح');
    }

    /**
     * Delete customer
     */
    public function destroy(Customer $customer)
    {
        $this->customerService->deleteCustomer($customer->id);

        return redirect()->route('customers.index')
            ->with('success', 'تم حذف العميل بنجاح');
    }

    /**
     * Get customers data for DataTable
     */
    public function getCustomersData(Request $request)
    {
        $user = Auth::user();
        
        $query = Customer::with(['branch'])
            ->where('tenant_id', $user->tenant_id);
            
        // Filter by branch if specified
        if ($request->has('branch_id') && $request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        } elseif ($user->branch_id) {
            $query->where('branch_id', $user->branch_id);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('full_name', function ($customer) {
                return $customer->first_name . ' ' . $customer->last_name;
            })
            ->addColumn('contact', function ($customer) {
                $contact = '';
                if ($customer->phone) {
                    $contact .= '<div><i class="fa fa-phone"></i> ' . $customer->phone . '</div>';
                }
                if ($customer->email) {
                    $contact .= '<div><i class="fa fa-envelope"></i> ' . $customer->email . '</div>';
                }
                return $contact;
            })
            ->addColumn('branch_name', function ($customer) {
                return $customer->branch ? $customer->branch->name : '-';
            })
            ->addColumn('loyalty_points', function ($customer) {
                return '<span class="badge badge-success">' . number_format($customer->loyalty_points, 2) . '</span>';
            })
            ->addColumn('status', function ($customer) {
                if ($customer->is_active) {
                    return '<span class="badge badge-success">نشط</span>';
                } else {
                    return '<span class="badge badge-danger">غير نشط</span>';
                }
            })
            ->addColumn('last_visit', function ($customer) {
                return $customer->last_visit_at ? $customer->last_visit_at->format('Y-m-d H:i') : '-';
            })
            ->addColumn('action', function ($customer) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<button type="button" class="btn btn-sm btn-info view-btn" data-id="' . $customer->id . '" title="عرض"><i class="fa fa-eye"></i></button>';
                $actions .= '<button type="button" class="btn btn-sm btn-primary edit-btn" data-id="' . $customer->id . '" title="تعديل"><i class="fa fa-edit"></i></button>';
                $actions .= '<button type="button" class="btn btn-sm btn-success loyalty-btn" data-id="' . $customer->id . '" title="نقاط الولاء"><i class="fa fa-star"></i></button>';
                $actions .= '<button type="button" class="btn btn-sm btn-danger delete-btn" data-id="' . $customer->id . '" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['contact', 'loyalty_points', 'status', 'action'])
            ->make(true);
    }
}
