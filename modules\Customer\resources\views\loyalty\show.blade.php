@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">نقاط الولاء</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ {{ $customer->first_name }} {{ $customer->last_name }}</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('customers.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left"></i> العودة للعملاء
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>نجح!</strong> {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>خطأ!</strong> {{ session('error') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

<!-- Customer Info Row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header">
                <h4 class="card-title mg-b-0">معلومات العميل</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>الاسم الكامل:</h6>
                        <p>{{ $customer->first_name }} {{ $customer->last_name }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>رقم الهاتف:</h6>
                        <p>{{ $customer->phone ?: '-' }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>البريد الإلكتروني:</h6>
                        <p>{{ $customer->email ?: '-' }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>الرصيد الحالي:</h6>
                        <h4 class="text-success">{{ number_format($customer->loyalty_points, 2) }} نقطة</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loyalty Stats Row -->
<div class="row">
    <div class="col-lg-4">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-success rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-plus text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">إجمالي النقاط المكتسبة</h6>
                        <h4 class="text-success mb-0">{{ number_format($totalEarned, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-warning rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-minus text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">إجمالي النقاط المستخدمة</h6>
                        <h4 class="text-warning mb-0">{{ number_format($totalRedeemed, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card mg-b-20">
            <div class="card-body">
                <div class="d-flex">
                    <div class="wd-40 ht-40 bg-primary rounded d-flex align-items-center justify-content-center">
                        <i class="fa fa-star text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h6 class="mb-1">الرصيد المتاح</h6>
                        <h4 class="text-primary mb-0">{{ number_format($customer->loyalty_points, 2) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions Row -->
<div class="row">
    <div class="col-lg-6">
        <div class="card mg-b-20">
            <div class="card-header">
                <h5 class="card-title">إضافة نقاط ولاء</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('customers.loyalty.add', $customer) }}" method="POST">
                    @csrf
                    <div class="form-group">
                        <label for="points">عدد النقاط:</label>
                        <input type="number" step="0.01" min="0.01" class="form-control" id="points" name="points" required>
                    </div>
                    <div class="form-group">
                        <label for="description">الوصف:</label>
                        <input type="text" class="form-control" id="description" name="description" placeholder="سبب إضافة النقاط" required>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fa fa-plus"></i> إضافة النقاط
                    </button>
                </form>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card mg-b-20">
            <div class="card-header">
                <h5 class="card-title">استخدام نقاط الولاء</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('customers.loyalty.redeem', $customer) }}" method="POST">
                    @csrf
                    <div class="form-group">
                        <label for="redeem_points">عدد النقاط:</label>
                        <input type="number" step="0.01" min="0.01" max="{{ $customer->loyalty_points }}" class="form-control" id="redeem_points" name="points" required>
                        <small class="form-text text-muted">الحد الأقصى: {{ number_format($customer->loyalty_points, 2) }} نقطة</small>
                    </div>
                    <div class="form-group">
                        <label for="redeem_description">الوصف:</label>
                        <input type="text" class="form-control" id="redeem_description" name="description" placeholder="سبب استخدام النقاط" required>
                    </div>
                    <button type="submit" class="btn btn-warning" {{ $customer->loyalty_points <= 0 ? 'disabled' : '' }}>
                        <i class="fa fa-minus"></i> استخدام النقاط
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">سجل المعاملات</h4>
                    <a href="{{ route('customers.loyalty.history', $customer) }}" class="btn btn-sm btn-primary">
                        عرض السجل الكامل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="transactions-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">النقاط</th>
                                <th class="border-bottom-0">الوصف</th>
                                <th class="border-bottom-0">تمت بواسطة</th>
                                <th class="border-bottom-0">التاريخ</th>
                                <th class="border-bottom-0">تاريخ الانتهاء</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable for transactions
    $('#transactions-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("customers.loyalty.data", $customer) }}',
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'type_badge', name: 'type' },
            { data: 'points_formatted', name: 'points' },
            { data: 'description', name: 'description' },
            { data: 'processed_by_name', name: 'processedBy.name' },
            { data: 'date_formatted', name: 'created_at' },
            { data: 'expires_at_formatted', name: 'expires_at' }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        order: [[5, 'desc']], // Order by date descending
        pageLength: 10
    });
});
</script>
@endsection
