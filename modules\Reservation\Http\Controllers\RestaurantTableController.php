<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Table;
use Illuminate\Http\Request;
use Illuminate\View\View;

class RestaurantTableController extends Controller
{
    /**
     * Display the restaurant table page.
     */
    public function show(Request $request, string $table_id): View
    {
        // Validate the hash parameter
        $hash = $request->get('hash');
        
        if (!$hash) {
            abort(404, 'Invalid table access link');
        }
        
        // Find the table by the table_id (which is the QR code)
        $table = Table::with(['branch', 'area'])
            ->where('qr_code', $table_id)
            ->first();
        
        if (!$table) {
            abort(404, 'Table not found');
        }
        
        // Validate the hash matches the expected format
        $expectedHash = $this->generateTableHash($table);
        
        if ($hash !== $expectedHash) {
            abort(403, 'Invalid access token');
        }
        
        // Load the table's menu and other relevant data
        $menuItems = $this->getTableMenuItems($table);
        
        return view('reservation::restaurant.table', compact('table', 'menuItems'));
    }
    
    /**
     * Generate the hash for a table.
     */
    private function generateTableHash(Table $table): string
    {
        // Create a hash based on branch name and table number
        $branchName = $table->branch->name ?? 'default';
        $tableNumber = $table->table_number;
        
        // Convert to lowercase and replace spaces with hyphens
        $hash = strtolower(str_replace(' ', '-', $branchName . '-' . $tableNumber));
        
        return $hash;
    }
    
    /**
     * Get menu items for the table.
     */
    private function getTableMenuItems(Table $table): array
    {
        // This is a placeholder - you can implement actual menu loading logic
        // based on your menu system
        return [
            // Example menu structure
            'categories' => [],
            'items' => []
        ];
    }
}