<?php

namespace Modules\Tenant\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Modules\Tenant\Services\TenantService;

class TenantWebController extends Controller
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Display a listing of tenants.
     */
    public function index()
    {
        $countries = Country::all();
        
        return view('tenant::tenants.index', compact('countries'));
    }

    /**
     * Show the form for creating a new tenant.
     */
    public function create()
    {
        $countries = Country::all();
        
        return view('tenant::tenants.create', compact('countries'));
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|unique:tenants,code|max:50',
            'business_type' => 'required|string|max:100',
            'country_id' => 'required|exists:countries,id',
            'primary_contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|unique:tenants,contact_email',
            'contact_phone' => 'required|string|max:20',
            'business_address' => 'required|string',
            'tax_number' => 'nullable|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'website_url' => 'nullable|url',
            'timezone' => 'required|string|max:50',
            'currency_code' => 'required|string|max:3',
            'language_code' => 'required|string|max:5',
        ]);

        try {
            $tenant = $this->tenantService->createTenant($request->all());
            
            return redirect()->route('tenants.index')
                ->with('success', 'تم إنشاء المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified tenant.
     */
    public function show(Tenant $tenant)
    {
        $tenant->load(['branches', 'users']);
        $statistics = $this->getStatistics($tenant);
        
        return view('tenant::tenants.show', compact('tenant', 'statistics'));
    }

    /**
     * Show the form for editing the specified tenant.
     */
    public function edit(Tenant $tenant)
    {
        $countries = Country::all();
        
        return view('tenant::tenants.edit', compact('tenant', 'countries'));
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, Tenant $tenant)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:tenants,code,' . $tenant->id,
            'business_type' => 'required|string|max:100',
            'country_id' => 'required|exists:countries,id',
            'primary_contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|unique:tenants,contact_email,' . $tenant->id,
            'contact_phone' => 'required|string|max:20',
            'business_address' => 'required|string',
            'tax_number' => 'nullable|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'website_url' => 'nullable|url',
            'timezone' => 'required|string|max:50',
            'currency_code' => 'required|string|max:3',
            'language_code' => 'required|string|max:5',
        ]);

        try {
            $tenant->update($request->all());
            
            return redirect()->route('tenants.show', $tenant)
                ->with('success', 'تم تحديث بيانات المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(Tenant $tenant)
    {
        try {
            // Check if tenant has active branches or users
            if ($tenant->branches()->count() > 0 || $tenant->users()->count() > 0) {
                return back()->with('error', 'لا يمكن حذف المستأجر لوجود فروع أو مستخدمين مرتبطين به');
            }

            $tenant->delete();
            
            return redirect()->route('tenants.index')
                ->with('success', 'تم حذف المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Activate tenant.
     */
    public function activate(Tenant $tenant)
    {
        try {
            $tenant->update(['status' => 'active']);
            
            return back()->with('success', 'تم تفعيل المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تفعيل المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate tenant.
     */
    public function deactivate(Tenant $tenant)
    {
        try {
            $tenant->update(['status' => 'inactive']);
            
            return back()->with('success', 'تم إلغاء تفعيل المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إلغاء تفعيل المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Suspend tenant.
     */
    public function suspend(Tenant $tenant)
    {
        try {
            $tenant->update(['status' => 'suspended']);
            
            return back()->with('success', 'تم تعليق المستأجر بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تعليق المستأجر: ' . $e->getMessage());
        }
    }

    /**
     * Get tenants data for DataTable.
     */
    public function getTenantsData(Request $request)
    {
        $query = Tenant::with(['country'])
            ->select(['id', 'name', 'code', 'business_type', 'country_id', 'contact_email', 'contact_phone', 'status', 'created_at']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('business_type')) {
            $query->where('business_type', $request->business_type);
        }

        if ($request->filled('country_id')) {
            $query->where('country_id', $request->country_id);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('country_name', function ($tenant) {
                return $tenant->country ? $tenant->country->name : '-';
            })
            ->addColumn('contact_info', function ($tenant) {
                return '<div class="contact-info">' .
                       '<div><i class="fa fa-envelope"></i> ' . $tenant->contact_email . '</div>' .
                       '<div><i class="fa fa-phone"></i> ' . $tenant->contact_phone . '</div>' .
                       '</div>';
            })
            ->addColumn('status_badge', function ($tenant) {
                $badges = [
                    'active' => '<span class="badge badge-success">نشط</span>',
                    'inactive' => '<span class="badge badge-secondary">غير نشط</span>',
                    'suspended' => '<span class="badge badge-warning">معلق</span>',
                    'trial' => '<span class="badge badge-info">تجريبي</span>',
                ];
                return $badges[$tenant->status] ?? $tenant->status;
            })
            ->addColumn('branches_count', function ($tenant) {
                return $tenant->branches()->count();
            })
            ->addColumn('users_count', function ($tenant) {
                return $tenant->users()->count();
            })
            ->addColumn('action', function ($tenant) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('tenants.show', $tenant) . '" class="btn btn-sm btn-info" title="عرض"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('tenants.edit', $tenant) . '" class="btn btn-sm btn-primary" title="تعديل"><i class="fa fa-edit"></i></a>';
                
                if ($tenant->status === 'active') {
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="changeStatus(' . $tenant->id . ', \'deactivate\')" title="إلغاء تفعيل"><i class="fa fa-pause"></i></button>';
                } elseif ($tenant->status === 'inactive') {
                    $actions .= '<button type="button" class="btn btn-sm btn-success" onclick="changeStatus(' . $tenant->id . ', \'activate\')" title="تفعيل"><i class="fa fa-play"></i></button>';
                }
                
                if ($tenant->status !== 'suspended') {
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="changeStatus(' . $tenant->id . ', \'suspend\')" title="تعليق"><i class="fa fa-ban"></i></button>';
                }
                
                $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteTenant(' . $tenant->id . ')" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['contact_info', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Get tenant statistics.
     */
    public function getStatistics(Tenant $tenant)
    {
        return [
            'branches_count' => $tenant->branches()->count(),
            'active_branches' => $tenant->branches()->where('status', 'active')->count(),
            'users_count' => $tenant->users()->count(),
            'active_users' => $tenant->users()->where('is_active', true)->count(),
            'orders_count' => $tenant->branches()->withCount('orders')->get()->sum('orders_count'),
            'total_revenue' => $tenant->branches()->join('orders', 'branches.id', '=', 'orders.branch_id')
                ->where('orders.status', 'completed')
                ->sum('orders.total_amount'),
        ];
    }

    /**
     * Show tenant subscription page.
     */
    public function subscription(Tenant $tenant)
    {
        return view('tenant::tenants.subscription', compact('tenant'));
    }

    /**
     * Show tenant billing page.
     */
    public function billing(Tenant $tenant)
    {
        return view('tenant::tenants.billing', compact('tenant'));
    }

    /**
     * Get billing data for DataTable.
     */
    public function getBillingData(Request $request, Tenant $tenant)
    {
        // This would typically come from a billing_records table
        // For now, return empty data
        return DataTables::of(collect([]))->make(true);
    }
}
