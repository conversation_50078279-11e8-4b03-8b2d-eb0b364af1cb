@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة العملاء</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ قائمة العملاء</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('customers.create') }}" class="btn btn-primary">
                <i class="fa fa-plus"></i> إضافة عميل جديد
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>نجح!</strong> {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>خطأ!</strong> {{ session('error') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة العملاء</h4>
                    <button type="button" id="create-customer-btn" class="btn btn-primary">
                        <i class="fa fa-plus"></i> إضافة عميل جديد
                    </button>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع عملاء المطعم ونقاط الولاء</p>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="branchFilter">الفرع:</label>
                        <select id="branchFilter" class="form-control select2">
                            <option value="">جميع الفروع</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="statusFilter">الحالة:</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" id="filterBtn" class="btn btn-primary">
                                <i class="fa fa-filter"></i> تطبيق الفلتر
                            </button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="customers-table" class="table key-buttons text-md-nowrap" data-page-length='50'>
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">الاسم الكامل</th>
                                <th class="border-bottom-0">معلومات الاتصال</th>
                                <th class="border-bottom-0">الفرع</th>
                                <th class="border-bottom-0">نقاط الولاء</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">آخر زيارة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

<!-- View Customer Modal -->
<div class="modal fade" id="viewCustomerModal" tabindex="-1" role="dialog" aria-labelledby="viewCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCustomerModalLabel">تفاصيل العميل</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الاسم الكامل:</strong></label>
                            <p id="view_full_name"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>رقم الهاتف:</strong></label>
                            <p id="view_phone"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>البريد الإلكتروني:</strong></label>
                            <p id="view_email"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ الميلاد:</strong></label>
                            <p id="view_date_of_birth"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الجنس:</strong></label>
                            <p id="view_gender"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الفرع:</strong></label>
                            <p id="view_branch"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>نقاط الولاء:</strong></label>
                            <p id="view_loyalty_points"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="view_status"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>آخر زيارة:</strong></label>
                            <p id="view_last_visit"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ التسجيل:</strong></label>
                            <p id="view_created_at"></p>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label><strong>العنوان:</strong></label>
                    <p id="view_address"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="edit-from-view">تعديل</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أنك تريد حذف هذا العميل؟</p>
                <div class="alert alert-warning">
                    <strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء وسيتم حذف جميع البيانات المرتبطة بالعميل.
                </div>
                <div id="customer-delete-info"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loyalty Points Modal -->
<div class="modal fade" id="loyaltyModal" tabindex="-1" role="dialog" aria-labelledby="loyaltyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loyaltyModalLabel">إدارة نقاط الولاء</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>العميل: <span id="loyalty_customer_name"></span></h4>
                                <h2 class="text-success">نقاط الولاء الحالية: <span id="loyalty_current_points">0</span></h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">إضافة نقاط</h5>
                            </div>
                            <div class="card-body">
                                <form id="addPointsForm">
                                    <div class="form-group">
                                        <label for="add_points">عدد النقاط</label>
                                        <input type="number" class="form-control" id="add_points" name="points" min="1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="add_reason">السبب</label>
                                        <input type="text" class="form-control" id="add_reason" name="reason" placeholder="مثال: شراء بقيمة 100 ريال">
                                    </div>
                                    <button type="submit" class="btn btn-success btn-block">إضافة نقاط</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">استبدال نقاط</h5>
                            </div>
                            <div class="card-body">
                                <form id="redeemPointsForm">
                                    <div class="form-group">
                                        <label for="redeem_points">عدد النقاط</label>
                                        <input type="number" class="form-control" id="redeem_points" name="points" min="1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="redeem_reason">السبب</label>
                                        <input type="text" class="form-control" id="redeem_reason" name="reason" placeholder="مثال: خصم 10 ريال">
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-block">استبدال نقاط</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h5>آخر المعاملات</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>النقاط</th>
                                        <th>السبب</th>
                                    </tr>
                                </thead>
                                <tbody id="loyalty_transactions">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <a href="#" id="view_full_history" class="btn btn-info" target="_blank">عرض السجل الكامل</a>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Customer Modal -->
<div class="modal fade" id="customerModal" tabindex="-1" role="dialog" aria-labelledby="customerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="customerForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_of_birth">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gender">الجنس</label>
                                <select class="form-control" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="address">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch_id">الفرع</label>
                                <select class="form-control" id="branch_id" name="branch_id">
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_active">الحالة</label>
                                <select class="form-control" id="is_active" name="is_active">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jszip.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/pdfmake.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/vfs_fonts.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.html5.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.print.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Initialize DataTable
    var table = $('#customers-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("customers.data") }}',
            data: function(d) {
                d.branch_id = $('#branchFilter').val();
                d.status = $('#statusFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'full_name', name: 'full_name' },
            { data: 'contact', name: 'contact', orderable: false, searchable: false },
            { data: 'branch_name', name: 'branch.name' },
            { data: 'loyalty_points', name: 'loyalty_points' },
            { data: 'status', name: 'is_active' },
            { data: 'last_visit', name: 'last_visit_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Filter functionality
    $('#filterBtn').click(function() {
        table.draw();
    });

    $('#resetBtn').click(function() {
        $('#branchFilter').val('').trigger('change');
        $('#statusFilter').val('');
        table.draw();
    });

    // Handle view button click
    $(document).on('click', '.view-btn', function() {
        var customerId = $(this).data('id');
        
        // Fetch customer data
        $.ajax({
            url: "{{ route('customers.show', ':id') }}".replace(':id', customerId),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var customer = response.data;
                    
                    // Populate view modal
                    $('#view_full_name').text(customer.first_name + ' ' + customer.last_name);
                    $('#view_phone').text(customer.phone || 'غير محدد');
                    $('#view_email').text(customer.email || 'غير محدد');
                    $('#view_date_of_birth').text(customer.date_of_birth || 'غير محدد');
                    $('#view_gender').text(customer.gender === 'male' ? 'ذكر' : customer.gender === 'female' ? 'أنثى' : 'غير محدد');
                    $('#view_branch').text(customer.branch ? customer.branch.name : 'غير محدد');
                    $('#view_loyalty_points').text(customer.loyalty_points || '0');
                    $('#view_status').html(customer.is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                    $('#view_last_visit').text(customer.last_visit || 'لم يزر بعد');
                    $('#view_created_at').text(customer.created_at ? new Date(customer.created_at).toLocaleDateString('ar-SA') : 'غير محدد');
                    $('#view_address').text(customer.address || 'غير محدد');
                    
                    // Set edit button data
                    $('#edit-from-view').data('id', customerId);
                    
                    $('#viewCustomerModal').modal('show');
                }
            },
            error: function() {
                alert('حدث خطأ في تحميل بيانات العميل');
            }
        });
    });

    // Handle edit from view modal
    $(document).on('click', '#edit-from-view', function() {
        var customerId = $(this).data('id');
        $('#viewCustomerModal').modal('hide');
        $('.edit-btn[data-id="' + customerId + '"]').click();
    });

    // Handle delete button click
    $(document).on('click', '.delete-btn', function() {
        var customerId = $(this).data('id');
        var deleteUrl = "{{ route('customers.destroy', ':id') }}".replace(':id', customerId);
        
        // Fetch customer data for delete confirmation
        $.ajax({
            url: "{{ route('customers.show', ':id') }}".replace(':id', customerId),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var customer = response.data;
                    var customerInfo = '<div class="card"><div class="card-body">';
                    customerInfo += '<h6>العميل المراد حذفه:</h6>';
                    customerInfo += '<p><strong>الاسم:</strong> ' + customer.first_name + ' ' + customer.last_name + '</p>';
                    customerInfo += '<p><strong>الهاتف:</strong> ' + (customer.phone || 'غير محدد') + '</p>';
                    customerInfo += '<p><strong>البريد الإلكتروني:</strong> ' + (customer.email || 'غير محدد') + '</p>';
                    customerInfo += '<p><strong>نقاط الولاء:</strong> ' + (customer.loyalty_points || '0') + '</p>';
                    customerInfo += '</div></div>';
                    
                    $('#customer-delete-info').html(customerInfo);
                }
            }
        });
        
        $('#deleteForm').attr('action', deleteUrl);
        $('#deleteModal').modal('show');
    });

    // Handle loyalty points button click
    $(document).on('click', '.loyalty-btn', function() {
        var customerId = $(this).data('id');
        
        // Fetch customer data
        $.ajax({
            url: "{{ route('customers.show', ':id') }}".replace(':id', customerId),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var customer = response.data;
                    
                    // Populate loyalty modal
                    $('#loyalty_customer_name').text(customer.first_name + ' ' + customer.last_name);
                    $('#loyalty_current_points').text(customer.loyalty_points || '0');
                    
                    // Set form data attributes
                    $('#addPointsForm').data('customer-id', customerId);
                    $('#redeemPointsForm').data('customer-id', customerId);
                    
                    // Set full history link
                    $('#view_full_history').attr('href', "{{ route('customers.loyalty.history', ':id') }}".replace(':id', customerId));
                    
                    // Load recent transactions
                    loadLoyaltyTransactions(customerId);
                    
                    $('#loyaltyModal').modal('show');
                }
            },
            error: function() {
                alert('حدث خطأ في تحميل بيانات العميل');
            }
        });
    });

    // Load loyalty transactions
    function loadLoyaltyTransactions(customerId) {
        $.ajax({
            url: "{{ route('customers.loyalty.history', ':id') }}".replace(':id', customerId),
            type: 'GET',
            data: { limit: 5 }, // Get only last 5 transactions
            success: function(response) {
                var tbody = $('#loyalty_transactions');
                tbody.empty();
                
                if (response.data && response.data.length > 0) {
                    response.data.forEach(function(transaction) {
                        var typeText = transaction.transaction_type === 'earned' ? 'إضافة' : 'استبدال';
                        var typeClass = transaction.transaction_type === 'earned' ? 'text-success' : 'text-warning';
                        var pointsText = transaction.transaction_type === 'earned' ? '+' + transaction.points : '-' + transaction.points;
                        
                        var row = '<tr>';
                        row += '<td>' + new Date(transaction.created_at).toLocaleDateString('ar-SA') + '</td>';
                        row += '<td><span class="' + typeClass + '">' + typeText + '</span></td>';
                        row += '<td><span class="' + typeClass + '">' + pointsText + '</span></td>';
                        row += '<td>' + (transaction.reason || 'غير محدد') + '</td>';
                        row += '</tr>';
                        
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="4" class="text-center">لا توجد معاملات</td></tr>');
                }
            }
        });
    }

    // Handle add points form
    $('#addPointsForm').on('submit', function(e) {
        e.preventDefault();
        var customerId = $(this).data('customer-id');
        var points = $('#add_points').val();
        var reason = $('#add_reason').val();
        
        $.ajax({
            url: "{{ route('customers.loyalty.add', ':id') }}".replace(':id', customerId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                points: points,
                reason: reason
            },
            success: function(response) {
                if (response.success) {
                    // Update current points display
                    $('#loyalty_current_points').text(response.data.total_points);
                    
                    // Clear form
                    $('#add_points').val('');
                    $('#add_reason').val('');
                    
                    // Reload transactions
                    loadLoyaltyTransactions(customerId);
                    
                    // Refresh datatable
                    table.ajax.reload(null, false);
                    
                    alert('تم إضافة النقاط بنجاح');
                }
            },
            error: function() {
                alert('حدث خطأ في إضافة النقاط');
            }
        });
    });

    // Handle redeem points form
    $('#redeemPointsForm').on('submit', function(e) {
        e.preventDefault();
        var customerId = $(this).data('customer-id');
        var points = $('#redeem_points').val();
        var reason = $('#redeem_reason').val();
        
        $.ajax({
            url: "{{ route('customers.loyalty.redeem', ':id') }}".replace(':id', customerId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                points: points,
                reason: reason
            },
            success: function(response) {
                if (response.success) {
                    // Update current points display
                    $('#loyalty_current_points').text(response.data.total_points);
                    
                    // Clear form
                    $('#redeem_points').val('');
                    $('#redeem_reason').val('');
                    
                    // Reload transactions
                    loadLoyaltyTransactions(customerId);
                    
                    // Refresh datatable
                    table.ajax.reload(null, false);
                    
                    alert('تم استبدال النقاط بنجاح');
                }
            },
            error: function(xhr) {
                var errorMsg = 'حدث خطأ في استبدال النقاط';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                alert(errorMsg);
            }
        });
    });

    // Create customer functionality
    $('#create-customer-btn').click(function() {
        $('#customerModalLabel').text('إضافة عميل جديد');
        $('#customerForm')[0].reset();
        $('#customerForm').attr('action', '{{ route("customers.store") }}');
        $('#customerForm').find('input[name="_method"]').remove();
        $('#customerModal').modal('show');
    });

    // Edit customer functionality
    $(document).on('click', '.edit-btn', function() {
        var customerId = $(this).data('id');
        var editUrl = '{{ route("customers.update", ":id") }}';
        editUrl = editUrl.replace(':id', customerId);

        $('#customerModalLabel').text('تعديل العميل');
        $('#customerForm').attr('action', editUrl);

        // Add method field for PUT request
        if ($('#customerForm').find('input[name="_method"]').length === 0) {
            $('#customerForm').append('<input type="hidden" name="_method" value="PUT">');
        }

        // Load customer data
        $.get('{{ route("customers.show", ":id") }}'.replace(':id', customerId))
            .done(function(response) {
                var customer = response.success ? response.data : response;
                $('#first_name').val(customer.first_name);
                $('#last_name').val(customer.last_name);
                $('#email').val(customer.email);
                $('#phone').val(customer.phone);
                $('#date_of_birth').val(customer.date_of_birth);
                $('#gender').val(customer.gender);
                $('#address').val(customer.address);
                $('#branch_id').val(customer.branch_id);
                $('#is_active').val(customer.is_active ? '1' : '0');
                $('#customerModal').modal('show');
            })
            .fail(function() {
                alert('خطأ في تحميل بيانات العميل');
            });
    });

    // Handle form submission
    $('#customerForm').submit(function(e) {
        e.preventDefault();

        var formData = $(this).serialize();
        var url = $(this).attr('action');
        var method = $(this).find('input[name="_method"]').val() || 'POST';

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            success: function(response) {
                $('#customerModal').modal('hide');
                table.draw();

                // Show success message
                $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    '<strong>نجح!</strong> تم حفظ العميل بنجاح.' +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span></button></div>')
                    .prependTo('.card-body').delay(3000).fadeOut();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors || {};
                var errorMessage = 'حدث خطأ أثناء حفظ العميل';

                if (Object.keys(errors).length > 0) {
                    errorMessage = Object.values(errors).flat().join('<br>');
                }

                $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                    '<strong>خطأ!</strong> ' + errorMessage +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span></button></div>')
                    .prependTo('.card-body').delay(5000).fadeOut();
            }
        });
    });
});
</script>
@endsection
