@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الصلاحيات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" data-toggle="modal" data-target="#addPermissionModal">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-warning btn-icon ml-2" data-toggle="modal" data-target="#bulkCreateModal">
                <i class="mdi mdi-plus-box-multiple"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الصلاحيات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة صلاحيات النظام وتجميعها حسب الوحدات</p>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="groupFilter">تصفية حسب المجموعة:</label>
                        <select id="groupFilter" class="form-control select2">
                            <option value="">جميع المجموعات</option>
                            @foreach($groups as $group)
                                <option value="{{ $group }}">{{ ucfirst($group) }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="guardFilter">تصفية حسب الحارس:</label>
                        <select id="guardFilter" class="form-control select2">
                            <option value="">جميع الحراس</option>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">تطبيق التصفية</button>
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">مسح التصفية</button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="permissionsTable" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الصلاحية</th>
                                <th class="border-bottom-0">المجموعة</th>
                                <th class="border-bottom-0">الحارس</th>
                                <th class="border-bottom-0">عدد الأدوار</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

<!-- Add Permission Modal -->
<div class="modal fade" id="addPermissionModal" tabindex="-1" role="dialog" aria-labelledby="addPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPermissionModalLabel">إضافة صلاحية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addPermissionForm" action="{{ route('permissions.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="name">اسم الصلاحية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <small class="form-text text-muted">مثال: users.create, orders.view</small>
                    </div>
                    <div class="form-group">
                        <label for="guard_name">الحارس <span class="text-danger">*</span></label>
                        <select class="form-control" id="guard_name" name="guard_name" required>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div class="modal fade" id="editPermissionModal" tabindex="-1" role="dialog" aria-labelledby="editPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPermissionModalLabel">تعديل الصلاحية</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editPermissionForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_name">اسم الصلاحية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_guard_name">الحارس <span class="text-danger">*</span></label>
                        <select class="form-control" id="edit_guard_name" name="guard_name" required>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div class="modal fade" id="bulkCreateModal" tabindex="-1" role="dialog" aria-labelledby="bulkCreateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkCreateModalLabel">إنشاء صلاحيات متعددة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bulkCreateForm" action="{{ route('permissions.bulk-create') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="bulk_guard_name">الحارس <span class="text-danger">*</span></label>
                        <select class="form-control" id="bulk_guard_name" name="guard_name" required>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="permissions_list">قائمة الصلاحيات (واحدة في كل سطر) <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="permissions_list" rows="10" placeholder="users.create&#10;users.read&#10;users.update&#10;users.delete&#10;orders.create&#10;orders.read" required></textarea>
                        <small class="form-text text-muted">أدخل كل صلاحية في سطر منفصل</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء الصلاحيات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('js')
<!-- Internal Data tables -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!--Internal  Datatable js -->
<script src="{{URL::asset('assets/js/table-data.js')}}"></script>
<!-- Select2 -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Initialize DataTable
    var table = $('#permissionsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('permissions.data') }}",
            data: function (d) {
                d.group = $('#groupFilter').val();
                d.guard_name = $('#guardFilter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'group', name: 'group'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'roles_count', name: 'roles_count', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Apply filters
    window.applyFilters = function() {
        table.draw();
    };

    // Clear filters
    window.clearFilters = function() {
        $('#groupFilter').val('').trigger('change');
        $('#guardFilter').val('').trigger('change');
        table.draw();
    };

    // Edit permission
    window.editPermission = function(id) {
        $.get("{{ url('admin/permissions') }}/" + id, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_guard_name').val(data.guard_name);
            $('#editPermissionForm').attr('action', "{{ url('admin/permissions') }}/" + id);
            $('#editPermissionModal').modal('show');
        });
    };

    // Delete permission
    window.deletePermission = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "{{ url('admin/permissions') }}/" + id,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الصلاحية بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الصلاحية.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الصلاحية.", "error");
            }
        });
    };

    // Handle bulk create form
    $('#bulkCreateForm').on('submit', function(e) {
        e.preventDefault();
        
        var permissions = $('#permissions_list').val().split('\n').filter(function(line) {
            return line.trim() !== '';
        });
        
        var guardName = $('#bulk_guard_name').val();
        var permissionsData = permissions.map(function(permission) {
            return {
                name: permission.trim(),
                guard_name: guardName
            };
        });

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: {
                _token: "{{ csrf_token() }}",
                permissions: permissionsData
            },
            success: function(response) {
                $('#bulkCreateModal').modal('hide');
                swal("نجح!", "تم إنشاء الصلاحيات بنجاح.", "success");
                table.draw();
                $('#permissions_list').val('');
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء إنشاء الصلاحيات.", "error");
            }
        });
    });
});
</script>
@endsection