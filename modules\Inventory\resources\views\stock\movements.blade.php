@extends('layouts.master')

@push('inventory-styles')
<style>
.movement-card {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    transition: transform 0.2s ease;
}

.movement-card:hover {
    transform: translateY(-2px);
}

.movement-type-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
}

.movement-in {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.movement-out {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.movement-adjustment {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.timeline-item {
    border-left: 3px solid #e9ecef;
    padding-left: 20px;
    margin-bottom: 20px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #6c757d;
}

.timeline-item.in::before {
    background: #28a745;
}

.timeline-item.out::before {
    background: #dc3545;
}

.timeline-item.adjustment::before {
    background: #ffc107;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ حركات المخزون</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="exportMovements()">
                <i class="mdi mdi-download"></i> تصدير
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="generateMovementReport()">
                <i class="mdi mdi-file-chart"></i> تقرير الحركات
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Filters -->
<div class="row">
    <div class="col-xl-12">
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>المادة</label>
                        <select id="item-filter" class="form-control select2">
                            <option value="">جميع المواد</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>نوع الحركة</label>
                        <select id="type-filter" class="form-control select2">
                            <option value="">جميع الأنواع</option>
                            <option value="in">إدخال</option>
                            <option value="out">إخراج</option>
                            <option value="adjustment">تسوية</option>
                            <option value="transfer">نقل</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" id="date-from" class="form-control">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" id="date-to" class="form-control">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-primary mr-2" onclick="applyMovementFilters()">
                                <i class="mdi mdi-filter"></i> تطبيق
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearMovementFilters()">
                                <i class="mdi mdi-filter-remove"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Movement Statistics -->
<div class="row row-sm">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-success">
                        <i class="mdi mdi-arrow-down"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-success">{{ $stats['total_in'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">إجمالي الإدخال</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-danger">
                        <i class="mdi mdi-arrow-up"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-danger">{{ $stats['total_out'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">إجمالي الإخراج</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-adjust"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-warning">{{ $stats['total_adjustments'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">التسويات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-info">
                        <i class="mdi mdi-swap-horizontal"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-info">{{ $stats['total_transfers'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">النقل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Movements Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">سجل حركات المخزون</h4>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshMovementsTable()">
                                <i class="mdi mdi-refresh"></i> تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleTimelineView()">
                                <i class="mdi mdi-timeline"></i> عرض زمني
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View -->
                <div id="table-view">
                    <div class="table-responsive">
                        <table id="movements-table" class="table table-striped table-bordered text-md-nowrap">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>التاريخ والوقت</th>
                                    <th>المادة</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>المخزون قبل</th>
                                    <th>المخزون بعد</th>
                                    <th>السبب</th>
                                    <th>المستخدم</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Timeline View -->
                <div id="timeline-view" style="display: none;">
                    <div id="movements-timeline">
                        <!-- Timeline items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('inventory-scripts')
<script>
let movementsTable;
let timelineView = false;

$(document).ready(function() {
    initializeMovementsTable();
    loadItemsForFilter();
});

function initializeMovementsTable() {
    movementsTable = $('#movements-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.stock.movements.datatable") }}',
            data: function(d) {
                d.item_id = $('#item-filter').val();
                d.type = $('#type-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { 
                data: 'created_at', 
                name: 'created_at',
                render: function(data) {
                    return new Date(data).toLocaleString('ar-SA');
                }
            },
            { 
                data: 'item.product.name', 
                name: 'item.product.name',
                render: function(data, type, row) {
                    return `${data}<br><small class="text-muted">${row.item.product.sku}</small>`;
                }
            },
            { 
                data: 'type', 
                name: 'type',
                render: function(data, type, row) {
                    return getMovementTypeBadge(data, row.quantity);
                }
            },
            { 
                data: 'quantity', 
                name: 'quantity',
                render: function(data, type, row) {
                    const sign = data > 0 ? '+' : '';
                    const color = data > 0 ? 'text-success' : 'text-danger';
                    return `<span class="${color}">${sign}${data} ${row.item.product.unit?.symbol || ''}</span>`;
                }
            },
            { 
                data: 'stock_before', 
                name: 'stock_before',
                render: function(data, type, row) {
                    return `${data} ${row.item.product.unit?.symbol || ''}`;
                }
            },
            { 
                data: 'stock_after', 
                name: 'stock_after',
                render: function(data, type, row) {
                    return `${data} ${row.item.product.unit?.symbol || ''}`;
                }
            },
            { data: 'reason', name: 'reason' },
            { data: 'user.name', name: 'user.name' },
            { 
                data: 'notes', 
                name: 'notes',
                render: function(data) {
                    return data || '-';
                }
            }
        ],
        order: [[1, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger btn-sm'
            }
        ]
    });
}

function loadItemsForFilter() {
    $.get('{{ route("inventory.api.items") }}')
        .done(function(items) {
            const options = items.map(item => 
                `<option value="${item.id}">${item.product.name} (${item.product.sku})</option>`
            ).join('');
            
            $('#item-filter').html('<option value="">جميع المواد</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load items for filter');
        });
}

function getMovementTypeBadge(type, quantity) {
    let badge = '';
    let text = '';
    
    if (quantity > 0) {
        badge = 'movement-in';
        text = 'إدخال';
    } else if (quantity < 0) {
        badge = 'movement-out';
        text = 'إخراج';
    } else {
        badge = 'movement-adjustment';
        text = 'تسوية';
    }
    
    switch(type) {
        case 'purchase':
            text = 'شراء';
            break;
        case 'sale':
            text = 'بيع';
            break;
        case 'adjustment':
            text = 'تسوية';
            break;
        case 'transfer':
            text = 'نقل';
            break;
        case 'damage':
            text = 'تلف';
            break;
        case 'expired':
            text = 'انتهاء صلاحية';
            break;
        case 'count':
            text = 'جرد';
            break;
    }
    
    return `<span class="movement-type-badge ${badge}">${text}</span>`;
}

function applyMovementFilters() {
    movementsTable.ajax.reload();
}

function clearMovementFilters() {
    $('#item-filter').val('').trigger('change');
    $('#type-filter').val('').trigger('change');
    $('#date-from').val('');
    $('#date-to').val('');
    movementsTable.ajax.reload();
}

function refreshMovementsTable() {
    movementsTable.ajax.reload();
}

function toggleTimelineView() {
    timelineView = !timelineView;
    
    if (timelineView) {
        $('#table-view').hide();
        $('#timeline-view').show();
        loadTimelineData();
    } else {
        $('#timeline-view').hide();
        $('#table-view').show();
    }
}

function loadTimelineData() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    $.get('{{ route("inventory.api.stock.movements.timeline") }}', filters)
        .done(function(movements) {
            renderTimeline(movements);
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات الخط الزمني');
        });
}

function renderTimeline(movements) {
    let timelineHtml = '';
    
    movements.forEach(function(movement) {
        const typeClass = movement.quantity > 0 ? 'in' : (movement.quantity < 0 ? 'out' : 'adjustment');
        const quantityColor = movement.quantity > 0 ? 'text-success' : 'text-danger';
        const sign = movement.quantity > 0 ? '+' : '';
        
        timelineHtml += `
            <div class="timeline-item ${typeClass}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${movement.item.product.name}</h6>
                        <p class="mb-1">
                            <span class="${quantityColor} font-weight-bold">${sign}${movement.quantity} ${movement.item.product.unit?.symbol || ''}</span>
                            - ${getMovementTypeText(movement.type)}
                        </p>
                        <small class="text-muted">
                            ${movement.reason ? movement.reason + ' - ' : ''}
                            بواسطة ${movement.user.name}
                        </small>
                        ${movement.notes ? `<p class="mt-2 mb-0"><small class="text-muted">${movement.notes}</small></p>` : ''}
                    </div>
                    <div class="text-right">
                        <small class="text-muted">${new Date(movement.created_at).toLocaleString('ar-SA')}</small>
                        <div class="mt-1">
                            <small class="text-muted">
                                ${movement.stock_before} → ${movement.stock_after} ${movement.item.product.unit?.symbol || ''}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#movements-timeline').html(timelineHtml || '<div class="text-center text-muted py-4">لا توجد حركات مخزون</div>');
}

function getMovementTypeText(type) {
    const types = {
        'purchase': 'شراء',
        'sale': 'بيع',
        'adjustment': 'تسوية',
        'transfer': 'نقل',
        'damage': 'تلف',
        'expired': 'انتهاء صلاحية',
        'count': 'جرد',
        'other': 'أخرى'
    };
    
    return types[type] || type;
}

function exportMovements() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        format: 'excel'
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.stock.movements.export") }}?${queryString}`;
}

function generateMovementReport() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.stock.movements.report") }}?${queryString}`;
}
</script>
@endpush
