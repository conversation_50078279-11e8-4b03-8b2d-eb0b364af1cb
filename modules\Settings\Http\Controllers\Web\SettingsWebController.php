<?php

namespace Modules\Settings\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Services\SettingsService;
use App\Models\Settings\PrinterSetting;
use App\Models\Settings\PaymentSetting;
use App\Models\Settings\SecuritySetting;
use App\Models\Settings\BranchSetting;
use App\Models\Settings\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SettingsWebController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function index()
    {
        $tenantId = Auth::user()->tenant_id;
        
        // Get overview data for dashboard
        $overview = [
            'printer_count' => PrinterSetting::where('tenant_id', $tenantId)->count(),
            'payment_methods' => PaymentSetting::where('tenant_id', $tenantId)->where('is_enabled', true)->count(),
            'security_enabled' => SecuritySetting::where('tenant_id', $tenantId)->exists(),
            'branches_configured' => BranchSetting::where('tenant_id', $tenantId)->distinct('branch_id')->count(),
        ];

        return view('settings::index', compact('overview'));
    }

    // Printer Settings
    public function printerSettings(Request $request)
    {
        $tenantId = Auth::user()->tenant_id;
        $branchId = $request->input('branch_id');
        
        $printers = PrinterSetting::where('tenant_id', $tenantId)
            ->when($branchId, function($query, $branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->orderBy('type')
            ->orderBy('display_order')
            ->get();

        $branches = Auth::user()->tenant->branches;
        $printerTypes = PrinterSetting::getAvailableTypes();
        $connectionTypes = PrinterSetting::getConnectionTypes();

        return view('settings::printer.index', compact('printers', 'branches', 'printerTypes', 'connectionTypes'));
    }

    public function createPrinter()
    {
        $branches = Auth::user()->tenant->branches;
        $printerTypes = PrinterSetting::getAvailableTypes();
        $connectionTypes = PrinterSetting::getConnectionTypes();

        return view('settings::printer.create', compact('branches', 'printerTypes', 'connectionTypes'));
    }

    public function editPrinter($id)
    {
        $tenantId = Auth::user()->tenant_id;
        
        $printer = PrinterSetting::where('tenant_id', $tenantId)
            ->where('id', $id)
            ->firstOrFail();

        $branches = Auth::user()->tenant->branches;
        $printerTypes = PrinterSetting::getAvailableTypes();
        $connectionTypes = PrinterSetting::getConnectionTypes();

        return view('settings::printer.edit', compact('printer', 'branches', 'printerTypes', 'connectionTypes'));
    }

    // Payment Settings
    public function paymentSettings(Request $request)
    {
        $tenantId = Auth::user()->tenant_id;
        $branchId = $request->input('branch_id');
        
        $paymentSettings = PaymentSetting::where('tenant_id', $tenantId)
            ->when($branchId, function($query, $branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->orderBy('display_order')
            ->get();

        $branches = Auth::user()->tenant->branches;
        $availablePaymentMethods = PaymentSetting::getAvailablePaymentMethods();

        return view('settings::payment.index', compact('paymentSettings', 'branches', 'availablePaymentMethods'));
    }

    public function createPaymentSetting()
    {
        $branches = Auth::user()->tenant->branches;
        $availablePaymentMethods = PaymentSetting::getAvailablePaymentMethods();

        return view('settings::payment.create', compact('branches', 'availablePaymentMethods'));
    }

    public function editPaymentSetting($id)
    {
        $tenantId = Auth::user()->tenant_id;
        
        $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
            ->where('id', $id)
            ->firstOrFail();

        $branches = Auth::user()->tenant->branches;
        $availablePaymentMethods = PaymentSetting::getAvailablePaymentMethods();

        return view('settings::payment.edit', compact('paymentSetting', 'branches', 'availablePaymentMethods'));
    }

    // Security Settings
    public function securitySettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();
        
        if (!$securitySetting) {
            $securitySetting = SecuritySetting::create([
                'tenant_id' => $tenantId,
                'password_min_length' => 8,
                'password_require_uppercase' => true,
                'password_require_lowercase' => true,
                'password_require_numbers' => true,
                'password_require_symbols' => false,
                'password_expiry_days' => 90,
                'max_login_attempts' => 5,
                'lockout_duration' => 30,
                'session_timeout' => 120,
                'two_factor_enabled' => false,
                'ip_whitelist_enabled' => false,
                'audit_log_enabled' => true,
                'audit_log_retention_days' => 365,
            ]);
        }

        return view('settings::security.index', compact('securitySetting'));
    }

    // Branch Settings
    public function branchSettings(Request $request, $branchId = null)
    {
        $tenantId = Auth::user()->tenant_id;
        $branches = Auth::user()->tenant->branches;
        
        if (!$branchId && $branches->count() > 0) {
            $branchId = $branches->first()->id;
        }

        $categories = BranchSetting::getDefaultCategories();
        $settings = [];
        
        if ($branchId) {
            $branchSettings = BranchSetting::where('tenant_id', $tenantId)
                ->where('branch_id', $branchId)
                ->get()
                ->groupBy('category');
            
            foreach ($categories as $category => $categoryData) {
                $settings[$category] = $branchSettings->get($category, collect());
            }
        }

        return view('settings::branch.index', compact('branches', 'branchId', 'categories', 'settings'));
    }

    // System Settings
    public function systemSettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        $systemSettings = SystemSetting::where('tenant_id', $tenantId)->get()->keyBy('key');
        
        $categories = [
            'general' => [
                'name' => 'الإعدادات العامة',
                'keys' => ['app_name', 'app_logo', 'timezone', 'language', 'currency', 'date_format']
            ],
            'business' => [
                'name' => 'إعدادات الأعمال',
                'keys' => ['business_name', 'business_address', 'business_phone', 'business_email', 'tax_number']
            ],
            'pos' => [
                'name' => 'إعدادات نقطة البيع',
                'keys' => ['default_tax_rate', 'auto_print_receipt', 'sound_enabled', 'order_prefix']
            ]
        ];

        return view('settings::system.index', compact('systemSettings', 'categories'));
    }

    // Restaurant Settings
    public function restaurantSettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        // Get restaurant-specific settings from system settings
        $restaurantSettings = SystemSetting::where('tenant_id', $tenantId)
            ->whereIn('key', [
                'restaurant_name', 'restaurant_type', 'table_count', 'service_charge',
                'delivery_enabled', 'takeaway_enabled', 'dine_in_enabled',
                'kitchen_display_enabled', 'order_numbering_system'
            ])
            ->get()
            ->keyBy('key');

        return view('settings::restaurant.index', compact('restaurantSettings'));
    }

    // User/Role Settings
    public function userSettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        // This would integrate with your existing user/role system
        $roles = Auth::user()->tenant->roles ?? collect();
        $permissions = Auth::user()->tenant->permissions ?? collect();

        return view('settings::user.index', compact('roles', 'permissions'));
    }

    // Report Settings
    public function reportSettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        $reportSettings = SystemSetting::where('tenant_id', $tenantId)
            ->whereIn('key', [
                'default_report_format', 'report_logo', 'report_footer',
                'auto_email_reports', 'report_retention_days'
            ])
            ->get()
            ->keyBy('key');

        return view('settings::report.index', compact('reportSettings'));
    }

    // Inventory Settings
    public function inventorySettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        $inventorySettings = SystemSetting::where('tenant_id', $tenantId)
            ->whereIn('key', [
                'low_stock_threshold', 'auto_reorder_enabled', 'stock_valuation_method',
                'barcode_enabled', 'expiry_tracking_enabled'
            ])
            ->get()
            ->keyBy('key');

        return view('settings::inventory.index', compact('inventorySettings'));
    }

    // Backup Settings
    public function backupSettings()
    {
        $tenantId = Auth::user()->tenant_id;
        
        $backupSettings = SystemSetting::where('tenant_id', $tenantId)
            ->whereIn('key', [
                'auto_backup_enabled', 'backup_frequency', 'backup_retention_days',
                'backup_location', 'backup_encryption_enabled'
            ])
            ->get()
            ->keyBy('key');

        return view('settings::backup.index', compact('backupSettings'));
    }
}
