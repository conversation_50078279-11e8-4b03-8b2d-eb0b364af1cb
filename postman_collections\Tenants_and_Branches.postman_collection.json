{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Restaurant POS - Tenants & Branches", "description": "API collection for managing tenants and branches in the Restaurant POS system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Tenants", "item": [{"name": "Get All Tenants", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "description": "Optional for public access"}], "url": {"raw": "{{base_url}}/api/tenants?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["api", "tenants"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Create Tenant", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Sample Restaurant\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567890\",\n    \"address\": \"123 Main Street, City, State\",\n    \"subscription_plan\": \"basic\",\n    \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/api/tenants", "host": ["{{base_url}}"], "path": ["api", "tenants"]}}, "response": []}, {"name": "Get Tenant by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}"]}}, "response": []}, {"name": "Update Tenant", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Restaurant Name\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567891\",\n    \"address\": \"456 Updated Street, City, State\"\n}"}, "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}"]}}, "response": []}, {"name": "Delete Tenant", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}"]}}, "response": []}, {"name": "Activate Tenant", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}/activate", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}", "activate"]}}, "response": []}, {"name": "Deactivate Tenant", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}/deactivate", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}", "deactivate"]}}, "response": []}, {"name": "Get Tenant Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/tenants/{{tenant_id}}/statistics", "host": ["{{base_url}}"], "path": ["api", "tenants", "{{tenant_id}}", "statistics"]}}, "response": []}]}, {"name": "Branches", "item": [{"name": "Get All Branches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "description": "Optional for public access"}], "url": {"raw": "{{base_url}}/api/branches?page=1&per_page=15&tenant_id={{tenant_id}}", "host": ["{{base_url}}"], "path": ["api", "branches"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "tenant_id", "value": "{{tenant_id}}"}]}}, "response": []}, {"name": "Create Branch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"tenant_id\": {{tenant_id}},\n    \"name\": \"Downtown Branch\",\n    \"code\": \"DT001\",\n    \"address\": \"789 Downtown Ave, City, State\",\n    \"latitude\": \"40.7128\",\n    \"longitude\": \"-74.0060\",\n    \"phone\": \"+1234567892\",\n    \"email\": \"<EMAIL>\",\n    \"manager_name\": \"<PERSON>\",\n    \"opening_hours\": {\n        \"monday\": {\"open\": \"09:00\", \"close\": \"22:00\"},\n        \"tuesday\": {\"open\": \"09:00\", \"close\": \"22:00\"},\n        \"wednesday\": {\"open\": \"09:00\", \"close\": \"22:00\"},\n        \"thursday\": {\"open\": \"09:00\", \"close\": \"22:00\"},\n        \"friday\": {\"open\": \"09:00\", \"close\": \"23:00\"},\n        \"saturday\": {\"open\": \"10:00\", \"close\": \"23:00\"},\n        \"sunday\": {\"open\": \"10:00\", \"close\": \"21:00\"}\n    },\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/branches", "host": ["{{base_url}}"], "path": ["api", "branches"]}}, "response": []}, {"name": "Get Branch by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}"]}}, "response": []}, {"name": "Update Branch", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Branch Name\",\n    \"address\": \"Updated Address\",\n    \"phone\": \"+1234567893\",\n    \"email\": \"<EMAIL>\",\n    \"manager_name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}"]}}, "response": []}, {"name": "Delete Branch", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}"]}}, "response": []}, {"name": "Activate Branch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}/activate", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}", "activate"]}}, "response": []}, {"name": "Deactivate Branch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}/deactivate", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}", "deactivate"]}}, "response": []}, {"name": "Get Branch Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/branches/{{branch_id}}/statistics", "host": ["{{base_url}}"], "path": ["api", "branches", "{{branch_id}}", "statistics"]}}, "response": []}]}, {"name": "Subscriptions", "item": [{"name": "Get All Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions?page=1&per_page=15", "host": ["{{base_url}}"], "path": ["subscriptions"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}]}}, "response": []}, {"name": "Get Available Plans", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/plans", "host": ["{{base_url}}"], "path": ["subscriptions", "plans"]}}, "response": []}, {"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"tenant_id\": {{tenant_id}},\n    \"plan\": \"basic\",\n    \"billing_cycle\": \"monthly\"\n}"}, "url": {"raw": "{{base_url}}/subscriptions", "host": ["{{base_url}}"], "path": ["subscriptions"]}}, "response": []}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}"]}}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"plan\": \"premium\",\n    \"billing_cycle\": \"yearly\"\n}"}, "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}"]}}, "response": []}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}/cancel", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}", "cancel"]}}, "response": []}, {"name": "Suspend Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}/suspend", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}", "suspend"]}}, "response": []}, {"name": "Reactivate Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}/reactivate", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}", "reactivate"]}}, "response": []}, {"name": "Upgrade Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"new_plan\": \"premium\"\n}"}, "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}/upgrade", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}", "upgrade"]}}, "response": []}, {"name": "Get Subscription Usage", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions/{{subscription_id}}/usage", "host": ["{{base_url}}"], "path": ["subscriptions", "{{subscription_id}}", "usage"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "tenant_id", "value": "1", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "subscription_id", "value": "1", "type": "string"}]}