@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الأدوار</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" data-toggle="modal" data-target="#addRoleModal">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الأدوار</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة أدوار النظام وصلاحياتها</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="rolesTable" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الدور</th>
                                <th class="border-bottom-0">الحارس</th>
                                <th class="border-bottom-0">عدد الصلاحيات</th>
                                <th class="border-bottom-0">عدد المستخدمين</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

<!-- Add Role Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" role="dialog" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">إضافة دور جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addRoleForm" action="{{ route('roles.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم الدور <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="guard_name">الحارس <span class="text-danger">*</span></label>
                                <select class="form-control" id="guard_name" name="guard_name" required>
                                    <option value="web">ويب</option>
                                    <option value="api">API</option>
                                    <option value="sanctum">Sanctum</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>الصلاحيات:</label>
                        <div class="permissions-container" style="max-height: 300px; overflow-y: auto;">
                            <div class="row">
                                @foreach($permissions as $group => $groupPermissions)
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-header py-2">
                                                <h6 class="mb-0">
                                                    <input type="checkbox" class="group-checkbox" data-group="{{ $group }}">
                                                    {{ ucfirst($group) }}
                                                </h6>
                                            </div>
                                            <div class="card-body py-2">
                                                @foreach($groupPermissions as $permission)
                                                    <div class="form-check">
                                                        <input class="form-check-input permission-checkbox" type="checkbox" 
                                                               name="permissions[]" value="{{ $permission->name }}" 
                                                               id="perm_{{ $permission->id }}" data-group="{{ $group }}">
                                                        <label class="form-check-label" for="perm_{{ $permission->id }}">
                                                            {{ $permission->name }}
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1" role="dialog" aria-labelledby="editRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRoleModalLabel">تعديل الدور</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editRoleForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_name">اسم الدور <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_guard_name">الحارس <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_guard_name" name="guard_name" required>
                                    <option value="web">ويب</option>
                                    <option value="api">API</option>
                                    <option value="sanctum">Sanctum</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>الصلاحيات:</label>
                        <div class="edit-permissions-container" style="max-height: 300px; overflow-y: auto;">
                            <!-- Permissions will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign Permissions Modal -->
<div class="modal fade" id="assignPermissionsModal" tabindex="-1" role="dialog" aria-labelledby="assignPermissionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignPermissionsModalLabel">إدارة صلاحيات الدور</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="assignPermissionsForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="assign-permissions-container" style="max-height: 400px; overflow-y: auto;">
                        <!-- Permissions will be loaded dynamically -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الصلاحيات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('js')
<!-- Internal Data tables -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!--Internal  Datatable js -->
<script src="{{URL::asset('assets/js/table-data.js')}}"></script>
<!-- Select2 -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#rolesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('roles.data') }}",
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'permissions_count', name: 'permissions_count', orderable: false},
            {data: 'users_count', name: 'users_count', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Group checkbox functionality
    $(document).on('change', '.group-checkbox', function() {
        var group = $(this).data('group');
        var isChecked = $(this).is(':checked');
        $('.permission-checkbox[data-group="' + group + '"]').prop('checked', isChecked);
    });

    // Individual permission checkbox functionality
    $(document).on('change', '.permission-checkbox', function() {
        var group = $(this).data('group');
        var groupCheckbox = $('.group-checkbox[data-group="' + group + '"]');
        var groupPermissions = $('.permission-checkbox[data-group="' + group + '"]');
        var checkedPermissions = $('.permission-checkbox[data-group="' + group + '"]:checked');
        
        if (checkedPermissions.length === groupPermissions.length) {
            groupCheckbox.prop('checked', true);
        } else {
            groupCheckbox.prop('checked', false);
        }
    });

    // Edit role
    window.editRole = function(id) {
        $.get("{{ url('admin/roles') }}/" + id, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_guard_name').val(data.guard_name);
            $('#editRoleForm').attr('action', "{{ url('admin/roles') }}/" + id);
            
            // Load permissions for editing
            loadPermissionsForEdit(data.permissions);
            
            $('#editRoleModal').modal('show');
        });
    };

    // Load permissions for edit modal
    function loadPermissionsForEdit(rolePermissions) {
        $.get("{{ route('permissions.index') }}", function(response) {
            var permissionsHtml = '<div class="row">';
            
            @foreach($permissions as $group => $groupPermissions)
                permissionsHtml += '<div class="col-md-6 mb-3">';
                permissionsHtml += '<div class="card">';
                permissionsHtml += '<div class="card-header py-2">';
                permissionsHtml += '<h6 class="mb-0">';
                permissionsHtml += '<input type="checkbox" class="group-checkbox" data-group="{{ $group }}">';
                permissionsHtml += ' {{ ucfirst($group) }}';
                permissionsHtml += '</h6>';
                permissionsHtml += '</div>';
                permissionsHtml += '<div class="card-body py-2">';
                
                @foreach($groupPermissions as $permission)
                    var isChecked = rolePermissions.some(p => p.name === '{{ $permission->name }}');
                    permissionsHtml += '<div class="form-check">';
                    permissionsHtml += '<input class="form-check-input permission-checkbox" type="checkbox" ';
                    permissionsHtml += 'name="permissions[]" value="{{ $permission->name }}" ';
                    permissionsHtml += 'id="edit_perm_{{ $permission->id }}" data-group="{{ $group }}"';
                    if (isChecked) permissionsHtml += ' checked';
                    permissionsHtml += '>';
                    permissionsHtml += '<label class="form-check-label" for="edit_perm_{{ $permission->id }}">';
                    permissionsHtml += '{{ $permission->name }}';
                    permissionsHtml += '</label>';
                    permissionsHtml += '</div>';
                @endforeach
                
                permissionsHtml += '</div>';
                permissionsHtml += '</div>';
                permissionsHtml += '</div>';
            @endforeach
            
            permissionsHtml += '</div>';
            $('.edit-permissions-container').html(permissionsHtml);
        });
    }

    // Assign permissions to role
    window.assignPermissions = function(id) {
        $.get("{{ url('admin/roles') }}/" + id + "/permissions", function(data) {
            $('#assignPermissionsForm').attr('action', "{{ url('admin/roles') }}/" + id + "/permissions");
            
            var permissionsHtml = '<div class="row">';
            
            @foreach($permissions as $group => $groupPermissions)
                permissionsHtml += '<div class="col-md-6 mb-3">';
                permissionsHtml += '<div class="card">';
                permissionsHtml += '<div class="card-header py-2">';
                permissionsHtml += '<h6 class="mb-0">';
                permissionsHtml += '<input type="checkbox" class="group-checkbox" data-group="{{ $group }}">';
                permissionsHtml += ' {{ ucfirst($group) }}';
                permissionsHtml += '</h6>';
                permissionsHtml += '</div>';
                permissionsHtml += '<div class="card-body py-2">';
                
                @foreach($groupPermissions as $permission)
                    var isChecked = data.rolePermissions.includes('{{ $permission->name }}');
                    permissionsHtml += '<div class="form-check">';
                    permissionsHtml += '<input class="form-check-input permission-checkbox" type="checkbox" ';
                    permissionsHtml += 'name="permissions[]" value="{{ $permission->name }}" ';
                    permissionsHtml += 'id="assign_perm_{{ $permission->id }}" data-group="{{ $group }}"';
                    if (isChecked) permissionsHtml += ' checked';
                    permissionsHtml += '>';
                    permissionsHtml += '<label class="form-check-label" for="assign_perm_{{ $permission->id }}">';
                    permissionsHtml += '{{ $permission->name }}';
                    permissionsHtml += '</label>';
                    permissionsHtml += '</div>';
                @endforeach
                
                permissionsHtml += '</div>';
                permissionsHtml += '</div>';
                permissionsHtml += '</div>';
            @endforeach
            
            permissionsHtml += '</div>';
            $('.assign-permissions-container').html(permissionsHtml);
            
            $('#assignPermissionsModal').modal('show');
        });
    };

    // Delete role
    window.deleteRole = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "{{ url('admin/roles') }}/" + id,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الدور بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الدور.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الدور.", "error");
            }
        });
    };

    // View role users
    window.viewRoleUsers = function(id) {
        window.location.href = "{{ url('admin/roles') }}/" + id + "/users";
    };
});
</script>
@endsection