<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('waiter_requests', function (Blueprint $table) {
            // Only add branch_id if it doesn't exist
            if (!Schema::hasColumn('waiter_requests', 'branch_id')) {
                // Add branch_id column without foreign key constraint first
                $table->unsignedBigInteger('branch_id')->nullable()->after('table_id');
            }
            
            // Add other columns if they don't exist
            if (!Schema::hasColumn('waiter_requests', 'notes')) {
                $table->text('notes')->nullable()->after('waiter_id');
            }
            
            if (!Schema::hasColumn('waiter_requests', 'request_type')) {
                $table->string('request_type')->default('service')->after('notes');
            }
            
            if (!Schema::hasColumn('waiter_requests', 'response_time')) {
                $table->timestamp('response_time')->nullable()->after('request_type');
            }
        });
        
        // Update existing records to have valid branch_id values
        if (Schema::hasColumn('waiter_requests', 'branch_id')) {
            // Get the first available branch ID
            $firstBranch = DB::table('branches')->first();
            
            if ($firstBranch) {
                // Update all waiter_requests that have NULL branch_id
                // Set branch_id based on the table's area's branch_id
                DB::statement("
                    UPDATE waiter_requests wr
                    JOIN tables t ON wr.table_id = t.id
                    JOIN areas a ON t.area_id = a.id
                    SET wr.branch_id = a.branch_id
                    WHERE wr.branch_id IS NULL
                ");
                
                // For any remaining NULL values, set to first branch
                DB::table('waiter_requests')
                    ->whereNull('branch_id')
                    ->update(['branch_id' => $firstBranch->id]);
            }
        }
        
        // Now add the foreign key constraint and make column non-nullable
        Schema::table('waiter_requests', function (Blueprint $table) {
            if (Schema::hasColumn('waiter_requests', 'branch_id')) {
                // Make branch_id non-nullable
                $table->unsignedBigInteger('branch_id')->nullable(false)->change();
                
                // Add foreign key constraint
                $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            }
        });
        
        // Add indexes after all columns are created
        Schema::table('waiter_requests', function (Blueprint $table) {
            // Check if indexes don't exist before adding them
            if (Schema::hasColumn('waiter_requests', 'branch_id')) {
                // Check if index doesn't already exist
                $indexExists = collect(DB::select("SHOW INDEX FROM waiter_requests WHERE Key_name = 'waiter_requests_branch_status_index'"))->isNotEmpty();
                if (!$indexExists) {
                    $table->index(['branch_id', 'status'], 'waiter_requests_branch_status_index');
                }
                
                $indexExists2 = collect(DB::select("SHOW INDEX FROM waiter_requests WHERE Key_name = 'waiter_requests_branch_table_index'"))->isNotEmpty();
                if (!$indexExists2) {
                    $table->index(['branch_id', 'table_id'], 'waiter_requests_branch_table_index');
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('waiter_requests', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn(['branch_id', 'notes', 'request_type', 'response_time']);
        });
    }
};
