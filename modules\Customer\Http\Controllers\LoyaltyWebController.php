<?php

namespace Modules\Customer\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\LoyaltyProgram;
use App\Models\LoyaltyTransaction;
use Modules\Customer\Services\LoyaltyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class LoyaltyWebController extends Controller
{
    protected $loyaltyService;

    public function __construct(LoyaltyService $loyaltyService)
    {
        $this->loyaltyService = $loyaltyService;
    }

    /**
     * Show customer loyalty details
     */
    public function show(Customer $customer)
    {
        $customer->load(['loyaltyTransactions' => function($query) {
            $query->latest()->limit(10);
        }]);
        
        $totalEarned = $customer->loyaltyTransactions()
            ->where('type', 'earned')
            ->sum('points');
            
        $totalRedeemed = $customer->loyaltyTransactions()
            ->where('type', 'redeemed')
            ->sum('points');
        
        return view('customers::loyalty.show', compact('customer', 'totalEarned', 'totalRedeemed'));
    }

    /**
     * Add loyalty points
     */
    public function addPoints(Request $request, Customer $customer)
    {
        $request->validate([
            'points' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255'
        ]);

        try {
            $this->loyaltyService->addPoints(
                $customer->id,
                $request->points,
                $request->description,
                Auth::id()
            );

            return redirect()->back()->with('success', 'تم إضافة النقاط بنجاح');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'خطأ في إضافة النقاط: ' . $e->getMessage());
        }
    }

    /**
     * Redeem loyalty points
     */
    public function redeemPoints(Request $request, Customer $customer)
    {
        $request->validate([
            'points' => 'required|numeric|min:0.01|max:' . $customer->loyalty_points,
            'description' => 'required|string|max:255'
        ]);

        try {
            $this->loyaltyService->redeemPoints(
                $customer->id,
                $request->points,
                $request->description,
                Auth::id()
            );

            return redirect()->back()->with('success', 'تم استخدام النقاط بنجاح');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'خطأ في استخدام النقاط: ' . $e->getMessage());
        }
    }

    /**
     * Show loyalty transaction history
     */
    public function history(Customer $customer)
    {
        return view('customers::loyalty.history', compact('customer'));
    }

    /**
     * Get loyalty transactions data for DataTable
     */
    public function getTransactionsData(Request $request, Customer $customer)
    {
        $query = LoyaltyTransaction::with(['processedBy'])
            ->where('customer_id', $customer->id);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('type_badge', function ($transaction) {
                $badges = [
                    'earned' => '<span class="badge badge-success">مكتسب</span>',
                    'redeemed' => '<span class="badge badge-warning">مستخدم</span>',
                    'expired' => '<span class="badge badge-danger">منتهي الصلاحية</span>',
                    'adjusted' => '<span class="badge badge-info">تعديل</span>'
                ];
                return $badges[$transaction->transaction_type] ?? $transaction->transaction_type;
            })
            ->addColumn('points_formatted', function ($transaction) {
                $color = $transaction->transaction_type === 'earned' ? 'text-success' : 'text-danger';
                $sign = $transaction->transaction_type === 'earned' ? '+' : '-';
                return '<span class="' . $color . '">' . $sign . number_format(abs($transaction->points), 2) . '</span>';
            })
            ->addColumn('processed_by_name', function ($transaction) {
                return $transaction->processedBy ? $transaction->processedBy->name : '-';
            })
            ->addColumn('date_formatted', function ($transaction) {
                return $transaction->created_at->format('Y-m-d H:i:s');
            })
            ->addColumn('expires_at_formatted', function ($transaction) {
                return $transaction->expires_at ? $transaction->expires_at->format('Y-m-d') : '-';
            })
            ->rawColumns(['type_badge', 'points_formatted'])
            ->make(true);
    }

    /**
     * Show loyalty programs management
     */
    public function programs()
    {
        $user = Auth::user();
        return view('customers::loyalty.programs');
    }

    /**
     * Show create loyalty program form
     */
    public function createProgram()
    {
        return view('customers::loyalty.create-program');
    }

    /**
     * Store new loyalty program
     */
    public function storeProgram(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:points,visits,spending',
            'points_per_currency_unit' => 'required|numeric|min:0',
            'currency_per_point' => 'required|numeric|min:0',
            'minimum_points_redemption' => 'required|integer|min:1',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        $user = Auth::user();
        $data = $request->all();
        $data['tenant_id'] = $user->tenant_id;

        LoyaltyProgram::create($data);

        return redirect()->route('loyalty-programs.index')
            ->with('success', 'تم إنشاء برنامج الولاء بنجاح');
    }

    /**
     * Show edit loyalty program form
     */
    public function editProgram(LoyaltyProgram $program)
    {
        return view('customers::loyalty.edit-program', compact('program'));
    }

    /**
     * Update loyalty program
     */
    public function updateProgram(Request $request, LoyaltyProgram $program)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:points,visits,spending',
            'points_per_currency_unit' => 'required|numeric|min:0',
            'currency_per_point' => 'required|numeric|min:0',
            'minimum_points_redemption' => 'required|integer|min:1',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        $program->update($request->all());

        return redirect()->route('loyalty-programs.index')
            ->with('success', 'تم تحديث برنامج الولاء بنجاح');
    }

    /**
     * Delete loyalty program
     */
    public function destroyProgram(LoyaltyProgram $program)
    {
        $program->delete();

        return redirect()->route('loyalty-programs.index')
            ->with('success', 'تم حذف برنامج الولاء بنجاح');
    }

    /**
     * Get loyalty programs data for DataTable
     */
    public function getProgramsData(Request $request)
    {
        $user = Auth::user();
        $query = LoyaltyProgram::where('tenant_id', $user->tenant_id);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('status', function ($program) {
                if ($program->is_active) {
                    return '<span class="badge badge-success">نشط</span>';
                } else {
                    return '<span class="badge badge-danger">غير نشط</span>';
                }
            })
            ->addColumn('type_formatted', function ($program) {
                $types = [
                    'points' => 'نقاط',
                    'visits' => 'زيارات',
                    'spending' => 'إنفاق'
                ];
                return $types[$program->type] ?? $program->type;
            })
            ->addColumn('date_range', function ($program) {
                $start = $program->start_date->format('Y-m-d');
                $end = $program->end_date ? $program->end_date->format('Y-m-d') : 'مفتوح';
                return $start . ' - ' . $end;
            })
            ->addColumn('action', function ($program) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('loyalty-programs.edit', $program) . '" class="btn btn-sm btn-primary" title="تعديل"><i class="fa fa-edit"></i></a>';
                $actions .= '<button type="button" class="btn btn-sm btn-danger delete-program" data-id="' . $program->id . '" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }
}
