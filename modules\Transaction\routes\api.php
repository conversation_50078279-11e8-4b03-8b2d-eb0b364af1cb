<?php

use Illuminate\Support\Facades\Route;
use Modules\Transaction\Http\Controllers\TransactionController;
use Modules\Transaction\Http\Controllers\PaymentController;

/*
|--------------------------------------------------------------------------
| Transaction Module API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Transaction module.
| These routes are loaded by the TransactionServiceProvider within a group
| which is assigned the "api" middleware group.
|
*/

Route::prefix('transactions')->group(function () {
    // Transaction routes
    Route::get('/', [TransactionController::class, 'index'])->name('api.transactions.index');
    Route::post('/', [TransactionController::class, 'store'])->name('api.transactions.store');
    Route::get('/statistics', [TransactionController::class, 'statistics'])->name('api.transactions.statistics');
    Route::get('/due', [TransactionController::class, 'due'])->name('api.transactions.due');
    Route::get('/{id}', [TransactionController::class, 'show'])->name('api.transactions.show');
    Route::put('/{id}', [TransactionController::class, 'update'])->name('api.transactions.update');
    Route::delete('/{id}', [TransactionController::class, 'destroy'])->name('api.transactions.destroy');
    Route::post('/{id}/update-status', [TransactionController::class, 'updateStatus'])->name('api.transactions.update-status');
});

Route::prefix('payments')->group(function () {
    // Payment routes
    Route::get('/', [PaymentController::class, 'index'])->name('api.payments.index');
    Route::post('/', [PaymentController::class, 'store'])->name('api.payments.store');
    Route::get('/statistics', [PaymentController::class, 'statistics'])->name('api.payments.statistics');
    Route::get('/payment-methods', [PaymentController::class, 'paymentMethods'])->name('api.payments.payment-methods');
    Route::get('/transaction/{transactionId}', [PaymentController::class, 'byTransaction'])->name('api.payments.by-transaction');
    Route::get('/{id}', [PaymentController::class, 'show'])->name('api.payments.show');
    Route::post('/{id}/cancel', [PaymentController::class, 'cancel'])->name('api.payments.cancel');
    Route::post('/{id}/refund', [PaymentController::class, 'refund'])->name('api.payments.refund');
});
