<?php

namespace Modules\Customer\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Customer\Services\CustomerService;
use Modules\Customer\Http\Requests\StoreCustomerRequest;
use Modules\Customer\Http\Requests\UpdateCustomerRequest;
use Modules\Customer\Http\Requests\LoyaltyPointsRequest;
use Modules\Customer\Http\Resources\CustomerResource;
use Modules\Customer\Http\Resources\CustomerCollection;
use Modules\Customer\Http\Resources\LoyaltyTransactionResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CustomerController extends Controller
{
    protected CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Get all customers for a specific branch
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }
        
        $branchId = $user->branch_id;
        $perPage = $request->input('per_page', 15);

        $customers = $this->customerService->getCustomersByBranch($branchId, $perPage);

        return response()->json([
            'success' => true,
            'data' => new CustomerCollection($customers),
            'pagination' => [
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'per_page' => $customers->perPage(),
                'total' => $customers->total(),
            ]
        ]);
    }

    /**
     * Store a new customer
     */
    public function store(StoreCustomerRequest $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }
        
        $data = $request->validated();
        $data['tenant_id'] = $user->tenant_id;
        $data['branch_id'] = $user->branch_id;
        
        $customer = $this->customerService->createCustomer($data);

        return response()->json([
            'success' => true,
            'message' => 'Customer created successfully',
            'data' => new CustomerResource($customer)
        ], 201);
    }

    /**
     * Show a specific customer
     */
    public function show($id)
    {
        try {
            $customer = $this->customerService->getCustomerById($id);
            
            return response()->json([
                'success' => true,
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Update a customer
     */
    public function update(UpdateCustomerRequest $request, $id)
    {
        try {
            $user = auth()->user();
            
            // Check if user is assigned to a branch
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;

            $customer = $this->customerService->updateCustomer($id, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Delete a customer
     */
    public function destroy($id)
    {
        try {
            $this->customerService->deleteCustomer($id);
            
            return response()->json([
                'success' => true,
                'message' => 'Customer deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Search customers by phone, email, or name
     */
    public function search(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }
        
        $branchId = $user->branch_id;
        $searchTerm = $request->input('search');

        if (!$searchTerm) {
            return response()->json([
                'success' => false,
                'message' => 'Search term is required'
            ], 400);
        }

        $customers = $this->customerService->searchCustomers($branchId, $searchTerm);

        return response()->json([
            'success' => true,
            'data' => CustomerResource::collection($customers)
        ]);
    }

    /**
     * Activate a customer
     */
    public function activate(int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->activateCustomer($id);

            return response()->json([
                'success' => true,
                'message' => 'Customer activated successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Deactivate a customer
     */
    public function deactivate(int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->deactivateCustomer($id);

            return response()->json([
                'success' => true,
                'message' => 'Customer deactivated successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Update customer's last visit
     */
    public function updateLastVisit(int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->updateLastVisit($id);

            return response()->json([
                'success' => true,
                'message' => 'Last visit updated successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(LoyaltyPointsRequest $request, int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->addLoyaltyPoints(
                $id,
                $request->input('points'),
                $request->input('description'),
                auth()->id()
            );

            return response()->json([
                'success' => true,
                'message' => 'Loyalty points added successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemLoyaltyPoints(LoyaltyPointsRequest $request, int $id): JsonResponse
    {
        try {
            $customer = $this->customerService->redeemLoyaltyPoints(
                $id,
                $request->input('points'),
                $request->input('description'),
                auth()->id()
            );

            return response()->json([
                'success' => true,
                'message' => 'Loyalty points redeemed successfully',
                'data' => new CustomerResource($customer)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get customer's loyalty history
     */
    public function loyaltyHistory(int $id): JsonResponse
    {
        try {
            $loyaltyHistory = $this->customerService->getLoyaltyHistory($id);

            return response()->json([
                'success' => true,
                'data' => LoyaltyTransactionResource::collection($loyaltyHistory)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ], 404);
        }
    }

    /**
     * Get active customers by branch
     */
    public function activeCustomers(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'User is not assigned to any branch'
            ], 400);
        }
        
        $branchId = $user->branch_id;

        $customers = $this->customerService->getActiveCustomersByBranch($branchId);

        return response()->json([
            'success' => true,
            'data' => CustomerResource::collection($customers)
        ]);
    }
}