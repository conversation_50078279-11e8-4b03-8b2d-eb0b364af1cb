<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Table;
use Modules\Reservation\Services\QRCodeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class QRTestController extends Controller
{
    protected QRCodeService $qrCodeService;

    public function __construct(QRCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Display the QR test page
     */
    public function index()
    {
        $tables = Table::with(['branch', 'area'])->get();
        return view('reservation::qr-test', compact('tables'));
    }

    /**
     * Generate QR code for a table
     */
    public function generateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_id' => 'required|exists:tables,id',
                'type' => 'required|in:table,menu,order',
                'size' => 'nullable|integer|min:100|max:500'
            ]);

            $qrData = $this->qrCodeService->generateTableQR(
                $request->table_id,
                [
                    'type' => $request->type,
                    'size' => $request->size ?? 200
                ]
            );

            return response()->json([
                'success' => true,
                'data' => $qrData,
                'message' => 'QR code generated successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate QR code data
     */
    public function validateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'qr_data' => 'required|string'
            ]);

            $result = $this->qrCodeService->validateQRCode($request->qr_data);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'QR code validated successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate QR code: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate custom QR code
     */
    public function generateCustomQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'text' => 'required|string',
                'size' => 'nullable|integer|min:100|max:500'
            ]);

            $qrData = [
                'text' => $request->text,
                'size' => $request->size ?? 200,
                'url' => url('/') . '/qr-redirect?data=' . urlencode($request->text)
            ];

            return response()->json([
                'success' => true,
                'data' => $qrData,
                'message' => 'Custom QR code generated successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate custom QR code: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Batch generate QR codes
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_ids' => 'required|array',
                'table_ids.*' => 'exists:tables,id',
                'type' => 'required|in:table,menu,order'
            ]);

            $results = [];
            foreach ($request->table_ids as $tableId) {
                try {
                    $qrData = $this->qrCodeService->generateTableQR($tableId, [
                        'type' => $request->type,
                        'size' => 200
                    ]);
                    $results[] = [
                        'table_id' => $tableId,
                        'success' => true,
                        'data' => $qrData
                    ];
                } catch (Exception $e) {
                    $results[] = [
                        'table_id' => $tableId,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Batch QR generation completed'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to batch generate QR codes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get table information
     */
    public function getTableInfo(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_id' => 'required|exists:tables,id'
            ]);

            $table = Table::with(['branch', 'area'])->find($request->table_id);

            return response()->json([
                'success' => true,
                'data' => $table,
                'message' => 'Table information retrieved successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get table information: ' . $e->getMessage()
            ], 500);
        }
    }
}