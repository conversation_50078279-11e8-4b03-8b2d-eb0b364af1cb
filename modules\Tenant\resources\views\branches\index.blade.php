@extends('layouts.master')

@section('title', 'إدارة الفروع')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">إدارة الفروع</h3>
                    <a href="{{ route('branches.create') }}" class="btn btn-primary">
                        <i class="fa fa-plus"></i> إضافة فرع جديد
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="statusFilter">الحالة</label>
                            <select id="statusFilter" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tenantFilter">المستأجر</label>
                            <select id="tenantFilter" class="form-control">
                                <option value="">جميع المستأجرين</option>
                                @foreach($tenants as $tenant)
                                    <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" id="resetFilters" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="branches-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الكود</th>
                                    <th>المستأجر</th>
                                    <th>العنوان</th>
                                    <th>معلومات الاتصال</th>
                                    <th>مدير الفرع</th>
                                    <th>الخدمات</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تغيير الحالة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" id="confirmStatusChange" class="btn btn-primary">تأكيد</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا الفرع؟ هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" id="confirmDelete" class="btn btn-danger">حذف</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .contact-info div {
        margin-bottom: 2px;
        font-size: 0.9em;
    }
    .contact-info i {
        width: 15px;
        margin-right: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    let table = $('#branches-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("branches.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.tenant_id = $('#tenantFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'tenant_name', name: 'tenant.name' },
            { data: 'address', name: 'address' },
            { data: 'contact_info', name: 'contact_info', orderable: false, searchable: false },
            { data: 'manager_name', name: 'manager_name' },
            { data: 'services', name: 'services', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[9, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
    });

    // Filter change handlers
    $('#statusFilter, #tenantFilter').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#statusFilter, #tenantFilter').val('');
        table.draw();
    });

    // Status change functionality
    let currentBranchId = null;
    let currentAction = null;

    window.changeStatus = function(branchId, action) {
        currentBranchId = branchId;
        currentAction = action;
        
        let messages = {
            'activate': 'هل أنت متأكد من تفعيل هذا الفرع؟',
            'deactivate': 'هل أنت متأكد من إلغاء تفعيل هذا الفرع؟'
        };
        
        $('#statusMessage').text(messages[action]);
        $('#statusModal').modal('show');
    };

    $('#confirmStatusChange').click(function() {
        if (currentBranchId && currentAction) {
            let url = '{{ route("branches.activate", ":id") }}';
            if (currentAction === 'deactivate') {
                url = '{{ route("branches.deactivate", ":id") }}';
            }
            url = url.replace(':id', currentBranchId);
            
            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#statusModal').modal('hide');
                    table.draw();
                    // Show success message
                    if (response.success) {
                        alert('تم تغيير حالة الفرع بنجاح');
                    }
                },
                error: function(xhr) {
                    $('#statusModal').modal('hide');
                    alert('حدث خطأ أثناء تغيير حالة الفرع');
                }
            });
        }
    });

    // Delete functionality
    window.deleteBranch = function(branchId) {
        currentBranchId = branchId;
        $('#deleteModal').modal('show');
    };

    $('#confirmDelete').click(function() {
        if (currentBranchId) {
            let url = '{{ route("branches.destroy", ":id") }}';
            url = url.replace(':id', currentBranchId);
            
            $.ajax({
                url: url,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#deleteModal').modal('hide');
                    table.draw();
                    // Show success message
                    if (response.success) {
                        alert('تم حذف الفرع بنجاح');
                    }
                },
                error: function(xhr) {
                    $('#deleteModal').modal('hide');
                    let message = 'حدث خطأ أثناء حذف الفرع';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    alert(message);
                }
            });
        }
    });
});
</script>
@endpush