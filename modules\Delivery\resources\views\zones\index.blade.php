@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة مناطق التوصيل</h3>
                    <div class="card-tools">
                        <a href="{{ route('delivery.zones.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> إضافة منطقة جديدة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="branch-filter" class="form-control">
                                <option value="">جميع الفروع</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="status-filter" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="1">نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" id="filter-btn" class="btn btn-info">
                                <i class="fa fa-filter"></i> تطبيق الفلتر
                            </button>
                            <button type="button" id="reset-btn" class="btn btn-secondary">
                                <i class="fa fa-refresh"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="zones-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المنطقة</th>
                                    <th>الفرع</th>
                                    <th>معلومات التوصيل</th>
                                    <th>الأولوية</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير حالة المنطقة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="status-message"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" id="confirm-status" class="btn btn-primary">تأكيد</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف منطقة التوصيل</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه المنطقة؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="delete-form" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#zones-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.zones.data") }}',
            data: function(d) {
                d.branch_id = $('#branch-filter').val();
                d.is_active = $('#status-filter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'branch_name', name: 'branch.name' },
            { data: 'delivery_info', name: 'delivery_info', orderable: false, searchable: false },
            { data: 'priority', name: 'priority' },
            { data: 'status_badge', name: 'is_active' },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[6, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        }
    });

    // Filter functionality
    $('#filter-btn').click(function() {
        table.draw();
    });

    $('#reset-btn').click(function() {
        $('#branch-filter').val('');
        $('#status-filter').val('');
        table.draw();
    });

    // Status toggle functionality
    window.toggleStatus = function(zoneId, isActive) {
        $('#status-message').text(isActive ? 'هل تريد تفعيل هذه المنطقة؟' : 'هل تريد إلغاء تفعيل هذه المنطقة؟');
        $('#statusModal').modal('show');
        
        $('#confirm-status').off('click').on('click', function() {
            $.ajax({
                url: '{{ route("delivery.zones.toggle-status", ":id") }}'.replace(':id', zoneId),
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    is_active: isActive
                },
                success: function(response) {
                    if (response.success) {
                        $('#statusModal').modal('hide');
                        table.draw(false);
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('حدث خطأ أثناء تغيير حالة المنطقة');
                }
            });
        });
    };

    // Delete functionality
    window.deleteZone = function(zoneId) {
        $('#delete-form').attr('action', '{{ route("delivery.zones.destroy", ":id") }}'.replace(':id', zoneId));
        $('#deleteModal').modal('show');
    };
});
</script>
@endpush