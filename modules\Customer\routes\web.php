<?php

use Illuminate\Support\Facades\Route;
use Modu<PERSON>\Customer\Http\Controllers\CustomerWebController;
use Modules\Customer\Http\Controllers\LoyaltyWebController;

/*
|--------------------------------------------------------------------------
| Customer Web Routes
|--------------------------------------------------------------------------
|
| Here are the web routes for the Customer module management interface.
|
*/

Route::middleware(['auth'])->group(function () {
    // Customer Management Routes
    Route::prefix('customers')->name('customers.')->group(function () {
        Route::get('/', [CustomerWebController::class, 'index'])->name('index');
        Route::get('/create', [CustomerWebController::class, 'create'])->name('create');
        Route::post('/', [CustomerWebController::class, 'store'])->name('store');
        Route::get('/{customer}', [CustomerWebController::class, 'show'])->name('show');
        Route::get('/{customer}/edit', [CustomerWebController::class, 'edit'])->name('edit');
        Route::put('/{customer}', [CustomerWebController::class, 'update'])->name('update');
        Route::delete('/{customer}', [CustomerWebController::class, 'destroy'])->name('destroy');
        
        // DataTable AJAX endpoint
        Route::get('/data/customers', [CustomerWebController::class, 'getCustomersData'])->name('data');
        
        // Loyalty Points Management
        Route::prefix('{customer}/loyalty')->name('loyalty.')->group(function () {
            Route::get('/', [LoyaltyWebController::class, 'show'])->name('show');
            Route::post('/add', [LoyaltyWebController::class, 'addPoints'])->name('add');
            Route::post('/redeem', [LoyaltyWebController::class, 'redeemPoints'])->name('redeem');
            Route::get('/history', [LoyaltyWebController::class, 'history'])->name('history');
            Route::get('/data/transactions', [LoyaltyWebController::class, 'getTransactionsData'])->name('data');
        });
    });
    
    // Loyalty Programs Management
    Route::prefix('loyalty-programs')->name('loyalty-programs.')->group(function () {
        Route::get('/', [LoyaltyWebController::class, 'programs'])->name('index');
        Route::get('/create', [LoyaltyWebController::class, 'createProgram'])->name('create');
        Route::post('/', [LoyaltyWebController::class, 'storeProgram'])->name('store');
        Route::get('/{program}/edit', [LoyaltyWebController::class, 'editProgram'])->name('edit');
        Route::put('/{program}', [LoyaltyWebController::class, 'updateProgram'])->name('update');
        Route::delete('/{program}', [LoyaltyWebController::class, 'destroyProgram'])->name('destroy');
        
        // DataTable AJAX endpoint
        Route::get('/data/programs', [LoyaltyWebController::class, 'getProgramsData'])->name('data');
    });
});
