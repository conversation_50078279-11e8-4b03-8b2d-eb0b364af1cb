<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the new image column first
        Schema::table('menu_items', function (Blueprint $table) {
            $table->string('image', 500)->nullable()->after('cost_price')->comment('Single image path');
        });
        
        // Migrate existing data from image_urls to image
        $menuItems = DB::table('menu_items')->whereNotNull('image_urls')->get();
        
        foreach ($menuItems as $item) {
            $imageUrls = json_decode($item->image_urls, true);
            if (is_array($imageUrls) && !empty($imageUrls)) {
                // Take the first image from the array
                $firstImage = $imageUrls[0];
                DB::table('menu_items')
                    ->where('id', $item->id)
                    ->update(['image' => $firstImage]);
            }
        }
        
        // Now drop the old column
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn('image_urls');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back the image_urls column
        Schema::table('menu_items', function (Blueprint $table) {
            $table->json('image_urls')->nullable()->comment('Multiple images')->after('cost_price');
        });
        
        // Migrate data back to array format
        $menuItems = DB::table('menu_items')->whereNotNull('image')->get();
        foreach ($menuItems as $item) {
            if ($item->image) {
                DB::table('menu_items')
                    ->where('id', $item->id)
                    ->update(['image_urls' => json_encode([$item->image])]);
            }
        }
        
        // Drop the new image column
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn('image');
        });
    }
};