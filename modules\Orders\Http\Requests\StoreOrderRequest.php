<?php

namespace Modules\Orders\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use App\Models\MenuItemAddon;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'customer_id' => 'nullable|exists:customers,id',
            'order_type' => 'required|in:dine_in,takeaway,delivery,online',
            'status' => 'nullable|in:pending,confirmed,preparing,ready,served,completed,cancelled',
            'notes' => 'nullable|string|max:500',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
            'items.*.variant_id' => 'nullable|exists:menu_item_variants,id',
            'items.*.addons' => 'nullable|array',
            'items.*.addons.*.addon_id' => 'required_with:items.*.addons|exists:menu_item_addons,id',
            'items.*.addons.*.quantity' => 'required_with:items.*.addons|integer|min:1',
            'items.*.addons.*.unit_price' => 'required_with:items.*.addons|numeric|min:0',
            'items.*.addons.*.total_price' => 'required_with:items.*.addons|numeric|min:0',
        ];

        // Order type specific validation rules
        if ($this->input('order_type') === 'dine_in') {
            $rules['table_id'] = 'nullable|exists:tables,id'; // Table is now optional
            $rules['pax'] = 'nullable|integer|min:1|max:20'; // Number of people is optional
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'nullable|exists:customers,id'; // Optional for dine-in
        } elseif ($this->input('order_type') === 'delivery') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'required|exists:users,id';
            $rules['customer_id'] = 'required|exists:customers,id'; // Required for delivery
            $rules['delivery_address'] = 'required|string|max:500';
            $rules['delivery_coordinates'] = 'nullable|array';
            $rules['delivery_coordinates.lat'] = 'required_with:delivery_coordinates|numeric';
            $rules['delivery_coordinates.lng'] = 'required_with:delivery_coordinates|numeric';
        } elseif ($this->input('order_type') === 'takeaway') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'nullable|exists:customers,id'; // Optional for takeaway
        } else { // online
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'required|exists:customers,id'; // Required for online orders
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateAddonsForMenuItems($validator);
        });
    }

    /**
     * Validate that addons belong to their respective menu items.
     */
    protected function validateAddonsForMenuItems(Validator $validator): void
    {
        $items = $this->input('items', []);
        
        foreach ($items as $itemIndex => $item) {
            if (!isset($item['addons']) || !is_array($item['addons'])) {
                continue;
            }

            $menuItemId = $item['menu_item_id'] ?? null;
            
            foreach ($item['addons'] as $addonIndex => $addon) {
                $addonId = $addon['addon_id'] ?? null;
                
                if ($addonId && $menuItemId) {
                    // Check if addon belongs to the menu item
                    $addonExists = MenuItemAddon::where('id', $addonId)
                        ->where('menu_item_id', $menuItemId)
                        ->exists();
                    
                    if (!$addonExists) {
                        $validator->errors()->add(
                            "items.{$itemIndex}.addons.{$addonIndex}.addon_id",
                            "The selected addon does not belong to the specified menu item."
                        );
                    }
                }
            }
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'table_id.exists' => 'Selected table does not exist.',
            'pax.min' => 'Number of people must be at least 1.',
            'pax.max' => 'Number of people cannot exceed 20.',
            'customer_id.required' => 'Customer is required for this order type.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'delivery_man_id.required' => 'Delivery person is required for delivery orders.',
            'delivery_man_id.exists' => 'Selected delivery person does not exist.',
            'delivery_address.required' => 'Delivery address is required for delivery orders.',
            'order_type.required' => 'Order type is required.',
            'order_type.in' => 'Invalid order type selected.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.*.menu_item_id.required' => 'Menu item is required for each order item.',
            'items.*.menu_item_id.exists' => 'Selected menu item does not exist.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.unit_price.required' => 'Unit price is required for each item.',
            'items.*.unit_price.min' => 'Unit price must be greater than or equal to 0.',
            'items.*.addons.*.addon_id.exists' => 'The selected addon does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'customer_id' => 'customer',
            'table_id' => 'table',
            'pax' => 'number of people',
            'order_type' => 'order type',
            'delivery_man_id' => 'delivery person',
            'delivery_address' => 'delivery address',
            'items.*.menu_item_id' => 'menu item',
            'items.*.quantity' => 'quantity',
            'items.*.unit_price' => 'unit price',
        ];
    }
}