<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Reservation\Services\ReservationService;
use Modules\Reservation\Http\Requests\CreateReservationRequest;
use Modules\Reservation\Http\Requests\UpdateReservationRequest;
use Modules\Reservation\Http\Resources\ReservationResource;
use Modules\Reservation\Http\Resources\ReservationCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class ReservationController extends Controller
{
    protected ReservationService $reservationService;

    public function __construct(ReservationService $reservationService)
    {
        $this->reservationService = $reservationService;
    }

    /**
     * Display a listing of reservations.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $filters = $request->only([
                'status',
                'date_from',
                'date_to',
                'customer_phone',
                'customer_name',
                'table_id',
                'area_id'
            ]);
            
            // Add branch_id from authenticated user
            $filters['branch_id'] = $user->branch_id;

            $perPage = $request->get('per_page', 15);
            $reservations = $this->reservationService->getAllReservations($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new ReservationCollection($reservations),
                'message' => 'Reservations retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reservations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created reservation.
     */
    public function store(CreateReservationRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id || !$user->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch or tenant'
                ], 400);
            }

            $data = $request->validated();
            $data['tenant_id'] = $user->tenant_id;
            $data['branch_id'] = $user->branch_id;
            
            $reservation = $this->reservationService->createReservation($data);

            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation created successfully'
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display the specified reservation.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $reservation = $this->reservationService->getReservationById($id);
            
            if (!$reservation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Reservation not found'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving reservation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified reservation.
     */
    public function update(UpdateReservationRequest $request, int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;

            $reservation = $this->reservationService->updateReservation($id, $data);
            
            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating reservation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified reservation.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $reservation = $this->reservationService->cancelReservation($id);
            
            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling reservation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm a reservation.
     */
    public function confirm(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = $this->reservationService->confirmReservation($id);

            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation confirmed successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Mark reservation as seated.
     */
    public function seat(Request $request, int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $tableId = $request->get('table_id');
            $reservation = $this->reservationService->seatReservation($id, $tableId);

            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation marked as seated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to seat reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Complete a reservation.
     */
    public function complete(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = $this->reservationService->completeReservation($id);

            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation completed successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete reservation',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Mark reservation as no-show.
     */
    public function noShow(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $reservation = $this->reservationService->markAsNoShow($id);

            return response()->json([
                'success' => true,
                'data' => new ReservationResource($reservation),
                'message' => 'Reservation marked as no-show successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark reservation as no-show',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Check table availability.
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $request->validate([
                'datetime' => 'required|date',
                'party_size' => 'required|integer|min:1',
                'duration' => 'integer|min:30|max:480'
            ]);

            $availableTables = $this->reservationService->checkAvailability(
                $user->branch_id,
                $request->get('datetime'),
                $request->get('duration', 120),
                $request->get('party_size')
            );

            return response()->json([
                'success' => true,
                'data' => $availableTables,
                'message' => 'Availability checked successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check availability',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get reservation statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $stats = $this->reservationService->getReservationStats(
                $user->branch_id,
                $request->get('date_from'),
                $request->get('date_to')
            );

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}