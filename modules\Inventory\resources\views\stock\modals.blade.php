<!-- Update Stock Modal -->
<div class="modal fade" id="updateStockModal" tabindex="-1" role="dialog" aria-labelledby="updateStockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStockModalLabel">تحديث المخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="updateStockForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="stock_type">نوع العملية <span class="text-danger">*</span></label>
                        <select class="form-control" id="stock_type" name="type" required>
                            <option value="">اختر نوع العملية</option>
                            <option value="add">إضافة مخزون</option>
                            <option value="subtract">خصم مخزون</option>
                            <option value="set">تعيين مخزون</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_quantity">الكمية <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="stock_quantity" name="quantity" min="0" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_reason">السبب</label>
                        <select class="form-control" id="stock_reason" name="reason">
                            <option value="">اختر السبب</option>
                            <option value="purchase">شراء</option>
                            <option value="sale">بيع</option>
                            <option value="adjustment">تسوية</option>
                            <option value="damage">تلف</option>
                            <option value="expired">انتهاء صلاحية</option>
                            <option value="transfer">نقل</option>
                            <option value="count">جرد</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_notes">ملاحظات</label>
                        <textarea class="form-control" id="stock_notes" name="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-cube-outline"></i> تحديث المخزون
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Adjust Stock Modal -->
<div class="modal fade" id="adjustStockModal" tabindex="-1" role="dialog" aria-labelledby="adjustStockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustStockModalLabel">تسوية المخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="adjustStockForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        تسوية المخزون تعني تعديل الكمية الحالية لتتطابق مع الجرد الفعلي
                    </div>
                    
                    <div class="form-group">
                        <label for="current_stock_display">المخزون الحالي في النظام</label>
                        <input type="text" class="form-control" id="current_stock_display" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="actual_stock">المخزون الفعلي (حسب الجرد) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="actual_stock" name="actual_stock" min="0" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="adjustment_reason">سبب التسوية <span class="text-danger">*</span></label>
                        <select class="form-control" id="adjustment_reason" name="reason" required>
                            <option value="">اختر السبب</option>
                            <option value="physical_count">جرد فعلي</option>
                            <option value="system_error">خطأ في النظام</option>
                            <option value="damage">تلف</option>
                            <option value="theft">سرقة</option>
                            <option value="expired">انتهاء صلاحية</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="adjustment_notes">ملاحظات التسوية</label>
                        <textarea class="form-control" id="adjustment_notes" name="notes" rows="3" placeholder="تفاصيل إضافية حول التسوية"></textarea>
                    </div>
                    
                    <div id="adjustment_preview" class="alert alert-warning" style="display: none;">
                        <strong>معاينة التسوية:</strong>
                        <div id="adjustment_details"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="mdi mdi-adjust"></i> تطبيق التسوية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Update Modal -->
<div class="modal fade" id="quickUpdateModal" tabindex="-1" role="dialog" aria-labelledby="quickUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickUpdateModalLabel">تحديث سريع للمخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="quickUpdateForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="quick_item_search">البحث عن المادة</label>
                        <select class="form-control select2" id="quick_item_search" name="item_id" style="width: 100%;">
                            <option value="">ابحث عن المادة...</option>
                        </select>
                    </div>
                    
                    <div id="quick_item_details" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>المخزون الحالي</label>
                                    <input type="text" class="form-control" id="quick_current_stock" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>الحد الأدنى</label>
                                    <input type="text" class="form-control" id="quick_minimum_stock" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quick_operation">العملية <span class="text-danger">*</span></label>
                                    <select class="form-control" id="quick_operation" name="operation" required>
                                        <option value="">اختر العملية</option>
                                        <option value="add">إضافة</option>
                                        <option value="subtract">خصم</option>
                                        <option value="set">تعيين</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quick_quantity">الكمية <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="quick_quantity" name="quantity" min="0" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="quick_reason">السبب</label>
                            <select class="form-control" id="quick_reason" name="reason">
                                <option value="">اختر السبب</option>
                                <option value="purchase">شراء</option>
                                <option value="sale">بيع</option>
                                <option value="adjustment">تسوية</option>
                                <option value="damage">تلف</option>
                                <option value="expired">انتهاء صلاحية</option>
                                <option value="transfer">نقل</option>
                                <option value="count">جرد</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="quick_update_btn" disabled>
                        <i class="mdi mdi-flash"></i> تحديث سريع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Stock Transfer Modal -->
<div class="modal fade" id="stockTransferModal" tabindex="-1" role="dialog" aria-labelledby="stockTransferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockTransferModalLabel">نقل المخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="stockTransferForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        نقل المخزون بين الفروع أو المواقع المختلفة
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_from">من الفرع <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="transfer_from" name="from_branch" required>
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_to">إلى الفرع <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="transfer_to" name="to_branch" required>
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="transfer_item">المادة <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="transfer_item" name="item_id" required>
                            <option value="">اختر المادة</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>المخزون المتاح</label>
                                <input type="text" class="form-control" id="transfer_available_stock" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_quantity">الكمية المنقولة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="transfer_quantity" name="quantity" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="transfer_notes">ملاحظات النقل</label>
                        <textarea class="form-control" id="transfer_notes" name="notes" rows="3" placeholder="تفاصيل إضافية حول عملية النقل"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="mdi mdi-swap-horizontal"></i> تنفيذ النقل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Stock Count Modal -->
<div class="modal fade" id="stockCountModal" tabindex="-1" role="dialog" aria-labelledby="stockCountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockCountModalLabel">جرد المخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="stockCountForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="mdi mdi-alert"></i>
                        جرد المخزون سيقوم بإنشاء تقرير مقارنة بين المخزون في النظام والمخزون الفعلي
                    </div>
                    
                    <div class="form-group">
                        <label for="count_type">نوع الجرد <span class="text-danger">*</span></label>
                        <select class="form-control" id="count_type" name="type" required>
                            <option value="">اختر نوع الجرد</option>
                            <option value="full">جرد شامل</option>
                            <option value="partial">جرد جزئي</option>
                            <option value="category">جرد حسب الفئة</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="category_selection" style="display: none;">
                        <label for="count_category">الفئة</label>
                        <select class="form-control select2" id="count_category" name="category">
                            <option value="">اختر الفئة</option>
                            <option value="ingredients">المكونات</option>
                            <option value="beverages">المشروبات</option>
                            <option value="packaging">التعبئة والتغليف</option>
                            <option value="cleaning">مواد التنظيف</option>
                            <option value="equipment">المعدات</option>
                            <option value="general">عام</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="items_selection" style="display: none;">
                        <label for="count_items">المواد المحددة</label>
                        <select class="form-control select2" id="count_items" name="items[]" multiple>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="count_notes">ملاحظات الجرد</label>
                        <textarea class="form-control" id="count_notes" name="notes" rows="3" placeholder="ملاحظات حول عملية الجرد"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="mdi mdi-counter"></i> بدء الجرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1" role="dialog" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkUpdateModalLabel">تحديث مجمع للمخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="mdi mdi-information"></i>
                    يمكنك تحديث مخزون عدة مواد في نفس الوقت من خلال رفع ملف Excel أو إدخال البيانات يدوياً
                </div>
                
                <ul class="nav nav-tabs" id="bulkUpdateTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="manual-tab" data-toggle="tab" href="#manual" role="tab">إدخال يدوي</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="upload-tab" data-toggle="tab" href="#upload" role="tab">رفع ملف</a>
                    </li>
                </ul>
                
                <div class="tab-content" id="bulkUpdateTabContent">
                    <div class="tab-pane fade show active" id="manual" role="tabpanel">
                        <form id="bulkUpdateManualForm">
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-success" onclick="addBulkUpdateRow()">
                                    <i class="mdi mdi-plus"></i> إضافة مادة
                                </button>
                            </div>
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered" id="bulkUpdateTable">
                                    <thead>
                                        <tr>
                                            <th>المادة</th>
                                            <th>العملية</th>
                                            <th>الكمية</th>
                                            <th>السبب</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    </div>
                    
                    <div class="tab-pane fade" id="upload" role="tabpanel">
                        <form id="bulkUpdateUploadForm" enctype="multipart/form-data">
                            <div class="mt-3">
                                <div class="form-group">
                                    <label for="bulk_file">اختر ملف Excel <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control-file" id="bulk_file" name="file" accept=".xlsx,.xls" required>
                                    <small class="form-text text-muted">الملفات المدعومة: Excel (.xlsx, .xls)</small>
                                </div>
                                
                                <div class="form-group">
                                    <a href="#" class="btn btn-sm btn-outline-info" onclick="downloadBulkTemplate()">
                                        <i class="mdi mdi-download"></i> تحميل نموذج الملف
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="processBulkUpdate()">
                    <i class="mdi mdi-cube-outline"></i> تطبيق التحديثات
                </button>
            </div>
        </div>
    </div>
</div>
