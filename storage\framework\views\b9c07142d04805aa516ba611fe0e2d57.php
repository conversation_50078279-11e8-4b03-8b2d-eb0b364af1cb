<!-- Modern Header -->
<header class="top-header d-flex align-items-center justify-content-between px-4">
    <!-- Left Section: Mobile Menu Toggle & Breadcrumb -->
    <div class="d-flex align-items-center">
        <!-- Mobile Menu Toggle -->
        <button class="btn btn-link d-md-none me-3 p-0" 
                type="button" 
                id="sidebarToggle"
                aria-label="Toggle sidebar">
            <i class="fas fa-bars fs-5 text-dark"></i>
        </button>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('dashboard')); ?>" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                </li>
                <?php if(isset($breadcrumbs)): ?>
                    <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($loop->last): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($breadcrumb['title']); ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo e($breadcrumb['url']); ?>" class="text-decoration-none"><?php echo e($breadcrumb['title']); ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </ol>
        </nav>
    </div>

    <!-- Right Section: Search, Notifications, User Menu -->
    <div class="d-flex align-items-center">
        <!-- Search Bar -->
        <div class="me-3 d-none d-lg-block">
            <div class="input-group" style="width: 300px;">
                <input type="text" 
                       class="form-control border-0 bg-light" 
                       placeholder="البحث في النظام..." 
                       aria-label="Search">
                <button class="btn btn-light border-0" type="button">
                    <i class="fas fa-search text-muted"></i>
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="d-flex align-items-center me-3">
            <!-- New Order Button -->
            <button class="btn btn-primary btn-sm me-2 d-none d-md-inline-flex align-items-center">
                <i class="fas fa-plus me-1"></i>
                طلب جديد
            </button>
            
            <!-- Kitchen Status -->
            <div class="dropdown me-2">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center" 
                        type="button" 
                        id="kitchenStatusDropdown" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <i class="fas fa-fire me-1 text-warning"></i>
                    <span class="d-none d-md-inline">المطبخ</span>
                    <span class="badge bg-warning text-dark ms-1">5</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end shadow" style="width: 300px;">
                    <li class="dropdown-header">طلبات المطبخ النشطة</li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#">
                            <div>
                                <div class="fw-bold">طلب #1247</div>
                                <small class="text-muted">طاولة 5 - برجر دجاج</small>
                            </div>
                            <span class="badge bg-warning">قيد التحضير</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#">
                            <div>
                                <div class="fw-bold">طلب #1248</div>
                                <small class="text-muted">طاولة 3 - بيتزا مارجريتا</small>
                            </div>
                            <span class="badge bg-success">جاهز</span>
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-center" href="#">عرض جميع الطلبات</a></li>
                </ul>
            </div>
        </div>

        <!-- Notifications -->
        <div class="dropdown me-3">
            <button class="btn btn-link position-relative p-2" 
                    type="button" 
                    id="notificationsDropdown" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                <i class="fas fa-bell fs-5 text-dark"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    3
                    <span class="visually-hidden">unread notifications</span>
                </span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow" style="width: 350px;">
                <li class="dropdown-header d-flex justify-content-between align-items-center">
                    <span>الإشعارات</span>
                    <small><a href="#" class="text-decoration-none">تحديد الكل كمقروء</a></small>
                </li>
                <li><hr class="dropdown-divider"></li>
                
                <!-- Notification Items -->
                <li>
                    <a class="dropdown-item py-3 border-bottom" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-shopping-cart text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">طلب جديد</div>
                                <small class="text-muted">طلب #1252 من طاولة 8</small>
                                <div class="text-muted small">منذ 5 دقائق</div>
                            </div>
                        </div>
                    </a>
                </li>
                
                <li>
                    <a class="dropdown-item py-3 border-bottom" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-check text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">طلب مكتمل</div>
                                <small class="text-muted">تم تقديم طلب #1249</small>
                                <div class="text-muted small">منذ 10 دقائق</div>
                            </div>
                        </div>
                    </a>
                </li>
                
                <li>
                    <a class="dropdown-item py-3" href="#">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">تنبيه مخزون</div>
                                <small class="text-muted">مخزون الدجاج منخفض</small>
                                <div class="text-muted small">منذ 30 دقيقة</div>
                            </div>
                        </div>
                    </a>
                </li>
                
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
            </ul>
        </div>

        <!-- User Profile Dropdown -->
        <div class="dropdown">
            <button class="btn btn-link d-flex align-items-center p-0" 
                    type="button" 
                    id="userProfileDropdown" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                <div class="d-flex align-items-center">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-2">
                        <i class="fas fa-user text-primary"></i>
                    </div>
                    <div class="text-end d-none d-md-block">
                        <div class="fw-bold text-dark"><?php echo e(Auth::user()->name); ?></div>
                        <small class="text-muted"><?php echo e(Auth::user()->roles->first()->name ?? 'مستخدم'); ?></small>
                    </div>
                    <i class="fas fa-chevron-down ms-2 text-muted"></i>
                </div>
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow">
                <li class="dropdown-header">
                    <div class="text-center">
                        <div class="fw-bold"><?php echo e(Auth::user()->name); ?></div>
                        <small class="text-muted"><?php echo e(Auth::user()->email); ?></small>
                    </div>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item d-flex align-items-center" href="#">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a>
                </li>
                <li>
                    <a class="dropdown-item d-flex align-items-center" href="#">
                        <i class="fas fa-cog me-2"></i>الإعدادات
                    </a>
                </li>
                <li>
                    <a class="dropdown-item d-flex align-items-center" href="#">
                        <i class="fas fa-question-circle me-2"></i>المساعدة
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="dropdown-item text-danger d-flex align-items-center">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</header>

<!-- Mobile Search Bar (Hidden by default) -->
<div class="d-lg-none bg-light border-bottom p-3" id="mobileSearch" style="display: none;">
    <div class="input-group">
        <input type="text" 
               class="form-control" 
               placeholder="البحث في النظام..." 
               aria-label="Search">
        <button class="btn btn-primary" type="button">
            <i class="fas fa-search"></i>
        </button>
    </div>
</div>

<script>
// Mobile sidebar toggle
document.getElementById('sidebarToggle')?.addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    sidebar.classList.toggle('show');
    overlay.style.display = sidebar.classList.contains('show') ? 'block' : 'none';
});

// Close sidebar when clicking overlay
document.getElementById('sidebarOverlay')?.addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.remove('show');
    this.style.display = 'none';
});
</script>
<?php /**PATH D:\my dehive work\test\epis - Copy\resources\views/layouts/modern-header.blade.php ENDPATH**/ ?>