# Customer Module

This module provides comprehensive customer management functionality for the restaurant POS system.

## Features

- **Customer Management**: Complete CRUD operations for customers
- **Branch-based Operations**: All customers are associated with specific branches
- **Loyalty Program Integration**: Add and redeem loyalty points, view loyalty history
- **Customer Search**: Find customers by contact information (phone, email, or name)
- **Customer Status Management**: Activate, deactivate, and update customer visit information

## API Endpoints

### Customer CRUD Operations

- `GET /api/customers?branch_id={id}` - Get customers by branch (paginated)
- `POST /api/customers` - Create a new customer
- `GET /api/customers/{id}` - Get specific customer details
- `PUT /api/customers/{id}` - Update customer information
- `DELETE /api/customers/{id}` - Delete customer (soft delete)

### Customer Search

- `GET /api/customers/search/customers?branch_id={id}&search={term}` - Search customers by phone, email, or name

### Customer Status Management

- `PATCH /api/customers/{id}/activate` - Activate a customer
- `PATCH /api/customers/{id}/deactivate` - Deactivate a customer
- `PATCH /api/customers/{id}/update-visit` - Update customer's last visit timestamp

### Loyalty Points Management

- `POST /api/customers/{id}/loyalty/add` - Add loyalty points to customer
- `POST /api/customers/{id}/loyalty/redeem` - Redeem loyalty points from customer
- `GET /api/customers/{id}/loyalty/history` - Get customer's loyalty transaction history

### Additional Endpoints

- `GET /api/customers/active/list?branch_id={id}` - Get all active customers for a branch

## Request/Response Examples

### Create Customer
```json
POST /api/customers
{
    "branch_id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "date_of_birth": "1990-01-01",
    "gender": "male",
    "address": "123 Main St",
    "city": "New York",
    "postal_code": "10001",
    "preferences": ["vegetarian"],
    "notes": "Regular customer",
    "is_active": true
}
```

### Add Loyalty Points
```json
POST /api/customers/{id}/loyalty/add
{
    "points": 100.50,
    "description": "Purchase reward"
}
```

### Search Customers
```json
GET /api/customers/search/customers?branch_id=1&search=john

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+**********",
            "loyalty_points": 150.75,
            "is_active": true,
            ...
        }
    ]
}
```

## Installation

The Customer module is automatically registered via the `CustomerServiceProvider`. All routes are prefixed with `/api/customers` and use the `api` middleware group.

## Dependencies

- Laravel 11+
- Customer model with SoftDeletes
- LoyaltyTransaction model
- Branch model
- Tenant model