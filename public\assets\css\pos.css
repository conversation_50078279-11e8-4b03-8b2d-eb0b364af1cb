/**
 * POS System Styles
 * Modern, responsive design for the Point of Sale system
 */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

.pos-container {
    min-height: calc(100vh - 180px);
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
    z-index: 1;
    margin: -20px -20px 0 -20px;
    padding: 0;
}

/* Loading Screen */
.pos-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #fff;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header Styles */
.pos-header {
    background: white;
    color: #333;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
    margin: 0;
    border-bottom: 1px solid #e0e0e0;
}

.pos-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1600px;
    margin: 0 auto;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.pos-title h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.pos-subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
}

.pos-status {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.connection-status, .sync-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.connection-status.online {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.connection-status.offline {
    background-color: rgba(244, 67, 54, 0.2);
    color: #F44336;
}

.sync-status {
    background-color: rgba(255, 193, 7, 0.2);
    color: #FFC107;
}

.spinning {
    animation: spin 1s linear infinite;
}

/* Offline Banner */
.offline-banner {
    background-color: #FF9800;
    color: white;
    padding: 0.75rem 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-weight: 500;
}

.offline-banner button {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.offline-banner button:hover {
    background: rgba(255,255,255,0.3);
}

/* Main Layout */
.pos-layout {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 1.5rem;
    padding: 1.5rem;
    max-width: none;
    margin: 0;
    min-height: calc(100vh - 200px);
}

/* Menu Section */
.pos-menu {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.menu-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.4rem;
}

.menu-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-container {
    position: relative;
}

.search-input {
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 20px;
    width: 250px;
    font-size: 0.9rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.view-toggle {
    background: #f5f5f5;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.view-toggle:hover {
    background: #e0e0e0;
}

/* Category Navigation */
.category-nav {
    display: flex;
    padding: 0 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    overflow-x: auto;
}

.category-tab {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
}

.category-tab:hover {
    color: #2196F3;
}

.category-tab.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
}

/* Menu Items Grid */
.menu-items {
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
    max-height: 600px;
    overflow-y: auto;
}

.menu-items.list-view {
    grid-template-columns: 1fr;
}

.menu-item-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    height: 100%;
}

.menu-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #2196F3;
}

.menu-item-card:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

.item-image {
    height: 120px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-image-placeholder {
    height: 120px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 1.5rem;
}

.item-content {
    padding: 0.75rem;
}

.item-name {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
}

.item-description {
    margin: 0 0 0.75rem 0;
    color: #666;
    font-size: 0.8rem;
    line-height: 1.3;
}

.item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2196F3;
}

.item-badges {
    display: flex;
    gap: 0.25rem;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-spicy {
    background: #ffebee;
    color: #f44336;
}

.badge-variants, .badge-addons {
    background: #e3f2fd;
    color: #2196f3;
}

/* Order Section */
.pos-order {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    height: fit-content;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.order-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.order-count {
    background: #2196F3;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Order Type Selection */
.order-type-section {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.order-type-section h3 {
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
    color: #333;
}

.order-type-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.4rem;
}

.order-type-btn {
    background: #f5f5f5;
    border: 2px solid transparent;
    padding: 0.5rem 0.25rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    font-size: 0.7rem;
    font-weight: 500;
}

.order-type-btn:hover {
    background: #e3f2fd;
}

.order-type-btn.active {
    background: #2196F3;
    color: white;
    border-color: #1976D2;
}

.order-type-btn i {
    font-size: 1rem;
}

/* Form Styles */
.order-form {
    padding: 0 1rem;
}

.form-group {
    margin-bottom: 0.75rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 500;
    color: #333;
    font-size: 0.85rem;
}

.form-control {
    width: 100%;
    padding: 0.6rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.conditional-field {
    display: none;
}

.conditional-field.show {
    display: block;
}

/* Order Items */
.order-items-section {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.order-items-section h3 {
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
    color: #333;
}

.empty-order {
    text-align: center;
    padding: 1.5rem 0.5rem;
    color: #666;
}

.empty-order i {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    color: #ddd;
}

.empty-order p {
    margin: 0 0 0.25rem 0;
    font-weight: 500;
    font-size: 0.9rem;
}

.empty-order small {
    color: #999;
    font-size: 0.8rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.order-item-content {
    flex: 1;
}

.order-item-name {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: #333;
}

.item-variants, .item-addons {
    display: block;
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.order-item-price {
    font-weight: 700;
    color: #2196F3;
}

.order-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qty-btn, .remove-btn {
    background: #f5f5f5;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.qty-btn:hover {
    background: #e0e0e0;
}

.remove-btn {
    background: #ffebee;
    color: #f44336;
}

.remove-btn:hover {
    background: #ffcdd2;
}

.qty-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

/* Admin Template Integration */
.main-content .pos-container {
    margin: -20px -20px 0 -20px;
}

@media (min-width: 992px) {
    .main-content .pos-container {
        margin: -20px -20px 0 -20px;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .pos-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .pos-order {
        order: -1;
        max-height: none;
    }

    .menu-items {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }
}

@media (max-width: 768px) {
    .pos-header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .menu-controls {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-input {
        width: 200px;
    }
    
    .order-type-buttons {
        grid-template-columns: 1fr;
    }
    
    .menu-items {
        grid-template-columns: 1fr;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
}

.toast.hide {
    transform: translateX(100%);
}

.toast-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 0.75rem;
}

.toast-success {
    border-left: 4px solid #4CAF50;
}

.toast-error {
    border-left: 4px solid #F44336;
}

.toast-warning {
    border-left: 4px solid #FF9800;
}

.toast-info {
    border-left: 4px solid #2196F3;
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 0.25rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.loading-text {
    margin-top: 1rem;
    font-weight: 500;
}

body.loading-active {
    overflow: hidden;
}

/* Discount Section */
.discount-section {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.discount-section h3 {
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
    color: #333;
}

.discount-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.4rem;
}

/* Order Total */
.order-total {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.total-line:last-child {
    margin-bottom: 0;
}

.total-final {
    font-size: 1rem;
    font-weight: 700;
    color: #333;
    padding-top: 0.5rem;
    border-top: 2px solid #e0e0e0;
    margin-top: 0.5rem;
}

/* Action Buttons */
.order-actions {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-submit, .btn-print {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
}

.btn-submit {
    background: #4CAF50;
    color: white;
}

.btn-submit:hover:not(:disabled) {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-print {
    background: #2196F3;
    color: white;
}

.btn-print:hover:not(:disabled) {
    background: #1976D2;
    transform: translateY(-1px);
}

.btn-submit:disabled, .btn-print:disabled {
    background: #e0e0e0;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

/* Item Modal */
.modal-lg {
    max-width: 600px;
}

.item-modal-content {
    padding: 1rem 0;
}

.item-info {
    margin-bottom: 2rem;
    text-align: center;
}

.item-info h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.item-description {
    color: #666;
    margin-bottom: 1rem;
}

.item-base-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2196F3;
}

.variants-section, .addons-section, .quantity-section {
    margin-bottom: 2rem;
}

.variants-section h4, .addons-section h4, .quantity-section h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
}

.variants-list, .addons-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.variant-option, .addon-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.variant-option:hover, .addon-option:hover {
    background: #f5f5f5;
    border-color: #2196F3;
}

.variant-option input, .addon-option input {
    margin-right: 0.75rem;
}

.variant-name, .addon-name {
    flex: 1;
    font-weight: 500;
}

.variant-price, .addon-price {
    color: #2196F3;
    font-weight: 600;
}

.quantity-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.quantity-controls .qty-btn {
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.quantity-controls .qty-btn:hover {
    background: #e0e0e0;
}

.quantity-controls input {
    width: 80px;
    text-align: center;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-total {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 1rem;
}

.modal-total strong {
    font-size: 1.3rem;
    color: #333;
}

/* No Items Message */
.no-items {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.no-items i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ddd;
}

.no-items p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .menu-item-card {
        border-width: 2px;
    }

    .menu-item-card:hover {
        border-width: 3px;
    }

    .btn-submit, .btn-print {
        border: 2px solid transparent;
    }

    .btn-submit:focus, .btn-print:focus {
        border-color: #fff;
    }
}

/* Print Styles */
@media print {
    .pos-header, .pos-actions, .offline-banner {
        display: none !important;
    }

    .pos-layout {
        grid-template-columns: 1fr;
        padding: 0;
    }

    .pos-order {
        box-shadow: none;
        border: 1px solid #000;
    }
}
