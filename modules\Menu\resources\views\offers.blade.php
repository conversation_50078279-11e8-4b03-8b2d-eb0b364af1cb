@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة العروض</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ العروض</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-offer-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة العروض</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع عروض المطعم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="offers-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">الاسم</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">نوع الخصم</th>
                                <th class="border-bottom-0">قيمة الخصم</th>
                                <th class="border-bottom-0">كود الخصم</th>
                                <th class="border-bottom-0">تاريخ البداية</th>
                                <th class="border-bottom-0">تاريخ النهاية</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Offer Modal -->
<div class="modal fade" id="offerModal" tabindex="-1" role="dialog" aria-labelledby="offerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="offerModalLabel">إضافة عرض جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="offerForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="offer_id" name="offer_id">
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم العرض <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name_en">اسم العرض (إنجليزي)</label>
                                <input type="text" class="form-control" id="name_en" name="name_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Offer Type & Discount -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">نوع العرض والخصم</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="offer_type">نوع العرض <span class="text-danger">*</span></label>
                                <select class="form-control" id="offer_type" name="offer_type" required>
                                    <option value="">اختر نوع العرض</option>
                                    <option value="discount">خصم</option>
                                    <option value="buy_x_get_y">اشتري X واحصل على Y</option>
                                    <option value="combo">كومبو</option>
                                    <option value="free_delivery">توصيل مجاني</option>
                                    <option value="cashback">استرداد نقدي</option>
                                    <option value="free_item">عنصر مجاني</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="discount_type">نوع الخصم <span class="text-danger">*</span></label>
                                <select class="form-control" id="discount_type" name="discount_type" required>
                                    <option value="">اختر نوع الخصم</option>
                                    <option value="percentage">نسبة مئوية</option>
                                    <option value="fixed">مبلغ ثابت</option>
                                    <option value="free">مجاني</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="discount_value">قيمة الخصم</label>
                                <input type="number" class="form-control" id="discount_value" name="discount_value" step="0.01" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Promo Code -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="promo_code">كود الخصم</label>
                                <input type="text" class="form-control" id="promo_code" name="promo_code">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_discount_amount">الحد الأقصى لمبلغ الخصم</label>
                                <input type="number" class="form-control" id="max_discount_amount" name="max_discount_amount" step="0.01" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Conditions -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الشروط</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="minimum_order_amount">الحد الأدنى لمبلغ الطلب</label>
                                <input type="number" class="form-control" id="minimum_order_amount" name="minimum_order_amount" step="0.01" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="maximum_order_amount">الحد الأقصى لمبلغ الطلب</label>
                                <input type="number" class="form-control" id="maximum_order_amount" name="maximum_order_amount" step="0.01" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="minimum_items">الحد الأدنى لعدد العناصر</label>
                                <input type="number" class="form-control" id="minimum_items" name="minimum_items" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Buy X Get Y Settings -->
                    <div class="row" id="buy_x_get_y_section" style="display: none;">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">إعدادات اشتري X واحصل على Y</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="buy_quantity">كمية الشراء</label>
                                <input type="number" class="form-control" id="buy_quantity" name="buy_quantity" min="1">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="get_quantity">كمية الحصول</label>
                                <input type="number" class="form-control" id="get_quantity" name="get_quantity" min="1">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Scheduling -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الجدولة</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_time">وقت البداية</label>
                                <input type="time" class="form-control" id="start_time" name="start_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_time">وقت النهاية</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Usage Limits -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">حدود الاستخدام</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="usage_limit_total">الحد الأقصى للاستخدام الإجمالي</label>
                                <input type="number" class="form-control" id="usage_limit_total" name="usage_limit_total" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="usage_limit_per_customer">الحد الأقصى للاستخدام لكل عميل</label>
                                <input type="number" class="form-control" id="usage_limit_per_customer" name="usage_limit_per_customer" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="priority">الأولوية</label>
                                <input type="number" class="form-control" id="priority" name="priority" min="0" max="10" value="5">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الإعدادات</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_public" name="is_public" value="1" checked>
                                    <label class="form-check-label" for="is_public">
                                        عام
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_apply" name="auto_apply" value="1">
                                    <label class="form-check-label" for="auto_apply">
                                        تطبيق تلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_stackable" name="is_stackable" value="1">
                                    <label class="form-check-label" for="is_stackable">
                                        قابل للتراكم
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requires_promo_code" name="requires_promo_code" value="1">
                                    <label class="form-check-label" for="requires_promo_code">
                                        يتطلب كود خصم
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="first_order_only" name="first_order_only" value="1">
                                    <label class="form-check-label" for="first_order_only">
                                        للطلب الأول فقط
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="exclude_sale_items" name="exclude_sale_items" value="1">
                                    <label class="form-check-label" for="exclude_sale_items">
                                        استبعاد العناصر المخفضة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-offer-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Offer Modal -->
<div class="modal fade" id="showOfferModal" tabindex="-1" role="dialog" aria-labelledby="showOfferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showOfferModalLabel">تفاصيل العرض</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم العرض:</strong></label>
                            <p id="show_name"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>نوع العرض:</strong></label>
                            <p id="show_offer_type"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>نوع الخصم:</strong></label>
                            <p id="show_discount_type"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>قيمة الخصم:</strong></label>
                            <p id="show_discount_value"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>كود الخصم:</strong></label>
                            <p id="show_promo_code"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحد الأدنى لمبلغ الطلب:</strong></label>
                            <p id="show_minimum_order_amount"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label><strong>الوصف:</strong></label>
                            <p id="show_description"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ البداية:</strong></label>
                            <p id="show_start_date"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ النهاية:</strong></label>
                            <p id="show_end_date"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مميز:</strong></label>
                            <p id="show_featured"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>عدد مرات الاستخدام:</strong></label>
                            <p id="show_usage_count"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحد الأقصى للاستخدام:</strong></label>
                            <p id="show_usage_limit"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.flash.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var offersTable = $('#offers-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("api.menu.offers.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            {data: 'id', name: 'id'},
            {data: 'name', name: 'name'},
            {data: 'offer_type', name: 'offer_type'},
            {data: 'discount_type', name: 'discount_type'},
            {data: 'discount_value', name: 'discount_value'},
            {data: 'promo_code', name: 'promo_code'},
            {data: 'start_date', name: 'start_date'},
            {data: 'end_date', name: 'end_date'},
            {data: 'is_active', name: 'is_active', render: function(data) {
                return data ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
            }},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Show/Hide Buy X Get Y section based on offer type
    $('#offer_type').change(function() {
        if ($(this).val() === 'buy_x_get_y') {
            $('#buy_x_get_y_section').show();
        } else {
            $('#buy_x_get_y_section').hide();
        }
    });

    // Add Offer Button
    $('#add-offer-btn').click(function() {
        $('#offerForm')[0].reset();
        $('#offer_id').val('');
        $('#buy_x_get_y_section').hide();
        $('#offerModalLabel').text('إضافة عرض جديد');
        $('#offerModal').modal('show');
    });

    // Submit Offer Form
    $('#offerForm').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var offerId = $('#offer_id').val();
        var url = offerId ? '{{ route("api.menu.offers.update", ":id") }}'.replace(':id', offerId) : '{{ route("api.menu.offers.store") }}';
        var method = offerId ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#offerModal').modal('hide');
                    offersTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $('#' + key).addClass('is-invalid');
                        $('#' + key).siblings('.invalid-feedback').text(value[0]);
                    });
                } else {
                    swal('خطأ!', 'حدث خطأ أثناء حفظ العرض', 'error');
                }
            }
        });
    });

    // Edit Offer
    $(document).on('click', '.edit-offer', function() {
        var offerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.offers.show", ":id") }}'.replace(':id', offerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var offer = response.data;
                    $('#offer_id').val(offer.id);
                    $('#name').val(offer.name);
                    $('#name_en').val(offer.name_en);
                    $('#description').val(offer.description);
                    $('#offer_type').val(offer.offer_type);
                    $('#discount_type').val(offer.discount_type);
                    $('#discount_value').val(offer.discount_value);
                    $('#promo_code').val(offer.promo_code);
                    $('#max_discount_amount').val(offer.max_discount_amount);
                    $('#minimum_order_amount').val(offer.minimum_order_amount);
                    $('#maximum_order_amount').val(offer.maximum_order_amount);
                    $('#minimum_items').val(offer.minimum_items);
                    $('#buy_quantity').val(offer.buy_quantity);
                    $('#get_quantity').val(offer.get_quantity);
                    $('#start_date').val(offer.start_date);
                    $('#start_time').val(offer.start_time);
                    $('#end_date').val(offer.end_date);
                    $('#end_time').val(offer.end_time);
                    $('#usage_limit_total').val(offer.usage_limit_total);
                    $('#usage_limit_per_customer').val(offer.usage_limit_per_customer);
                    $('#priority').val(offer.priority);
                    $('#is_active').prop('checked', offer.is_active);
                    $('#is_featured').prop('checked', offer.is_featured);
                    $('#is_public').prop('checked', offer.is_public);
                    $('#auto_apply').prop('checked', offer.auto_apply);
                    $('#is_stackable').prop('checked', offer.is_stackable);
                    $('#requires_promo_code').prop('checked', offer.requires_promo_code);
                    $('#first_order_only').prop('checked', offer.first_order_only);
                    $('#exclude_sale_items').prop('checked', offer.exclude_sale_items);
                    
                    // Show/Hide Buy X Get Y section
                    if (offer.offer_type === 'buy_x_get_y') {
                        $('#buy_x_get_y_section').show();
                    } else {
                        $('#buy_x_get_y_section').hide();
                    }
                    
                    $('#offerModalLabel').text('تعديل العرض');
                    $('#offerModal').modal('show');
                }
            }
        });
    });

    // Show Offer
    $(document).on('click', '.show-offer', function() {
        var offerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.offers.show", ":id") }}'.replace(':id', offerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var offer = response.data;
                    $('#show_name').text(offer.name);
                    $('#show_offer_type').text(offer.offer_type);
                    $('#show_discount_type').text(offer.discount_type);
                    $('#show_discount_value').text(offer.discount_value || 'غير محدد');
                    $('#show_promo_code').text(offer.promo_code || 'غير محدد');
                    $('#show_minimum_order_amount').text(offer.minimum_order_amount || 'غير محدد');
                    $('#show_description').text(offer.description || 'غير محدد');
                    $('#show_start_date').text(offer.start_date + ' ' + (offer.start_time || ''));
                    $('#show_end_date').text(offer.end_date + ' ' + (offer.end_time || ''));
                    $('#show_status').html(offer.is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                    $('#show_featured').html(offer.is_featured ? '<span class="badge badge-info">مميز</span>' : '<span class="badge badge-secondary">عادي</span>');
                    $('#show_usage_count').text(offer.usage_count || 0);
                    $('#show_usage_limit').text(offer.usage_limit_total || 'غير محدود');
                    
                    $('#showOfferModal').modal('show');
                }
            }
        });
    });

    // Delete Offer
    $(document).on('click', '.delete-offer', function() {
        var offerId = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("api.menu.offers.destroy", ":id") }}'.replace(':id', offerId),
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        offersTable.ajax.reload();
                        swal('تم الحذف!', response.message, 'success');
                    }
                },
                error: function() {
                    swal('خطأ!', 'حدث خطأ أثناء حذف العرض', 'error');
                }
            });
        });
    });

    // Toggle Offer Status
    $(document).on('click', '.toggle-status', function() {
        var offerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.offers.toggle-status", ":id") }}'.replace(':id', offerId),
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    offersTable.ajax.reload();
                    swal('نجح!', response.message, 'success');
                }
            },
            error: function() {
                swal('خطأ!', 'حدث خطأ أثناء تغيير حالة العرض', 'error');
            }
        });
    });
});
</script>
@endsection