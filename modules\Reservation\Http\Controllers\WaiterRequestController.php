<?php

namespace Modules\Reservation\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\WaiterRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Reservation\Http\Requests\CreateWaiterRequestRequest;
use Modules\Reservation\Http\Requests\UpdateWaiterRequestRequest;
use Modules\Reservation\Http\Resources\WaiterRequestResource;
use Modules\Reservation\Http\Resources\WaiterRequestCollection;
use Modules\Reservation\Services\WaiterRequestService;

class WaiterRequestController extends Controller
{
    protected $waiterRequestService;

    public function __construct(WaiterRequestService $waiterRequestService)
    {
        $this->waiterRequestService = $waiterRequestService;
    }

    /**
     * Display a listing of waiter requests.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['table_id', 'status', 'waiter_id', 'branch_id']);
            
            // If no branch_id is provided, use the authenticated user's branch
            if (!isset($filters['branch_id']) && auth()->user()) {
                $filters['branch_id'] = auth()->user()->branch_id;
            }
            
            $waiterRequests = $this->waiterRequestService->getAllRequests($filters);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestCollection($waiterRequests),
                'message' => 'Waiter requests retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve waiter requests',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created waiter request.
     */
    public function store(CreateWaiterRequestRequest $request): JsonResponse
    {
        try {
            $waiterRequest = $this->waiterRequestService->createRequest($request->validated());

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestResource($waiterRequest),
                'message' => 'Waiter request created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create waiter request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified waiter request.
     */
    public function show($id): JsonResponse
    {
        try {
            $waiterRequest = $this->waiterRequestService->getRequestById($id);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestResource($waiterRequest),
                'message' => 'Waiter request retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Waiter request not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified waiter request.
     */
    public function update(UpdateWaiterRequestRequest $request, $id): JsonResponse
    {
        try {
            $waiterRequest = $this->waiterRequestService->updateRequest($id, $request->validated());

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestResource($waiterRequest),
                'message' => 'Waiter request updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update waiter request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified waiter request.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $this->waiterRequestService->deleteRequest($id);

            return response()->json([
                'success' => true,
                'message' => 'Waiter request deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete waiter request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete a waiter request.
     */
    public function complete($id): JsonResponse
    {
        try {
            $waiterRequest = $this->waiterRequestService->completeRequest($id);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestResource($waiterRequest),
                'message' => 'Waiter request completed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete waiter request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a waiter request.
     */
    public function cancel($id): JsonResponse
    {
        try {
            $waiterRequest = $this->waiterRequestService->cancelRequest($id);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestResource($waiterRequest),
                'message' => 'Waiter request cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel waiter request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get requests for a specific table.
     */
    public function getTableRequests($tableId): JsonResponse
    {
        try {
            $waiterRequests = $this->waiterRequestService->getTableRequests($tableId);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestCollection($waiterRequests),
                'message' => 'Table requests retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table requests',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending requests for a specific waiter.
     */
    public function getWaiterRequests(Request $request): JsonResponse
    {
        try {
            $waiterId = $request->input('waiter_id', auth()->id());
            $waiterRequests = $this->waiterRequestService->getWaiterRequests($waiterId);

            return response()->json([
                'success' => true,
                'data' => new WaiterRequestCollection($waiterRequests),
                'message' => 'Waiter requests retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve waiter requests',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}