<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MenuItem extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_id',
        'category_id',
        'name',
        'code',
        'description',
        'short_description',
        'base_price',
        'cost_price',
        'image',
        'prep_time_minutes',
        'calories',
        'nutritional_info',
        'allergens',
        'dietary_info',
        'recipe_id',
        'barcode',
        'sku',
        'is_active',
        'is_featured',
        'is_spicy',
        'spice_level',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'base_price' => 'decimal:2',
            'cost_price' => 'decimal:2',
            'nutritional_info' => 'array',
            'allergens' => 'array',
            'dietary_info' => 'array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
        ];
    }

    // Relationships
    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    public function tenant()
    {
        return $this->menu->tenant();
    }

    public function branch()
    {
        return $this->menu->branch();
    }

    public function category()
    {
        return $this->belongsTo(MenuCategory::class);
    }

    public function recipe()
    {
        return $this->belongsTo(Recipe::class);
    }

    public function variants()
    {
        return $this->hasMany(MenuItemVariant::class);
    }

    public function addons()
    {
        return $this->hasMany(MenuItemAddon::class);
    }

    /**
     * Alias for addons relationship to match service usage.
     */
    public function modifiers()
    {
        return $this->addons();
    }

    public function branches()
    {
        return $this->hasMany(MenuItemBranch::class);
    }

    public function availability()
    {
        return $this->hasMany(MenuAvailability::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the kitchen menu item assignment for this menu item.
     */
    public function kitchenMenuItem()
    {
        return $this->hasOne(\Modules\Kitchen\Models\KitchenMenuItem::class);
    }

    /**
     * Get all kitchen assignments for this menu item (alias for kitchenMenuItem).
     */
    public function kitchenAssignments()
    {
        return $this->hasMany(\Modules\Kitchen\Models\KitchenMenuItem::class);
    }

    /**
     * Get the kitchen assigned to this menu item.
     */
    public function assignedKitchen()
    {
        return $this->hasOneThrough(
            \Modules\Kitchen\Models\Kitchen::class,
            \Modules\Kitchen\Models\KitchenMenuItem::class,
            'menu_item_id', // Foreign key on kitchen_menu_items table
            'id', // Foreign key on kitchens table
            'id', // Local key on menu_items table
            'kitchen_id' // Local key on kitchen_menu_items table
        );
    }

    /**
     * Get KOT order items for this menu item.
     */
    public function kotOrderItems()
    {
        return $this->hasMany(\Modules\Kitchen\Models\KotOrderItem::class);
    }

    /**
     * Check if this menu item is assigned to a kitchen.
     */
    public function isAssignedToKitchen(): bool
    {
        return $this->kitchenMenuItem()->exists();
    }

    /**
     * Get the image URL (for backward compatibility)
     */
    public function getImageUrlAttribute()
    {
        return $this->image;
    }

    /**
     * Get the first image (for backward compatibility)
     */
    public function getFirstImageAttribute()
    {
        return $this->image;
    }

    /**
     * Get the image with full URL path
     */
    public function getImageAttribute()
    {
        $imageValue = $this->attributes['image'] ?? null;
        
        if ($imageValue) {
            // If it's already a full URL, return as is
            if (filter_var($imageValue, FILTER_VALIDATE_URL)) {
                return $imageValue;
            }
            
            // Check if file exists in storage
            $storagePath = storage_path('app/public/' . $imageValue);
            if (file_exists($storagePath)) {
                return asset('storage/' . $imageValue);
            }
            
            // If file doesn't exist, try direct asset path
            $publicPath = public_path($imageValue);
            if (file_exists($publicPath)) {
                return asset($imageValue);
            }
        }
        
        // Return null if no valid image found
        return null;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->base_price, 2);
    }

    /**
     * Get formatted cost price
     */
    public function getFormattedCostPriceAttribute()
    {
        return $this->cost_price ? '$' . number_format($this->cost_price, 2) : null;
    }

    /**
     * Get the kitchen this menu item is assigned to for a specific tenant.
     */
    public function getAssignedKitchen($tenantId = null)
    {
        $query = $this->assignedKitchen();
        
        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }
        
        return $query->first();
    }

    /**
     * Get the price for a specific branch, fallback to base price if not set
     * This method is optimized to work with eager loaded branches to avoid N+1 queries
     */
    public function getBranchPrice($branchId)
    {
        // If branches are already loaded, use them to avoid additional queries
        if ($this->relationLoaded('branches')) {
            $branchItem = $this->branches->where('branch_id', $branchId)->first();
        } else {
            // Fallback to query if not eager loaded
            $branchItem = $this->branches()->where('branch_id', $branchId)->first();
        }

        if ($branchItem && $branchItem->branch_price !== null) {
            return $branchItem->branch_price;
        }

        return $this->base_price;
    }

    /**
     * Get the price attribute (for backward compatibility)
     */
    public function getPriceAttribute()
    {
        return $this->base_price;
    }
}