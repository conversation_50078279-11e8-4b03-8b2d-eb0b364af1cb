<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Offer Model
 * 
 * Manages promotional offers, discounts, BOGO deals, combo offers,
 * and various promotional campaigns for the restaurant.
 * 
 * @package App\Models
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class Offer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'code',
        'description',
        'short_description',
        'offer_type',
        'discount_type',
        'discount_value',
        'max_discount_amount',
        'min_order_amount',
        'buy_quantity',
        'get_quantity',
        'applicable_items',
        'excluded_items',
        'applicable_categories',
        'excluded_categories',
        'combo_items',
        'combo_price',
        'promo_code',
        'requires_promo_code',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'available_days',
        'customer_eligibility',
        'usage_limit_per_customer',
        'total_usage_limit',
        'current_usage_count',
        'is_stackable',
        'stackable_with',
        'priority',
        'terms_conditions',
        'image_url',
        'banner_image',
        'notification_settings',
        'auto_apply',
        'auto_apply_conditions',
        'is_featured',
        'is_active',
        'is_public',
        'sort_order',
        'analytics_data',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'discount_value' => 'decimal:2',
            'max_discount_amount' => 'decimal:2',
            'min_order_amount' => 'decimal:2',
            'buy_quantity' => 'integer',
            'get_quantity' => 'integer',
            'applicable_items' => 'array',
            'excluded_items' => 'array',
            'applicable_categories' => 'array',
            'excluded_categories' => 'array',
            'combo_items' => 'array',
            'combo_price' => 'decimal:2',
            'requires_promo_code' => 'boolean',
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'available_days' => 'array',
            'customer_eligibility' => 'array',
            'usage_limit_per_customer' => 'integer',
            'total_usage_limit' => 'integer',
            'current_usage_count' => 'integer',
            'is_stackable' => 'boolean',
            'stackable_with' => 'array',
            'terms_conditions' => 'array',
            'notification_settings' => 'array',
            'auto_apply' => 'boolean',
            'auto_apply_conditions' => 'array',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'is_public' => 'boolean',
            'sort_order' => 'integer',
            'analytics_data' => 'array',
            'metadata' => 'array',
        ];
    }

    // Relationships

    /**
     * Get the tenant that owns the offer.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the branch that owns the offer.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes

    /**
     * Scope to get offers for a specific branch
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to get active offers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get public offers
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get featured offers
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get current offers (valid now)
     */
    public function scopeCurrent($query)
    {
        $now = now();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now)
                    ->where('is_active', true);
    }

    /**
     * Scope to get offers by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('offer_type', $type);
    }

    /**
     * Scope to get offers by promo code
     */
    public function scopeByPromoCode($query, $code)
    {
        return $query->where('promo_code', $code);
    }

    /**
     * Scope to order by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'high', 'medium', 'low')")
                    ->orderBy('sort_order', 'asc');
    }

    // Helper Methods

    /**
     * Check if offer is currently valid
     */
    public function isValid()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        // Check date range
        if ($now < $this->start_date || $now > $this->end_date) {
            return false;
        }

        // Check time range if specified
        if ($this->start_time && $this->end_time) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->start_time->format('H:i') || 
                $currentTime > $this->end_time->format('H:i')) {
                return false;
            }
        }

        // Check day availability
        if ($this->available_days && !in_array($now->dayOfWeek, $this->available_days)) {
            return false;
        }

        // Check usage limits
        if ($this->total_usage_limit && $this->current_usage_count >= $this->total_usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Check if offer is applicable to given items
     */
    public function isApplicableToItems($itemIds)
    {
        // If no specific items defined, applies to all
        if (empty($this->applicable_items)) {
            // Check if any items are excluded
            if (!empty($this->excluded_items)) {
                return !array_intersect($itemIds, $this->excluded_items);
            }
            return true;
        }

        // Check if any of the items are in applicable list
        $hasApplicableItems = array_intersect($itemIds, $this->applicable_items);
        
        // Check if any items are excluded
        if (!empty($this->excluded_items)) {
            $hasExcludedItems = array_intersect($itemIds, $this->excluded_items);
            return $hasApplicableItems && !$hasExcludedItems;
        }

        return (bool) $hasApplicableItems;
    }

    /**
     * Check if offer is applicable to given categories
     */
    public function isApplicableToCategories($categoryIds)
    {
        // If no specific categories defined, applies to all
        if (empty($this->applicable_categories)) {
            // Check if any categories are excluded
            if (!empty($this->excluded_categories)) {
                return !array_intersect($categoryIds, $this->excluded_categories);
            }
            return true;
        }

        // Check if any of the categories are in applicable list
        $hasApplicableCategories = array_intersect($categoryIds, $this->applicable_categories);
        
        // Check if any categories are excluded
        if (!empty($this->excluded_categories)) {
            $hasExcludedCategories = array_intersect($categoryIds, $this->excluded_categories);
            return $hasApplicableCategories && !$hasExcludedCategories;
        }

        return (bool) $hasApplicableCategories;
    }

    /**
     * Calculate discount amount for given order total
     */
    public function calculateDiscount($orderTotal, $applicableAmount = null)
    {
        if (!$this->isValid()) {
            return 0;
        }

        // Check minimum order amount
        if ($this->min_order_amount && $orderTotal < $this->min_order_amount) {
            return 0;
        }

        $baseAmount = $applicableAmount ?? $orderTotal;
        $discount = 0;

        switch ($this->discount_type) {
            case 'percentage':
                $discount = ($baseAmount * $this->discount_value) / 100;
                break;
                
            case 'fixed_amount':
                $discount = $this->discount_value;
                break;
                
            case 'buy_x_get_y':
                // This would need more complex logic based on item quantities
                // For now, return 0 and handle in service layer
                return 0;
        }

        // Apply maximum discount limit
        if ($this->max_discount_amount && $discount > $this->max_discount_amount) {
            $discount = $this->max_discount_amount;
        }

        return round($discount, 2);
    }

    /**
     * Check if customer is eligible for this offer
     */
    public function isCustomerEligible($customer = null)
    {
        if (empty($this->customer_eligibility)) {
            return true;
        }

        if (!$customer) {
            return in_array('guest', $this->customer_eligibility);
        }

        // Add logic based on customer type, loyalty status, etc.
        return true; // Simplified for now
    }

    /**
     * Check if offer can be used by customer (usage limits)
     */
    public function canBeUsedByCustomer($customerId)
    {
        if (!$this->usage_limit_per_customer) {
            return true;
        }

        // This would need to check order history
        // For now, return true and implement in service layer
        return true;
    }

    /**
     * Record offer usage
     */
    public function recordUsage($customerId = null, $orderTotal = 0, $discountAmount = 0)
    {
        $this->increment('current_usage_count');
        
        // Update analytics data
        $analytics = $this->analytics_data ?? [];
        $today = now()->format('Y-m-d');
        
        if (!isset($analytics['daily_usage'])) {
            $analytics['daily_usage'] = [];
        }
        
        if (!isset($analytics['daily_usage'][$today])) {
            $analytics['daily_usage'][$today] = [
                'count' => 0,
                'total_discount' => 0,
                'total_order_value' => 0,
            ];
        }
        
        $analytics['daily_usage'][$today]['count']++;
        $analytics['daily_usage'][$today]['total_discount'] += $discountAmount;
        $analytics['daily_usage'][$today]['total_order_value'] += $orderTotal;
        
        $this->update(['analytics_data' => $analytics]);
    }

    /**
     * Get offer status
     */
    public function getStatus()
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        $now = now();
        
        if ($now < $this->start_date) {
            return 'scheduled';
        }
        
        if ($now > $this->end_date) {
            return 'expired';
        }
        
        if ($this->total_usage_limit && $this->current_usage_count >= $this->total_usage_limit) {
            return 'usage_limit_reached';
        }
        
        return 'active';
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats()
    {
        return [
            'current_usage' => $this->current_usage_count,
            'usage_limit' => $this->total_usage_limit,
            'usage_percentage' => $this->total_usage_limit ? 
                round(($this->current_usage_count / $this->total_usage_limit) * 100, 2) : 0,
            'remaining_uses' => $this->total_usage_limit ? 
                max(0, $this->total_usage_limit - $this->current_usage_count) : null,
        ];
    }
}